"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[792],{1616:(e,t,n)=>{n.d(t,{$5:()=>A,Ic:()=>D,N1:()=>_,Q3:()=>h,Qo:()=>x,RT:()=>S,S7:()=>g,V_:()=>T,X8:()=>f,ZL:()=>y,ZP:()=>E,eL:()=>k,gA:()=>O,mR:()=>b,vr:()=>C,xx:()=>w,zp:()=>P});var r=n(467),o=n(4756),a=n.n(o),i="ADD_COMPONENT",s="ADD_LAYOUT",c="FETCH_APP_DATA_SUCCESS",l="FETCH_APP_DATA_ERROR",u="UPDATE_COMPONENT",p="DELETE_COMPONENT",d="UPDATE_LAYOUT",m="DELETE_LAYOUT",h={ADD_COMPONENT:i,ADD_LAYOUT:s,ADD_STYLE:"ADD_STYLE",ADD_DATA:"ADD_DATA",FETCH_APP_DATA_SUCCESS:c,FETCH_APP_DATA_ERROR:l,WS_CONNECT:"WS_CONNECT",WS_CONNECTED:"WS_CONNECTED",WS_DISCONNECTED:"WS_DISCONNECTED",WS_MESSAGE_RECEIVED:"WS_MESSAGE_RECEIVED",UPDATE_COMPONENT:u,DELETE_COMPONENT:p,UPDATE_LAYOUT:d,DELETE_LAYOUT:m,SAVE_APP_DATA:"SAVE_APP_DATA",LOAD_APP_DATA:"LOAD_APP_DATA",SET_LOADING:"SET_LOADING",SET_ERROR:"SET_ERROR",CLEAR_ERROR:"CLEAR_ERROR"},f=function(e){return{type:i,payload:{type:e,props:arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}}}},g=function(e){return{type:s,payload:{type:e,components:arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],styles:arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}}}},v=function(e){return{type:c,payload:e}},y=function(e){return function(){var t=(0,r.A)(a().mark((function t(n){var r,o;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,fetch("".concat("http://localhost:8000","/api/app-data/"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});case 3:return r=t.sent,t.next=6,r.json();case 6:o=t.sent,n(v(o)),t.next=14;break;case 10:t.prev=10,t.t0=t.catch(0),console.error("Error saving app data:",t.t0),n((a=t.t0.message,{type:l,payload:a}));case 14:case"end":return t.stop()}var a}),t,null,[[0,10]])})));return function(e){return t.apply(this,arguments)}}()},E=function(e,t){return{type:u,payload:{index:e,updates:t}}},A=function(e){return{type:p,payload:{index:e}}},b=function(e,t){return{type:d,payload:{index:e,updates:t}}},_=function(e){return{type:m,payload:{index:e}}},S=function(){return{type:"LOAD_PROJECTS"}},k=function(e){return{type:"SET_ACTIVE_PROJECT",payload:e}},C=function(e){return{type:"UPDATE_PROJECT",payload:e}},O=function(e){return{type:"CREATE_PROJECT",payload:e}},w=function(e){return{type:"DELETE_PROJECT",payload:e}},P=function(e){return{type:"ADD_THEME",payload:e}},T=function(e){return{type:"UPDATE_THEME",payload:e}},x=function(e){return{type:"REMOVE_THEME",payload:{id:e}}},D=function(e){return{type:"SET_ACTIVE_THEME",payload:e}}},3385:(e,t,n)=>{n.d(t,{HP:()=>E,NP:()=>v,rV:()=>A});var r=n(5544),o=n(4467),a=n(6540),i=n(9249),s=n(9467),c=n(7355),l=n(778);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var d="light",m="dark",h="system",f=(0,o.A)((0,o.A)({},d,{primary:"#1976d2",secondary:"#f50057",background:"#ffffff",surface:"#f5f5f5",text:"#212121",textSecondary:"#757575",border:"#e0e0e0",error:"#d32f2f",warning:"#f57c00",info:"#0288d1",success:"#388e3c"}),m,{primary:"#90caf9",secondary:"#f48fb1",background:"#121212",surface:"#1e1e1e",text:"#ffffff",textSecondary:"#b0b0b0",border:"#333333",error:"#f44336",warning:"#ff9800",info:"#29b6f6",success:"#66bb6a"}),g=(0,a.createContext)({theme:d,colors:f[d],setTheme:function(){},setCustomColors:function(){}}),v=function(e){var t=e.children,n=e.initialTheme,o=void 0===n?h:n,i=(0,a.useState)(o),s=(0,r.A)(i,2),c=s[0],l=s[1],u=(0,a.useState)({}),v=(0,r.A)(u,2),y=v[0],E=v[1],A=(0,a.useState)(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?m:d),b=(0,r.A)(A,2),_=b[0],S=b[1],k=function(){return c===h?f[_]:"custom"===c?p(p({},f[d]),y):f[c]};return(0,a.useEffect)((function(){var e=k();Object.entries(e).forEach((function(e){var t=(0,r.A)(e,2),n=t[0],o=t[1];document.documentElement.style.setProperty("--color-".concat(n),o)}));var t=c===h?_:c;document.documentElement.setAttribute("data-theme",t),t===m?document.body.classList.add("dark-theme"):document.body.classList.remove("dark-theme")}),[c,_,y]),(0,a.useEffect)((function(){var e=window.matchMedia("(prefers-color-scheme: dark)"),t=function(e){S(e.matches?m:d)};e.addEventListener?e.addEventListener("change",t):e.addListener(t);var n=localStorage.getItem("app_theme");return n&&l(n),function(){e.removeEventListener?e.removeEventListener("change",t):e.removeListener(t)}}),[]),a.createElement(g.Provider,{value:{theme:c,colors:k(),setTheme:function(e){l(e),localStorage.setItem("app_theme",e)},setCustomColors:E}},t)},y=function(){var e=(0,a.useContext)(g);if(!e)throw new Error("useTheme must be used within a ThemeProvider");return e},E=function(e){var t=e.position,n=void 0===t?"right":t,r=y(),o=r.theme,s=r.setTheme;return a.createElement("div",{className:"theme-switcher ".concat(n)},a.createElement(i.Ay,{type:"text",icon:a.createElement(l.A,null),onClick:function(){s(o===m?d:m)},"aria-label":"Switch to ".concat(o===m?"light":"dark"," theme")}))},A=function(e){var t=e.onSave,n=y(),o=n.colors,l=n.setCustomColors,u=s.A.useForm(),p=(0,r.A)(u,1)[0];return(0,a.useEffect)((function(){p.setFieldsValue({primaryColor:o.primary,secondaryColor:o.secondary,backgroundColor:o.background,textColor:o.text})}),[o,p]),a.createElement("div",{className:"theme-customizer"},a.createElement(s.A,{form:p,layout:"vertical",onFinish:function(e){l(e),t&&t(e)}},a.createElement(s.A.Item,{name:"primaryColor",label:"Primary Color"},a.createElement(c.A,{type:"color"})),a.createElement(s.A.Item,{name:"secondaryColor",label:"Secondary Color"},a.createElement(c.A,{type:"color"})),a.createElement(s.A.Item,{name:"backgroundColor",label:"Background Color"},a.createElement(c.A,{type:"color"})),a.createElement(s.A.Item,{name:"textColor",label:"Text Color"},a.createElement(c.A,{type:"color"})),a.createElement(s.A.Item,null,a.createElement(i.Ay,{type:"primary",htmlType:"submit"},"Apply Theme"))))}},3587:(e,t,n)=>{n.d(t,{$n:()=>f,Zp:()=>R,pd:()=>ue,l6:()=>be,I4:()=>_e});var r,o=n(6020),a=n(8168),i=n(3986),s=n(7528),c=n(6540),l=n(5556),u=n.n(l),p=n(1250),d=["children","variant","size","disabled","fullWidth","startIcon","endIcon","onClick"],m=p.Ay.button(r||(r=(0,s.A)(["\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: "," ",";\n  border-radius: ",";\n  font-family: ",";\n  font-weight: ",";\n  font-size: ",";\n  transition: ",";\n  cursor: ",";\n  opacity: ",";\n  width: ",";\n\n  /* Variant styles */\n  background-color: ",";\n\n  color: ",";\n\n  border: ",";\n\n  &:hover {\n    background-color: ",";\n\n    color: ",";\n  }\n\n  &:focus {\n    outline: none;\n    box-shadow: 0 0 0 3px ",";\n  }\n\n  .button-start-icon {\n    margin-right: ",";\n  }\n\n  .button-end-icon {\n    margin-left: ",";\n  }\n"])),(function(e){return"small"===e.size?o.Ay.spacing[2]:"large"===e.size?o.Ay.spacing[4]:o.Ay.spacing[3]}),(function(e){return"small"===e.size?o.Ay.spacing[3]:"large"===e.size?o.Ay.spacing[6]:o.Ay.spacing[4]}),o.Ay.borderRadius.md,o.Ay.typography.fontFamily.primary,o.Ay.typography.fontWeight.medium,(function(e){return"small"===e.size?o.Ay.typography.fontSize.sm:"large"===e.size?o.Ay.typography.fontSize.lg:o.Ay.typography.fontSize.md}),o.Ay.transitions.default,(function(e){return e.disabled?"not-allowed":"pointer"}),(function(e){return e.disabled?.6:1}),(function(e){return e.fullWidth?"100%":"auto"}),(function(e){return"primary"===e.variant?o.Ay.colors.primary.main:"secondary"===e.variant?o.Ay.colors.secondary.main:"outline"===e.variant||"text"===e.variant?"transparent":o.Ay.colors.primary.main}),(function(e){return"outline"===e.variant||"text"===e.variant?o.Ay.colors.primary.main:o.Ay.colors.primary.contrastText}),(function(e){return"outline"===e.variant?"1px solid ".concat(o.Ay.colors.primary.main):"none"}),(function(e){return"primary"===e.variant?o.Ay.colors.primary.dark:"secondary"===e.variant?o.Ay.colors.secondary.dark:"outline"===e.variant||"text"===e.variant?o.Ay.colors.primary.light:o.Ay.colors.primary.dark}),(function(e){return"outline"===e.variant||"text"===e.variant?o.Ay.colors.primary.dark:o.Ay.colors.primary.contrastText}),o.Ay.colors.primary.light,o.Ay.spacing[2],o.Ay.spacing[2]),h=function(e){var t=e.children,n=e.variant,r=void 0===n?"primary":n,o=e.size,s=void 0===o?"medium":o,l=e.disabled,u=void 0!==l&&l,p=e.fullWidth,h=void 0!==p&&p,f=e.startIcon,g=e.endIcon,v=e.onClick,y=(0,i.A)(e,d);return c.createElement(m,(0,a.A)({variant:r,size:s,disabled:u,fullWidth:h,onClick:u?void 0:v},y),f&&c.createElement("span",{className:"button-start-icon"},f),t,g&&c.createElement("span",{className:"button-end-icon"},g))};h.propTypes={children:u().node.isRequired,variant:u().oneOf(["primary","secondary","outline","text"]),size:u().oneOf(["small","medium","large"]),disabled:u().bool,fullWidth:u().bool,startIcon:u().node,endIcon:u().node,onClick:u().func};const f=h;var g,v,y,E,A,b=["children","elevation","radius","fullWidth","fullHeight"],_=["children","divider"],S=["children"],k=["children"],C=["children","divider","align"],O=p.Ay.div(g||(g=(0,s.A)(["\n  background-color: white;\n  border-radius: ",";\n  box-shadow: ",";\n  overflow: hidden;\n  width: ",";\n  height: ",";\n  display: flex;\n  flex-direction: column;\n"])),(function(e){return o.Ay.borderRadius[e.radius]}),(function(e){return o.Ay.shadows[e.elevation]}),(function(e){return e.fullWidth?"100%":"auto"}),(function(e){return e.fullHeight?"100%":"auto"})),w=p.Ay.div(v||(v=(0,s.A)(["\n  padding: ",";\n  border-bottom: ",";\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n"])),o.Ay.spacing[4],(function(e){return e.divider?"1px solid ".concat(o.Ay.colors.neutral[200]):"none"})),P=p.Ay.h3(y||(y=(0,s.A)(["\n  margin: 0;\n  font-size: ",";\n  font-weight: ",";\n  color: ",";\n"])),o.Ay.typography.fontSize.lg,o.Ay.typography.fontWeight.semibold,o.Ay.colors.neutral[900]),T=p.Ay.div(E||(E=(0,s.A)(["\n  padding: ",";\n  flex: 1;\n"])),o.Ay.spacing[4]),x=p.Ay.div(A||(A=(0,s.A)(["\n  padding: ",";\n  border-top: ",";\n  display: flex;\n  align-items: center;\n  justify-content: ",";\n  gap: ",";\n"])),o.Ay.spacing[4],(function(e){return e.divider?"1px solid ".concat(o.Ay.colors.neutral[200]):"none"}),(function(e){return"right"===e.align?"flex-end":"center"===e.align?"center":"flex-start"}),o.Ay.spacing[2]),D=function(e){var t=e.children,n=e.elevation,r=void 0===n?"md":n,o=e.radius,s=void 0===o?"md":o,l=e.fullWidth,u=void 0!==l&&l,p=e.fullHeight,d=void 0!==p&&p,m=(0,i.A)(e,b);return c.createElement(O,(0,a.A)({elevation:r,radius:s,fullWidth:u,fullHeight:d},m),t)};D.Header=function(e){var t=e.children,n=e.divider,r=void 0!==n&&n,o=(0,i.A)(e,_);return c.createElement(w,(0,a.A)({divider:r},o),t)},D.Title=function(e){var t=e.children,n=(0,i.A)(e,S);return c.createElement(P,n,t)},D.Content=function(e){var t=e.children,n=(0,i.A)(e,k);return c.createElement(T,n,t)},D.Footer=function(e){var t=e.children,n=e.divider,r=void 0===n||n,o=e.align,s=void 0===o?"right":o,l=(0,i.A)(e,C);return c.createElement(x,(0,a.A)({divider:r,align:s},l),t)},D.propTypes={children:u().node.isRequired,elevation:u().oneOf(["none","sm","md","lg","xl","2xl"]),radius:u().oneOf(["none","sm","md","lg","xl","2xl","3xl","full"]),fullWidth:u().bool,fullHeight:u().bool},D.Header.propTypes={children:u().node.isRequired,divider:u().bool},D.Title.propTypes={children:u().node.isRequired},D.Content.propTypes={children:u().node.isRequired},D.Footer.propTypes={children:u().node.isRequired,divider:u().bool,align:u().oneOf(["left","center","right"])};const R=D;var I,N,L,W,F,U,M,j,G,H,z,B,V,q,Q,K,J,Y,X,$,Z,ee=n(5544),te=["label","helperText","error","fullWidth","prefix","suffix","disabled"],ne=p.Ay.div(I||(I=(0,s.A)(["\n  display: flex;\n  flex-direction: column;\n  width: ",";\n"])),(function(e){return e.fullWidth?"100%":"auto"})),re=p.Ay.label(N||(N=(0,s.A)(["\n  font-size: ",";\n  font-weight: ",";\n  color: ",";\n  margin-bottom: ",";\n"])),o.Ay.typography.fontSize.sm,o.Ay.typography.fontWeight.medium,o.Ay.colors.neutral[700],o.Ay.spacing[1]),oe=p.Ay.input(L||(L=(0,s.A)(["\n  font-family: ",";\n  font-size: ",";\n  color: ",";\n  background-color: ",";\n  border: 1px solid ",";\n  border-radius: ",";\n  padding: "," ",";\n  transition: ",";\n  width: 100%;\n\n  &:focus {\n    outline: none;\n    border-color: ",";\n    box-shadow: 0 0 0 3px ",";\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n    opacity: 0.7;\n  }\n\n  &::placeholder {\n    color: ",";\n  }\n"])),(null===o.Ay||void 0===o.Ay||null===(W=o.Ay.typography)||void 0===W||null===(W=W.fontFamily)||void 0===W?void 0:W.primary)||"Inter, sans-serif",(null===o.Ay||void 0===o.Ay||null===(F=o.Ay.typography)||void 0===F||null===(F=F.fontSize)||void 0===F?void 0:F.md)||"16px",(null===o.Ay||void 0===o.Ay||null===(U=o.Ay.colors)||void 0===U||null===(U=U.neutral)||void 0===U?void 0:U[900])||"#111827",(function(e){var t;return e.disabled?(null===o.Ay||void 0===o.Ay||null===(t=o.Ay.colors)||void 0===t||null===(t=t.neutral)||void 0===t?void 0:t[100])||"#F3F4F6":"white"}),(function(e){var t,n,r;return e.error?(null===o.Ay||void 0===o.Ay||null===(t=o.Ay.colors)||void 0===t||null===(t=t.error)||void 0===t?void 0:t.main)||"#DC2626":e.focused?(null===o.Ay||void 0===o.Ay||null===(n=o.Ay.colors)||void 0===n||null===(n=n.primary)||void 0===n?void 0:n.main)||"#2563EB":(null===o.Ay||void 0===o.Ay||null===(r=o.Ay.colors)||void 0===r||null===(r=r.neutral)||void 0===r?void 0:r[300])||"#D1D5DB"}),(null===o.Ay||void 0===o.Ay||null===(M=o.Ay.borderRadius)||void 0===M?void 0:M.md)||"4px",(null===o.Ay||void 0===o.Ay||null===(j=o.Ay.spacing)||void 0===j?void 0:j[2])||"8px",(null===o.Ay||void 0===o.Ay||null===(G=o.Ay.spacing)||void 0===G?void 0:G[3])||"12px",(null===o.Ay||void 0===o.Ay||null===(H=o.Ay.transitions)||void 0===H?void 0:H.default)||"all 0.2s ease-in-out",(null===o.Ay||void 0===o.Ay||null===(z=o.Ay.colors)||void 0===z||null===(z=z.primary)||void 0===z?void 0:z.main)||"#2563EB",(null===o.Ay||void 0===o.Ay||null===(B=o.Ay.colors)||void 0===B||null===(B=B.primary)||void 0===B?void 0:B.light)||"rgba(37, 99, 235, 0.2)",(null===o.Ay||void 0===o.Ay||null===(V=o.Ay.colors)||void 0===V||null===(V=V.neutral)||void 0===V?void 0:V[400])||"#9CA3AF"),ae=p.Ay.div(q||(q=(0,s.A)(["\n  display: flex;\n  align-items: center;\n  margin-right: ",";\n"])),(null===o.Ay||void 0===o.Ay||null===(Q=o.Ay.spacing)||void 0===Q?void 0:Q[2])||"8px"),ie=p.Ay.div(K||(K=(0,s.A)(["\n  display: flex;\n  align-items: center;\n  margin-left: ",";\n"])),(null===o.Ay||void 0===o.Ay||null===(J=o.Ay.spacing)||void 0===J?void 0:J[2])||"8px"),se=p.Ay.div(Y||(Y=(0,s.A)(["\n  display: flex;\n  align-items: center;\n  position: relative;\n  width: 100%;\n"]))),ce=p.Ay.div(X||(X=(0,s.A)(["\n  font-size: ",";\n  margin-top: ",";\n  color: ",";\n"])),(null===o.Ay||void 0===o.Ay||null===($=o.Ay.typography)||void 0===$||null===($=$.fontSize)||void 0===$?void 0:$.xs)||"12px",(null===o.Ay||void 0===o.Ay||null===(Z=o.Ay.spacing)||void 0===Z?void 0:Z[1])||"4px",(function(e){var t,n;return e.error?(null===o.Ay||void 0===o.Ay||null===(t=o.Ay.colors)||void 0===t||null===(t=t.error)||void 0===t?void 0:t.main)||"#DC2626":(null===o.Ay||void 0===o.Ay||null===(n=o.Ay.colors)||void 0===n||null===(n=n.neutral)||void 0===n?void 0:n[500])||"#6B7280"})),le=(0,c.forwardRef)((function(e,t){var n=e.label,r=e.helperText,o=e.error,s=e.fullWidth,l=void 0!==s&&s,u=e.prefix,p=e.suffix,d=e.disabled,m=void 0!==d&&d,h=(0,i.A)(e,te),f=c.useState(!1),g=(0,ee.A)(f,2),v=g[0],y=g[1];return c.createElement(ne,{fullWidth:l},n&&c.createElement(re,null,n),c.createElement(se,null,u&&c.createElement(ae,null,u),c.createElement(oe,(0,a.A)({ref:t,disabled:m,error:o,focused:v,onFocus:function(e){y(!0),h.onFocus&&h.onFocus(e)},onBlur:function(e){y(!1),h.onBlur&&h.onBlur(e)}},h)),p&&c.createElement(ie,null,p)),r&&c.createElement(ce,{error:o},r))}));le.displayName="Input",le.propTypes={label:u().string,helperText:u().string,error:u().bool,fullWidth:u().bool,prefix:u().node,suffix:u().node,disabled:u().bool,onFocus:u().func,onBlur:u().func};const ue=le;var pe,de,me,he,fe=["label","helperText","error","fullWidth","options","disabled"],ge=p.Ay.div(pe||(pe=(0,s.A)(["\n  display: flex;\n  flex-direction: column;\n  width: ",";\n"])),(function(e){return e.fullWidth?"100%":"auto"})),ve=p.Ay.label(de||(de=(0,s.A)(["\n  font-size: ",";\n  font-weight: ",";\n  color: ",";\n  margin-bottom: ",";\n"])),o.Ay.typography.fontSize.sm,o.Ay.typography.fontWeight.medium,o.Ay.colors.neutral[700],o.Ay.spacing[1]),ye=p.Ay.select(me||(me=(0,s.A)(["\n  font-family: ",";\n  font-size: ",";\n  color: ",";\n  background-color: ",";\n  border: 1px solid ",";\n  border-radius: ",";\n  padding: "," ",";\n  transition: ",";\n  width: 100%;\n  appearance: none;\n  background-image: url(\"data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3E%3C/svg%3E\");\n  background-position: right "," center;\n  background-repeat: no-repeat;\n  background-size: 1.5em 1.5em;\n  padding-right: 2.5em;\n\n  &:focus {\n    outline: none;\n    border-color: ",";\n    box-shadow: 0 0 0 3px ",";\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n    opacity: 0.7;\n  }\n"])),o.Ay.typography.fontFamily.primary,o.Ay.typography.fontSize.md,o.Ay.colors.neutral[900],(function(e){return e.disabled?o.Ay.colors.neutral[100]:"white"}),(function(e){return e.error?o.Ay.colors.error.main:e.focused?o.Ay.colors.primary.main:o.Ay.colors.neutral[300]}),o.Ay.borderRadius.md,o.Ay.spacing[2],o.Ay.spacing[3],o.Ay.transitions.default,o.Ay.spacing[2],o.Ay.colors.primary.main,o.Ay.colors.primary.light),Ee=p.Ay.div(he||(he=(0,s.A)(["\n  font-size: ",";\n  margin-top: ",";\n  color: ",";\n"])),o.Ay.typography.fontSize.xs,o.Ay.spacing[1],(function(e){return e.error?o.Ay.colors.error.main:o.Ay.colors.neutral[500]})),Ae=(0,c.forwardRef)((function(e,t){var n=e.label,r=e.helperText,o=e.error,s=e.fullWidth,l=void 0!==s&&s,u=e.options,p=void 0===u?[]:u,d=e.disabled,m=void 0!==d&&d,h=(0,i.A)(e,fe),f=c.useState(!1),g=(0,ee.A)(f,2),v=g[0],y=g[1];return c.createElement(ge,{fullWidth:l},n&&c.createElement(ve,null,n),c.createElement(ye,(0,a.A)({ref:t,disabled:m,error:o,focused:v,onFocus:function(e){y(!0),h.onFocus&&h.onFocus(e)},onBlur:function(e){y(!1),h.onBlur&&h.onBlur(e)}},h),p.map((function(e){return c.createElement("option",{key:e.value,value:e.value},e.label)}))),r&&c.createElement(Ee,{error:o},r))}));Ae.displayName="Select",Ae.propTypes={label:u().string,helperText:u().string,error:u().bool,fullWidth:u().bool,options:u().arrayOf(u().shape({value:u().oneOfType([u().string,u().number]).isRequired,label:u().string.isRequired})),disabled:u().bool,onFocus:u().func,onBlur:u().func};const be=Ae,_e=p.Ay},4318:(e,t,n)=>{n.d(t,{AS:()=>d,H2:()=>l,Kg:()=>r,RH:()=>c,Te:()=>i,WD:()=>p,YG:()=>a,ZH:()=>u,_E:()=>g,co:()=>o,ei:()=>h,oz:()=>m,uV:()=>s,xS:()=>f});var r="WEBSOCKET_CONNECTED",o="WEBSOCKET_DISCONNECTED",a="WS_CONNECT",i="WS_CONNECTED",s="WS_DISCONNECT",c="WS_DISCONNECTED",l="WS_MESSAGE",u="WS_MESSAGE_RECEIVED",p="WS_SEND_MESSAGE",d="WS_ERROR",m="ADD_COMPONENT",h="UPDATE_COMPONENT",f="REMOVE_COMPONENT",g="TOGGLE_AUTO_APPLY_THEME"},4702:(e,t,n)=>{n.d(t,{ec:()=>W,gf:()=>S,wz:()=>O,VM:()=>N,wR:()=>T,iD:()=>x,ri:()=>R,Be:()=>I,kz:()=>D,eg:()=>L});var r=n(467),o=n(4756),a=n.n(o),i=n(4467),s=n(3029),c=n(2901),l=n(1083),u="http://localhost:8000",p=u;console.log("API Base URL:","http://localhost:8000"),console.log("Development mode:",!1),console.log("API Prefix:",p);var d={CSRF_TOKEN:"".concat(p,"/csrf-token/"),STATUS:["".concat(p,"/status/"),"".concat(p,"/health/"),"".concat(p,"/health-check/")],HEALTH:["".concat(p,"/health/"),"".concat(p,"/health-check/"),"".concat(p,"/status/")],APP_DATA:["".concat(p,"/get_app_data/"),"".concat(p,"/apps/"),"".concat(p,"/v1/apps/"),"".concat(p,"/app-data/")],SAVE_APP_DATA:["".concat(p,"/save_app_data/"),"".concat(p,"/apps/"),"".concat(p,"/v1/apps/"),"".concat(p,"/app-data/")],EXPORT_APP_DATA:["".concat(p,"/api/app-data/export/"),"".concat(p,"/export_app_data/")],IMPORT_APP_DATA:["".concat(p,"/api/app-data/import/"),"".concat(p,"/import_app_data/")],GENERATE_AI_SUGGESTIONS:["".concat(u,"/api/ai/suggestions/"),"".concat(u,"/generate_ai_suggestions/")],GENERATE_IMAGE:["".concat(u,"/api/ai/generate-image/"),"".concat(u,"/generate_image/")],LOGIN:["".concat(u,"/api/auth/login/"),"".concat(u,"/auth/login/")],REGISTER:["".concat(u,"/api/auth/register/"),"".concat(u,"/auth/register/")],USER_PROFILE:["".concat(u,"/api/auth/profile/"),"".concat(u,"/auth/profile/")],UPDATE_PROFILE:["".concat(u,"/api/auth/profile/update/"),"".concat(u,"/auth/profile/update/")],API_KEYS:["".concat(u,"/api/api-keys/"),"".concat(u,"/api/v1/api-keys/")],VALIDATE_API_KEY:["".concat(u,"/api/validate-api-key/"),"".concat(u,"/api/v1/validate-api-key/")],API_V1:"".concat(u,"/api/v1/"),API_V2:"".concat(u,"/api/v2/"),GRAPHQL:"".concat(u,"/graphql/")};function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const f=new(function(){return(0,c.A)((function e(){(0,s.A)(this,e),this.token=null,this.tokenPromise=null}),[{key:"getTokenFromCookie",value:function(){var e="; ".concat(document.cookie).split("; ".concat("csrftoken","="));return 2===e.length?e.pop().split(";").shift():null}},{key:"fetchToken",value:(i=(0,r.A)(a().mark((function e(){var t,n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch(d.CSRF_TOKEN,{method:"GET",credentials:"include",headers:{"Content-Type":"application/json"}});case 3:if((t=e.sent).ok){e.next=6;break}throw new Error("Failed to fetch CSRF token: ".concat(t.status));case 6:return e.next=8,t.json();case 8:return n=e.sent,this.token=n.csrfToken,e.abrupt("return",this.token);case 13:throw e.prev=13,e.t0=e.catch(0),console.error("Error fetching CSRF token:",e.t0),e.t0;case 17:case"end":return e.stop()}}),e,this,[[0,13]])}))),function(){return i.apply(this,arguments)})},{key:"getToken",value:(o=(0,r.A)(a().mark((function e(){var t,n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.token){e.next=2;break}return e.abrupt("return",this.token);case 2:if(!this.tokenPromise){e.next=4;break}return e.abrupt("return",this.tokenPromise);case 4:if(!(t=this.getTokenFromCookie())){e.next=8;break}return this.token=t,e.abrupt("return",this.token);case 8:return this.tokenPromise=this.fetchToken(),e.prev=9,e.next=12,this.tokenPromise;case 12:return n=e.sent,this.tokenPromise=null,e.abrupt("return",n);case 17:throw e.prev=17,e.t0=e.catch(9),this.tokenPromise=null,e.t0;case 21:case"end":return e.stop()}}),e,this,[[9,17]])}))),function(){return o.apply(this,arguments)})},{key:"clearToken",value:function(){this.token=null,this.tokenPromise=null}},{key:"getHeaders",value:(n=(0,r.A)(a().mark((function e(){var t,n,r=arguments;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.length>0&&void 0!==r[0]?r[0]:{},e.prev=1,e.next=4,this.getToken();case 4:return n=e.sent,e.abrupt("return",h({"X-CSRFToken":n,"Content-Type":"application/json"},t));case 8:return e.prev=8,e.t0=e.catch(1),console.warn("Failed to get CSRF token, proceeding without it:",e.t0),e.abrupt("return",h({"Content-Type":"application/json"},t));case 12:case"end":return e.stop()}}),e,this,[[1,8]])}))),function(){return n.apply(this,arguments)})},{key:"request",value:(t=(0,r.A)(a().mark((function e(t){var n,r,o=arguments;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:{},e.next=3,this.getHeaders(n.headers);case 3:return r=e.sent,e.abrupt("return",fetch(t,h(h({},n),{},{headers:r,credentials:"include"})));case 5:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"initialize",value:(e=(0,r.A)(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.getToken();case 3:console.log("CSRF service initialized successfully"),e.next=9;break;case 6:e.prev=6,e.t0=e.catch(0),console.warn("Failed to initialize CSRF service:",e.t0);case 9:case"end":return e.stop()}}),e,this,[[0,6]])}))),function(){return e.apply(this,arguments)})}]);var e,t,n,o,i}());function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var y={baseURL:"http://localhost:8000",timeout:1e4,headers:{"Content-Type":"application/json",Accept:"application/json"}};const E=new(function(){return(0,c.A)((function e(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,s.A)(this,e),this.config=v(v({},y),n),this.client=l.A.create(this.config),this.endpoints=[],this.initialized=!1,this.client.interceptors.request.use(function(){var e=(0,r.A)(a().mark((function e(t){var n,r;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if((n=S())&&(t.headers.Authorization="Bearer ".concat(n)),!t.method||"get"===t.method.toLowerCase()){e.next=13;break}return e.prev=3,e.next=6,f.getHeaders();case 6:r=e.sent,t.headers=v(v({},t.headers),r),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(3),console.warn("Failed to get CSRF token:",e.t0);case 13:return t.withCredentials=!0,e.abrupt("return",t);case 15:case"end":return e.stop()}}),e,null,[[3,10]])})));return function(t){return e.apply(this,arguments)}}(),(function(e){return Promise.reject(e)})),this.client.interceptors.response.use((function(e){return e.data}),function(){var e=(0,r.A)(a().mark((function e(n){var r,o;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n.config,!n.response||401!==n.response.status||r._retry){e.next=18;break}return r._retry=!0,e.prev=3,e.next=6,I();case 6:if(!(o=e.sent)){e.next=11;break}return t.client.defaults.headers.common.Authorization="Bearer ".concat(o),r.headers.Authorization="Bearer ".concat(o),e.abrupt("return",t.client(r));case 11:e.next=18;break;case 13:return e.prev=13,e.t0=e.catch(3),console.error("Token refresh failed:",e.t0),window.location.href="/login",e.abrupt("return",Promise.reject(e.t0));case 18:return e.abrupt("return",Promise.reject(n));case 19:case"end":return e.stop()}}),e,null,[[3,13]])})));return function(t){return e.apply(this,arguments)}}())}),[{key:"initServices",value:(p=(0,r.A)(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.discoverEndpoints();case 3:return this.initialized=!0,e.abrupt("return",{initialized:!0,endpoints:this.endpoints});case 7:e.prev=7,e.t0=e.catch(0),console.warn("API client initialization failed:",e.t0),e.next=14;break;case 14:throw e.t0;case 15:case"end":return e.stop()}}),e,this,[[0,7]])}))),function(){return p.apply(this,arguments)})},{key:"discoverEndpoints",value:(u=(0,r.A)(a().mark((function e(){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.client.get("/");case 3:if(!(t=e.sent)||!t.endpoints){e.next=7;break}return this.endpoints=t.endpoints,e.abrupt("return",this.endpoints);case 7:return this.endpoints=[{path:"/status",method:"GET",description:"Get API status"},{path:"/app-data",method:"GET",description:"Get app data"},{path:"/components",method:"GET",description:"Get components"},{path:"/templates",method:"GET",description:"Get templates"},{path:"/projects",method:"GET",description:"Get projects"},{path:"/users",method:"GET",description:"Get users"},{path:"/auth",method:"POST",description:"Authenticate user"}],e.abrupt("return",this.endpoints);case 11:return e.prev=11,e.t0=e.catch(0),console.warn("API endpoint discovery failed:",e.t0),this.endpoints=[{path:"/status",method:"GET",description:"Get API status"},{path:"/app-data",method:"GET",description:"Get app data"},{path:"/components",method:"GET",description:"Get components"},{path:"/templates",method:"GET",description:"Get templates"},{path:"/projects",method:"GET",description:"Get projects"},{path:"/users",method:"GET",description:"Get users"},{path:"/auth",method:"POST",description:"Authenticate user"}],e.abrupt("return",this.endpoints);case 16:case"end":return e.stop()}}),e,this,[[0,11]])}))),function(){return u.apply(this,arguments)})},{key:"get",value:(i=(0,r.A)(a().mark((function e(t){var n,r=arguments;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=r.length>1&&void 0!==r[1]?r[1]:{},e.abrupt("return",this.client.get(t,n));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return i.apply(this,arguments)})},{key:"post",value:(o=(0,r.A)(a().mark((function e(t){var n,r,o=arguments;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:{},r=o.length>2&&void 0!==o[2]?o[2]:{},e.abrupt("return",this.client.post(t,n,r));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return o.apply(this,arguments)})},{key:"put",value:(n=(0,r.A)(a().mark((function e(t){var n,r,o=arguments;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:{},r=o.length>2&&void 0!==o[2]?o[2]:{},e.abrupt("return",this.client.put(t,n,r));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return n.apply(this,arguments)})},{key:"patch",value:(t=(0,r.A)(a().mark((function e(t){var n,r,o=arguments;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:{},r=o.length>2&&void 0!==o[2]?o[2]:{},e.abrupt("return",this.client.patch(t,n,r));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"delete",value:(e=(0,r.A)(a().mark((function e(t){var n,r=arguments;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=r.length>1&&void 0!==r[1]?r[1]:{},e.abrupt("return",this.client.delete(t,n));case 2:case"end":return e.stop()}}),e,this)}))),function(t){return e.apply(this,arguments)})},{key:"hasEndpoint",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"GET";return this.endpoints.some((function(n){return n.path===e&&n.method===t}))}},{key:"getEndpointDescription",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"GET",n=this.endpoints.find((function(n){return n.path===e&&n.method===t}));return n?n.description:""}}]);var e,t,n,o,i,u,p}());var A="app_auth_token",b="app_refresh_token",_="app_user",S=function(){return localStorage.getItem(A)},k=function(e){localStorage.setItem(A,e)},C=function(e){localStorage.setItem(b,e)},O=function(){var e=localStorage.getItem(_);if(e)try{return JSON.parse(e)}catch(e){return console.error("Error parsing user data:",e),null}return null},w=function(e){localStorage.setItem(_,JSON.stringify(e))},P=function(){localStorage.removeItem(A),localStorage.removeItem(b),localStorage.removeItem(_)},T=function(){return!!S()},x=function(){var e=(0,r.A)(a().mark((function e(t,n){var r,o,i;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,E.post("/auth/login/",{username:t,password:n});case 3:if(!(r=e.sent).access){e.next=20;break}return k(r.access),r.refresh&&C(r.refresh),e.prev=7,e.next=10,E.get("/auth/profile/");case 10:(o=e.sent)&&w(o),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(7),console.warn("Failed to fetch user profile:",e.t0);case 17:return e.abrupt("return",{success:!0,user:r.user||{username:t}});case 20:if(!r.token){e.next=24;break}return k(r.token),r.user&&w(r.user),e.abrupt("return",{success:!0,user:r.user});case 24:return e.abrupt("return",{success:!1,error:"Invalid response from server"});case 27:return e.prev=27,e.t1=e.catch(0),console.error("Login error:",e.t1),e.abrupt("return",{success:!1,error:(null===(i=e.t1.response)||void 0===i||null===(i=i.data)||void 0===i?void 0:i.detail)||e.t1.message||"Login failed"});case 31:case"end":return e.stop()}}),e,null,[[0,27],[7,14]])})));return function(t,n){return e.apply(this,arguments)}}(),D=function(){var e=(0,r.A)(a().mark((function e(t){var n,r,o,i;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,E.post("/auth/register/",t);case 3:if(!(n=e.sent).access){e.next=20;break}return k(n.access),n.refresh&&C(n.refresh),e.prev=7,e.next=10,E.get("/auth/profile/");case 10:(r=e.sent)&&w(r),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(7),console.warn("Failed to fetch user profile:",e.t0);case 17:return e.abrupt("return",{success:!0,user:n.user||{username:t.username}});case 20:if(!n.token){e.next=26;break}return k(n.token),n.user&&w(n.user),e.abrupt("return",{success:!0,user:n.user});case 26:if(!n.success){e.next=28;break}return e.abrupt("return",{success:!0,user:n.user});case 28:return e.abrupt("return",{success:!1,error:n.error||"Registration failed"});case 31:return e.prev=31,e.t1=e.catch(0),console.error("Registration error:",e.t1),e.abrupt("return",{success:!1,error:(null===(o=e.t1.response)||void 0===o||null===(o=o.data)||void 0===o?void 0:o.detail)||(null===(i=e.t1.response)||void 0===i||null===(i=i.data)||void 0===i?void 0:i.message)||e.t1.message||"Registration failed"});case 35:case"end":return e.stop()}}),e,null,[[0,31],[7,14]])})));return function(t){return e.apply(this,arguments)}}(),R=function(){var e=(0,r.A)(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,E.post("/auth/logout");case 3:e.next=8;break;case 5:e.prev=5,e.t0=e.catch(0),console.warn("Logout notification failed:",e.t0);case 8:return e.prev=8,P(),e.finish(8);case 11:return e.abrupt("return",{success:!0});case 12:case"end":return e.stop()}}),e,null,[[0,5,8,11]])})));return function(){return e.apply(this,arguments)}}(),I=function(){var e=(0,r.A)(a().mark((function e(){var t,n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,t=localStorage.getItem(b)){e.next=4;break}throw new Error("No refresh token available");case 4:return e.next=6,E.post("/auth/token/refresh/",{refresh:t});case 6:if(!(n=e.sent).access){e.next=13;break}return k(n.access),n.refresh&&C(n.refresh),e.abrupt("return",n.access);case 13:if(!n.token){e.next=17;break}return k(n.token),n.refreshToken&&C(n.refreshToken),e.abrupt("return",n.token);case 17:return e.abrupt("return",null);case 20:return e.prev=20,e.t0=e.catch(0),console.error("Token refresh error:",e.t0),P(),e.abrupt("return",null);case 25:case"end":return e.stop()}}),e,null,[[0,20]])})));return function(){return e.apply(this,arguments)}}(),N=function(){var e=(0,r.A)(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,E.get("/auth/profile");case 3:return e.abrupt("return",e.sent);case 6:return e.prev=6,e.t0=e.catch(0),console.error("Get user profile error:",e.t0),e.abrupt("return",null);case 10:case"end":return e.stop()}}),e,null,[[0,6]])})));return function(){return e.apply(this,arguments)}}(),L=function(){var e=(0,r.A)(a().mark((function e(t){var n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,E.put("/auth/profile",t);case 3:if(!(n=e.sent).user){e.next=7;break}return w(n.user),e.abrupt("return",{success:!0,user:n.user});case 7:return e.abrupt("return",{success:!1,error:"Invalid response from server"});case 10:return e.prev=10,e.t0=e.catch(0),console.error("Update profile error:",e.t0),e.abrupt("return",{success:!1,error:e.t0.message||"Update profile failed"});case 14:case"end":return e.stop()}}),e,null,[[0,10]])})));return function(t){return e.apply(this,arguments)}}(),W=function(){var e=(0,r.A)(a().mark((function e(t,n){var r;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,E.post("/auth/change-password",{currentPassword:t,newPassword:n});case 3:return r=e.sent,e.abrupt("return",{success:r.success,error:r.error});case 7:return e.prev=7,e.t0=e.catch(0),console.error("Change password error:",e.t0),e.abrupt("return",{success:!1,error:e.t0.message||"Change password failed"});case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t,n){return e.apply(this,arguments)}}()},4941:(e,t,n)=>{var r,o,a,i,s,c,l,u=n(6540),p=n(5338),d=n(1468),m=n(8035),h=n(4976),f=n(7767),g=n(7528),v=n(1250),y=n(3385),E=v.Ay.div(r||(r=(0,g.A)(["\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: var(--background-color);\n  color: var(--text-color);\n  transition: background-color 0.3s ease, color 0.3s ease;\n"]))),A=v.Ay.header(o||(o=(0,g.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 20px;\n  height: 64px;\n  background-color: var(--background-secondary, #f5f5f5);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n"]))),b=v.Ay.div(a||(a=(0,g.A)(["\n  font-size: 1.5rem;\n  font-weight: bold;\n\n  a {\n    color: var(--primary-color);\n    text-decoration: none;\n\n    &:hover {\n      text-decoration: none;\n      opacity: 0.9;\n    }\n  }\n"]))),_=v.Ay.nav(i||(i=(0,g.A)(["\n  ul {\n    display: flex;\n    list-style: none;\n    margin: 0;\n    padding: 0;\n\n    li {\n      margin-left: 20px;\n\n      a {\n        color: var(--text-color);\n        text-decoration: none;\n        padding: 8px 12px;\n        border-radius: 4px;\n        transition: background-color 0.3s ease;\n\n        &:hover {\n          background-color: rgba(0, 0, 0, 0.05);\n        }\n\n        &.active {\n          color: var(--primary-color);\n          font-weight: 500;\n        }\n      }\n    }\n  }\n"]))),S=v.Ay.main(s||(s=(0,g.A)(["\n  flex: 1;\n  padding: 20px;\n"]))),k=v.Ay.footer(c||(c=(0,g.A)(["\n  padding: 20px;\n  text-align: center;\n  background-color: var(--background-secondary, #f5f5f5);\n  border-top: 1px solid var(--border-color, #e8e8e8);\n"]))),C=v.Ay.div(l||(l=(0,g.A)(["\n  display: flex;\n  align-items: center;\n"])));const O=function(e){var t=e.children;return u.createElement(E,{className:"app-layout"},u.createElement(A,{className:"app-header"},u.createElement(b,{className:"logo"},u.createElement(h.N_,{to:"/"},"App Builder")),u.createElement(_,{className:"main-nav"},u.createElement("ul",null,u.createElement("li",null,u.createElement(h.N_,{to:"/"},"Home")),u.createElement("li",null,u.createElement(h.N_,{to:"/app-builder"},"App Builder")),u.createElement("li",null,u.createElement(h.N_,{to:"/websocket"},"WebSocket")))),u.createElement(C,null,u.createElement(y.HP,null))),u.createElement(S,{className:"app-main"},t),u.createElement(k,{className:"app-footer"},u.createElement("p",null,"© ",(new Date).getFullYear()," App Builder")))};var w=n(4467),P=n(9029),T=n(3567);function x(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function D(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?x(Object(n),!0).forEach((function(t){(0,w.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):x(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const R=function(e){var t=e.tip,n=void 0===t?"Loading...":t,r=e.size,o=void 0===r?"large":r,a=e.fullScreen,i=void 0!==a&&a,s=e.backgroundColor,c=void 0===s?"rgba(255, 255, 255, 0.8)":s,l=e.icon,p=void 0===l?null:l,d=e.className,m=void 0===d?"":d,h=e.style,f=void 0===h?{}:h,g=p||u.createElement(T.A,{style:{fontSize:"large"===o?40:24},spin:!0});return i?u.createElement("div",{className:"loading-container ".concat(m),style:D({backgroundColor:c},f),"aria-live":"polite","aria-busy":"true"},u.createElement("div",{className:"loading-content"},u.createElement(P.A,{className:"loading-spinner",indicator:g,size:o}),n&&u.createElement("div",{className:"loading-text",role:"status"},n))):u.createElement("div",{style:D({textAlign:"center",padding:"20px"},f),className:m,"aria-live":"polite","aria-busy":"true"},u.createElement(P.A,{indicator:g,size:o}),n&&u.createElement("div",{style:{marginTop:"12px"},role:"status"},n))};var I=n(5544),N=n(467),L=n(3029),W=n(2901),F=n(4756),U=n.n(F),M=n(1083),j="app_builder_auth_token",G="app_builder_user",H=function(){return(0,W.A)((function e(){(0,L.A)(this,e),this.token=localStorage.getItem(j),this.user=JSON.parse(localStorage.getItem(G)||"null"),this.listeners=[],this.initInterceptors()}),[{key:"initInterceptors",value:function(){var e=this;M.A.interceptors.request.use((function(t){return e.token&&(t.headers.Authorization="Bearer ".concat(e.token)),t}),(function(e){return Promise.reject(e)})),M.A.interceptors.response.use((function(e){return e}),(function(t){return t.response&&401===t.response.status&&e.logout(),Promise.reject(t)}))}},{key:"login",value:(t=(0,N.A)(U().mark((function e(t,n){var r,o,a,i;return U().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,M.A.post("/api/auth/login",{email:t,password:n});case 3:return r=e.sent,o=r.data,a=o.token,i=o.user,this.setToken(a),this.setUser(i),this.notifyListeners("login",i),e.abrupt("return",i);case 11:throw e.prev=11,e.t0=e.catch(0),console.error("Login error:",e.t0),e.t0;case 15:case"end":return e.stop()}}),e,this,[[0,11]])}))),function(e,n){return t.apply(this,arguments)})},{key:"register",value:(e=(0,N.A)(U().mark((function e(t,n,r){var o,a,i,s;return U().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,M.A.post("/api/auth/register",{name:t,email:n,password:r});case 3:return o=e.sent,a=o.data,i=a.token,s=a.user,this.setToken(i),this.setUser(s),this.notifyListeners("register",s),e.abrupt("return",s);case 11:throw e.prev=11,e.t0=e.catch(0),console.error("Register error:",e.t0),e.t0;case 15:case"end":return e.stop()}}),e,this,[[0,11]])}))),function(t,n,r){return e.apply(this,arguments)})},{key:"logout",value:function(){this.setToken(null),this.setUser(null),this.notifyListeners("logout")}},{key:"getUser",value:function(){return this.user}},{key:"isAuthenticated",value:function(){return!!this.token}},{key:"setToken",value:function(e){this.token=e,e?localStorage.setItem(j,e):localStorage.removeItem(j)}},{key:"setUser",value:function(e){this.user=e,e?localStorage.setItem(G,JSON.stringify(e)):localStorage.removeItem(G)}},{key:"addListener",value:function(e){var t=this;return this.listeners.push(e),function(){t.listeners=t.listeners.filter((function(t){return t!==e}))}}},{key:"notifyListeners",value:function(e,t){this.listeners.forEach((function(n){try{n(e,t)}catch(e){console.error("Error in auth listener:",e)}}))}}]);var e,t}();const z=new H;var B,V=n(3016),q=n(7197),Q=n(2702),K=V.A.Text,J=v.Ay.div(B||(B=(0,g.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  width: 100%;\n  position: fixed;\n  top: 0;\n  left: 0;\n  background: rgba(255, 255, 255, 0.95);\n  z-index: 1000;\n"])));const Y=function(e){var t=e.loading,n=void 0===t||t,r=e.message,o=void 0===r?"Loading...":r,a=e.description,i=void 0===a?"":a,s=e.size,c=void 0===s?"default":s,l=e.fullPage,p=void 0!==l&&l,d=e.children,m=void 0===d?null:d,h=e.error,f=void 0===h?null:h,g=e.retry,v=void 0===g?null:g;if(!n&&!f)return u.createElement(u.Fragment,null,m);var y=u.createElement(T.A,{style:{fontSize:"large"===c?40:24},spin:!0}),E=f?u.createElement(q.A,{message:f.title||"Error",description:u.createElement(Q.A,{direction:"vertical"},u.createElement(K,null,f.message||"An error occurred"),v&&u.createElement("a",{onClick:v},"Try again")),type:"error",showIcon:!0}):u.createElement(P.A,{indicator:y,size:c},u.createElement("div",{style:{padding:o?"30px":"50px"}},o&&u.createElement("div",{style:{marginTop:8}},o),i&&u.createElement("div",{style:{marginTop:4,color:"rgba(0, 0, 0, 0.45)"}},i)));return p?u.createElement(J,null,E):E},X=function(e){var t=e.children,n=e.redirectTo,r=void 0===n?"/login":n,o=e.roles,a=void 0===o?[]:o,i=e.loadingFallback,s=void 0===i?null:i,c=(0,u.useState)(!0),l=(0,I.A)(c,2),p=l[0],d=l[1],m=(0,u.useState)(!1),h=(0,I.A)(m,2),g=h[0],v=h[1],y=(0,u.useState)(!0),E=(0,I.A)(y,2),A=E[0],b=E[1];if((0,u.useEffect)((function(){var e=z.isAuthenticated();if(v(e),e&&a.length>0){var t=z.getUser(),n=(null==t?void 0:t.roles)||[],r=a.some((function(e){return n.includes(e)}));b(r)}d(!1)}),[a]),(0,u.useEffect)((function(){return z.addListener((function(e){if("login"===e||"register"===e){if(v(!0),a.length>0){var t=z.getUser(),n=(null==t?void 0:t.roles)||[],r=a.some((function(e){return n.includes(e)}));b(r)}}else"logout"===e&&(v(!1),b(!1))}))}),[a]),p)return s||u.createElement(Y,{loading:!0,message:"Checking authentication...",fullPage:!0});if(!g){var _="".concat(r,"?redirect=").concat(encodeURIComponent(window.location.pathname));return u.createElement(f.C5,{to:_,replace:!0})}return A?t:u.createElement(f.C5,{to:"/unauthorized",replace:!0})};var $=n(9391),Z=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(76),n.e(913)]).then(n.bind(n,3913))})),ee=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(548)]).then(n.bind(n,3548))})),te=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(430)]).then(n.bind(n,3430))})),ne=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(76),n.e(498)]).then(n.bind(n,2498))})),re=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(76),n.e(602)]).then(n.bind(n,1602))})),oe=(0,u.lazy)((function(){return n.e(253).then(n.bind(n,253))})),ae=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(368)]).then(n.bind(n,368))})),ie=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(67)]).then(n.bind(n,4067))})),se=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(76),n.e(831)]).then(n.bind(n,5831))})),ce=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(167)]).then(n.bind(n,6548))})),le=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(552)]).then(n.bind(n,8552))})),ue=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(76),n.e(435)]).then(n.bind(n,7435))})),pe=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(730)]).then(n.bind(n,3730))})),de=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(76),n.e(934)]).then(n.bind(n,1934))})),me=(0,u.lazy)((function(){return n.e(375).then(n.bind(n,1375))})),he=(0,u.lazy)((function(){return Promise.all([n.e(76),n.e(905)]).then(n.bind(n,4905))})),fe=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(76),n.e(177)]).then(n.bind(n,1177))})),ge=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(76),n.e(160)]).then(n.bind(n,1160))})),ve=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(76),n.e(553)]).then(n.bind(n,2553))})),ye=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(772)]).then(n.bind(n,7772))})),Ee=function(){return u.createElement(R,{tip:"Loading page...",fullScreen:!0,backgroundColor:"rgba(255, 255, 255, 0.9)"})};const Ae=function(){var e=(0,$.As)().isAuthenticated;return u.createElement(O,null,u.createElement(u.Suspense,{fallback:u.createElement(Ee,null)},u.createElement(f.BV,null,u.createElement(f.qh,{path:"/",element:u.createElement(ee,null)}),u.createElement(f.qh,{path:"/home",element:u.createElement(Z,null)}),u.createElement(f.qh,{path:"/home-mvp",element:u.createElement(ee,null)}),u.createElement(f.qh,{path:"/mvp",element:u.createElement(te,null)}),u.createElement(f.qh,{path:"/login",element:e?u.createElement(f.C5,{to:"/dashboard"}):u.createElement(ie,null)}),u.createElement(f.qh,{path:"/register",element:e?u.createElement(f.C5,{to:"/dashboard"}):u.createElement(se,null)}),u.createElement(f.qh,{path:"/forgot-password",element:e?u.createElement(f.C5,{to:"/dashboard"}):u.createElement(ce,null)}),u.createElement(f.qh,{path:"/reset-password/:token",element:e?u.createElement(f.C5,{to:"/dashboard"}):u.createElement(le,null)}),u.createElement(f.qh,{path:"/dashboard",element:u.createElement(X,null,u.createElement(de,null))}),u.createElement(f.qh,{path:"/app-builder",element:u.createElement(X,null,u.createElement(ne,null))}),u.createElement(f.qh,{path:"/websocket",element:u.createElement(X,null,u.createElement(re,null))}),u.createElement(f.qh,{path:"/profile",element:u.createElement(X,null,u.createElement(ue,null))}),u.createElement(f.qh,{path:"/projects",element:u.createElement(X,null,u.createElement(pe,null))}),u.createElement(f.qh,{path:"/settings",element:u.createElement(X,null,u.createElement(me,null))}),u.createElement(f.qh,{path:"/theme-test",element:u.createElement(he,null)}),u.createElement(f.qh,{path:"/dark-mode-test",element:u.createElement(fe,null)}),u.createElement(f.qh,{path:"/contrast-test",element:u.createElement(ge,null)}),u.createElement(f.qh,{path:"/header-contrast-test",element:u.createElement(ve,null)}),u.createElement(f.qh,{path:"/service-worker-test",element:u.createElement(ye,null)}),u.createElement(f.qh,{path:"/unauthorized",element:u.createElement(ae,null)}),u.createElement(f.qh,{path:"/404",element:u.createElement(oe,null)}),u.createElement(f.qh,{path:"*",element:u.createElement(f.C5,{to:"/404"})}))))};var be=n(6822),_e=n(3954),Se=n(5501),ke=n(5556),Ce=n.n(ke),Oe=n(6020),we=n(3587),Pe=n(378),Te=n(6893),xe=n(581),De=n(6191),Re=n(436);function Ie(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ne(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ie(Object(n),!0).forEach((function(t){(0,w.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ie(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Le={enabled:!0,samplingRate:1,errorLimit:100,breadcrumbLimit:50,ignoredErrors:[/ResizeObserver loop limit exceeded/,/Loading chunk \d+ failed/,/Network request failed/,/Script error/,/Extension context invalidated/,/Failed to report error/,/Error reporting failed/,/TypeError: Failed to fetch/,/TypeError: NetworkError when attempting to fetch resource/,/AbortError/,/Request aborted/,/Request timed out/,/Load failed/],reportingEndpoint:"/api/errors",logToConsole:!0,captureConsoleErrors:!0,captureNetworkErrors:!0,captureUnhandledRejections:!0,captureBreadcrumbs:!0},We={state:"CLOSED",failureCount:0,lastFailureTime:null,failureThreshold:5,timeout:6e4,successThreshold:2},Fe={errors:[],breadcrumbs:[],sessionId:"".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),startTime:(new Date).toISOString(),reportingQueue:[],lastReportAttempt:null,reportingInProgress:!1};function Ue(e){e.preventDefault(),je({type:"uncaught_error",message:e.message||"Unknown error",stack:e.error?e.error.stack:null,source:e.filename,line:e.lineno,column:e.colno,timestamp:(new Date).toISOString()}),Le.logToConsole&&console.error("Uncaught error:",e.message)}function Me(e){e.preventDefault();var t=e.reason,n=t instanceof Error?t.message:String(t);je({type:"unhandled_rejection",message:n||"Unhandled promise rejection",stack:t instanceof Error?t.stack:null,timestamp:(new Date).toISOString()}),Le.logToConsole&&console.error("Unhandled rejection:",n)}function je(e){Le.enabled&&(Math.random()>Le.samplingRate||function(e){return Le.ignoredErrors.some((function(t){return t instanceof RegExp?t.test(e.message):e.message.includes(t)}))}(e)||(e.sessionId=Fe.sessionId,e.userAgent=navigator.userAgent,e.url=window.location.href,e.breadcrumbs=(0,Re.A)(Fe.breadcrumbs),Fe.errors.push(e),Fe.errors.length>Le.errorLimit&&Fe.errors.shift(),He(e)))}function Ge(e){Le.captureBreadcrumbs&&(Fe.breadcrumbs.push(e),Fe.breadcrumbs.length>Le.breadcrumbLimit&&Fe.breadcrumbs.shift())}function He(e){var t,n,r,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3;if(Le.reportingEndpoint)if(r=Date.now(),"OPEN"!==We.state||r-We.lastFailureTime>=We.timeout&&(We.state="HALF_OPEN",1)){if(!("error_reporting_failure"===e.type||null!==(t=e.message)&&void 0!==t&&t.includes("Failed to report error")||null!==(n=e.url)&&void 0!==n&&n.includes(Le.reportingEndpoint)))if(Fe.reportingInProgress)Fe.reportingQueue.push(e);else{Fe.reportingInProgress=!0,Fe.lastReportAttempt=Date.now();var i=Math.min(1e3*Math.pow(2,o),3e4),s=function(){(window._originalFetch||window.fetch)(Le.reportingEndpoint,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e),keepalive:!0}).then((function(e){if(!e.ok)throw new Error("HTTP ".concat(e.status,": ").concat(e.statusText));if("HALF_OPEN"===We.state?(We.successThreshold--,We.successThreshold<=0&&(We.state="CLOSED",We.failureCount=0,We.successThreshold=2)):"CLOSED"===We.state&&(We.failureCount=0),"CLOSED"===We.state&&Fe.reportingQueue.length>0){var t=Fe.reportingQueue.shift();setTimeout((function(){return He(t)}),100)}})).catch((function(t){We.failureCount++,We.lastFailureTime=Date.now(),We.failureCount>=We.failureThreshold&&(We.state="OPEN"),o<a?setTimeout((function(){He(e,o+1,a)}),i):(function(e){try{var t="errorTracker_failedReports",n=localStorage.getItem(t),r=n?JSON.parse(n):[];r.push(Ne(Ne({},e),{},{storedAt:(new Date).toISOString()})),r.length>50&&r.splice(0,r.length-50),localStorage.setItem(t,JSON.stringify(r))}catch(e){Le.logToConsole}}(e),Le.logToConsole)})).finally((function(){Fe.reportingInProgress=!1}))};o>0?setTimeout(s,i):s()}}else Fe.reportingQueue.push(e)}function ze(){return{trackError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};je(e instanceof Error?Ne(Ne({type:"manual",message:e.message,stack:e.stack},t),{},{timestamp:(new Date).toISOString()}):Ne(Ne({type:"manual",message:String(e)},t),{},{timestamp:(new Date).toISOString()}))},addBreadcrumb:function(e){Ge({type:"manual",category:arguments.length>1&&void 0!==arguments[1]?arguments[1]:"manual",message:e,data:arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},timestamp:(new Date).toISOString()})},getErrors:function(){return(0,Re.A)(Fe.errors)},getBreadcrumbs:function(){return(0,Re.A)(Fe.breadcrumbs)},clearErrors:function(){Fe.errors=[]},clearBreadcrumbs:function(){Fe.breadcrumbs=[]},getConfig:function(){return Ne({},Le)},updateConfig:function(e){Object.assign(Le,e)},getCircuitBreakerStatus:function(){return{state:We.state,failureCount:We.failureCount,lastFailureTime:We.lastFailureTime,queueLength:Fe.reportingQueue.length}},retryQueuedErrors:function(){return new Promise((function(e){0!==Fe.reportingQueue.length?("OPEN"===We.state&&(We.state="HALF_OPEN"),He(Fe.reportingQueue.shift()),setTimeout(e,1e3)):e()}))},getLocallyStoredErrors:function(){try{var e=localStorage.getItem("errorTracker_failedReports");return e?JSON.parse(e):[]}catch(e){return[]}},clearLocallyStoredErrors:function(){try{localStorage.removeItem("errorTracker_failedReports")}catch(e){}}}}const Be=function(){var e,t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.assign(Le,n),Le.enabled?(window.addEventListener("error",Ue),window.addEventListener("unhandledrejection",Me),Le.captureConsoleErrors&&(e=console.error,t=console.warn,console.error=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];e.apply(console,n),je({type:"console_error",message:n.map((function(e){return"string"==typeof e?e:JSON.stringify(e)})).join(" "),timestamp:(new Date).toISOString()})},console.warn=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];t.apply(console,n),Le.captureBreadcrumbs&&Ge({type:"console_warn",message:n.map((function(e){return"string"==typeof e?e:JSON.stringify(e)})).join(" "),timestamp:(new Date).toISOString()})}),Le.captureNetworkErrors&&function(){window._originalFetch||(window._originalFetch=window.fetch);var e=window._originalFetch;window.fetch=(0,N.A)(U().mark((function t(){var n,r,o,a,i,s,c,l=arguments;return U().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:for(n=l.length,r=new Array(n),o=0;o<n;o++)r[o]=l[o];return t.prev=1,t.next=4,e.apply(window,r);case 4:return a=t.sent,Le.captureBreadcrumbs&&Ge({type:"network",category:"fetch",data:{url:"string"==typeof r[0]?r[0]:r[0].url,method:(null===(i=r[1])||void 0===i?void 0:i.method)||"GET",status:a.status},timestamp:(new Date).toISOString()}),a.ok||je({type:"network_error",message:"Fetch error: ".concat(a.status," ").concat(a.statusText),data:{url:"string"==typeof r[0]?r[0]:r[0].url,method:(null===(s=r[1])||void 0===s?void 0:s.method)||"GET",status:a.status,statusText:a.statusText},timestamp:(new Date).toISOString()}),t.abrupt("return",a);case 10:throw t.prev=10,t.t0=t.catch(1),je({type:"network_error",message:"Fetch failed: ".concat(t.t0.message),stack:t.t0.stack,data:{url:"string"==typeof r[0]?r[0]:null===(c=r[0])||void 0===c?void 0:c.url},timestamp:(new Date).toISOString()}),t.t0;case 14:case"end":return t.stop()}}),t,null,[[1,10]])})));var t=XMLHttpRequest.prototype.open,n=XMLHttpRequest.prototype.send;XMLHttpRequest.prototype.open=function(e,n){return this._errorTracking={method:e,url:n},t.apply(this,arguments)},XMLHttpRequest.prototype.send=function(){return this.addEventListener("load",(function(){var e,t,n,r;Le.captureBreadcrumbs&&Ge({type:"network",category:"xhr",data:{url:null===(e=this._errorTracking)||void 0===e?void 0:e.url,method:null===(t=this._errorTracking)||void 0===t?void 0:t.method,status:this.status},timestamp:(new Date).toISOString()}),this.status>=400&&je({type:"network_error",message:"XHR error: ".concat(this.status," ").concat(this.statusText),data:{url:null===(n=this._errorTracking)||void 0===n?void 0:n.url,method:null===(r=this._errorTracking)||void 0===r?void 0:r.method,status:this.status,statusText:this.statusText},timestamp:(new Date).toISOString()})})),this.addEventListener("error",(function(){var e,t;je({type:"network_error",message:"XHR failed",data:{url:null===(e=this._errorTracking)||void 0===e?void 0:e.url,method:null===(t=this._errorTracking)||void 0===t?void 0:t.method},timestamp:(new Date).toISOString()})})),n.apply(this,arguments)}}(),Le.captureBreadcrumbs&&(document.addEventListener("click",(function(e){var t,n=e.target,r=n.tagName.toLowerCase(),o=n.id?"#".concat(n.id):"",a=Array.from(n.classList).map((function(e){return".".concat(e)})).join(""),i=null===(t=n.innerText)||void 0===t?void 0:t.substring(0,50);Ge({type:"user",category:"click",data:{element:"".concat(r).concat(o).concat(a),text:i},timestamp:(new Date).toISOString()})})),window.addEventListener("popstate",(function(){Ge({type:"navigation",data:{from:document.referrer,to:window.location.href},timestamp:(new Date).toISOString()})}))),console.log("Error tracking initialized"),ze()):(console.log("Error tracking is disabled"),ze())}();var Ve,qe,Qe,Ke,Je,Ye;function Xe(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Xe=function(){return!!e})()}var $e=v.Ay.div(Ve||(Ve=(0,g.A)(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ",";\n  background-color: ",";\n  border-radius: ",";\n  box-shadow: ",";\n  max-width: 800px;\n  margin: 0 auto;\n  text-align: center;\n"])),Oe.Ay.spacing[6],Oe.Ay.colors.neutral[100],Oe.Ay.borderRadius.lg,Oe.Ay.shadows.md),Ze=v.Ay.div(qe||(qe=(0,g.A)(["\n  font-size: 48px;\n  color: ",";\n  margin-bottom: ",";\n"])),Oe.Ay.colors.error.main,Oe.Ay.spacing[4]),et=v.Ay.h2(Qe||(Qe=(0,g.A)(["\n  font-size: ",";\n  color: ",";\n  margin-bottom: ",";\n"])),Oe.Ay.typography.fontSize.xl,Oe.Ay.colors.neutral[900],Oe.Ay.spacing[3]),tt=v.Ay.p(Ke||(Ke=(0,g.A)(["\n  font-size: ",";\n  color: ",";\n  margin-bottom: ",";\n"])),Oe.Ay.typography.fontSize.md,Oe.Ay.colors.neutral[700],Oe.Ay.spacing[4]),nt=v.Ay.div(Je||(Je=(0,g.A)(["\n  background-color: ",";\n  padding: ",";\n  border-radius: ",";\n  margin-bottom: ",";\n  text-align: left;\n  overflow: auto;\n  max-height: 200px;\n  width: 100%;\n  font-family: ",";\n  font-size: ",";\n"])),Oe.Ay.colors.neutral[200],Oe.Ay.spacing[3],Oe.Ay.borderRadius.md,Oe.Ay.spacing[4],Oe.Ay.typography.fontFamily.code,Oe.Ay.typography.fontSize.sm),rt=v.Ay.div(Ye||(Ye=(0,g.A)(["\n  display: flex;\n  gap: ",";\n  margin-top: ",";\n"])),Oe.Ay.spacing[3],Oe.Ay.spacing[4]),ot=function(e){function t(e){var n,r,o,a;return(0,L.A)(this,t),r=this,o=t,a=[e],o=(0,_e.A)(o),n=(0,be.A)(r,Xe()?Reflect.construct(o,a||[],(0,_e.A)(r).constructor):o.apply(r,a)),(0,w.A)(n,"handleReload",(function(){window.location.reload()})),(0,w.A)(n,"handleReset",(function(){n.setState({hasError:!1,error:null,errorInfo:null})})),(0,w.A)(n,"handleGoHome",(function(){window.location.href="/"})),n.state={hasError:!1,error:null,errorInfo:null,errorCount:0},n}return(0,Se.A)(t,e),(0,W.A)(t,[{key:"componentDidCatch",value:function(e,t){console.error("Error caught by ErrorBoundary:",e,t),Be.trackError(e,{componentStack:t.componentStack,source:"react_error_boundary",component:this.constructor.name,props:JSON.stringify(this.props)}),Be.addBreadcrumb("Error in component: ".concat(e.message),"error_boundary",{componentStack:t.componentStack}),this.setState((function(e){return{errorInfo:t,errorCount:e.errorCount+1}})),this.props.onError&&this.props.onError(e,t)}},{key:"render",value:function(){var e=this.state,t=e.hasError,n=e.error,r=e.errorInfo,o=e.errorCount,a=this.props,i=a.fallback,s=a.children;return t?i?i(n,r,this.handleReset):u.createElement($e,null,u.createElement(Ze,null,u.createElement(Pe.A,null)),u.createElement(et,null,"Something went wrong"),u.createElement(tt,null,"We're sorry, but an error occurred while rendering this component.",o>1&&u.createElement("div",{style:{marginTop:Oe.Ay.spacing[2],color:Oe.Ay.colors.error.main}},"Multiple errors detected (",o,"). You may need to reload the page.")),n&&u.createElement(nt,null,u.createElement("strong",null,"Error:")," ",n.toString(),r&&u.createElement("div",{style:{marginTop:Oe.Ay.spacing[2]}},u.createElement("strong",null,"Component Stack:"),u.createElement("pre",null,r.componentStack))),u.createElement(rt,null,u.createElement(we.$n,{onClick:this.handleReset,icon:u.createElement(Te.A,null),variant:"outline"},"Try Again"),u.createElement(we.$n,{onClick:this.handleReload,icon:u.createElement(xe.A,null)},"Reload Page"),u.createElement(we.$n,{onClick:this.handleGoHome,icon:u.createElement(De.A,null),variant:"outline"},"Go to Home"))):s}}],[{key:"getDerivedStateFromError",value:function(e){return{hasError:!0,error:e}}}])}(u.Component);ot.propTypes={children:Ce().node.isRequired,fallback:Ce().func,onError:Ce().func};const at=ot;var it=n(7362),st=document.createElement("style");st.textContent="\n  body {\n    margin: 0;\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n      sans-serif;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n\n  * {\n    box-sizing: border-box;\n  }\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n",document.head.appendChild(st);var ct=document.getElementById("root");if(!ct)throw new Error("Root element not found");var lt=(0,p.H)(ct),ut=function(){return u.createElement(d.Kq,{store:m.A},u.createElement($.OJ,null,u.createElement(at,null,u.createElement(y.NP,{initialTheme:"light"},u.createElement(h.Kd,null,u.createElement(Ae,null))))))};try{console.log("🚀 Starting App Builder 201..."),lt.render(u.createElement(u.StrictMode,null,u.createElement(ut,null))),console.log("✅ App Builder 201 loaded successfully!"),(0,it.kz)({onSuccess:function(e){console.log("✅ Service Worker registered successfully:",e)},onUpdate:function(e){console.log("🔄 Service Worker updated:",e)},onWaiting:function(){console.log("⏳ Service Worker waiting for activation")}})}catch(e){console.error("❌ Failed to load App Builder 201:",e),lt.render(u.createElement("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100vh",fontFamily:"Arial, sans-serif",background:"#f8fafc",color:"#1f2937",textAlign:"center",padding:"2rem"}},u.createElement("div",{style:{background:"white",padding:"2rem",borderRadius:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.1)",maxWidth:"500px"}},u.createElement("h1",{style:{margin:"0 0 1rem",color:"#dc2626"}},"App Loading Error"),u.createElement("p",{style:{margin:"0 0 1rem"}},"There was an error loading the App Builder application."),u.createElement("button",{onClick:function(){return window.location.reload()},style:{background:"#3b82f6",color:"white",border:"none",padding:"0.75rem 1.5rem",borderRadius:"6px",cursor:"pointer",fontSize:"1rem"}},"Reload Page"))))}},6020:(e,t,n)=>{n.d(t,{Ay:()=>o});var r={xs:"0px",sm:"640px",md:"768px",lg:"1024px",xl:"1280px","2xl":"1536px"};const o={colors:{primary:{main:"#2563EB",light:"#DBEAFE",dark:"#1E40AF",contrastText:"#FFFFFF"},secondary:{main:"#10B981",light:"#D1FAE5",dark:"#047857",contrastText:"#FFFFFF"},accent:{main:"#8B5CF6",light:"#EDE9FE",dark:"#6D28D9",contrastText:"#FFFFFF"},neutral:{50:"#F9FAFB",100:"#F3F4F6",200:"#E5E7EB",300:"#D1D5DB",400:"#9CA3AF",500:"#6B7280",600:"#4B5563",700:"#374151",800:"#1F2937",900:"#111827"},success:{main:"#10B981",light:"#D1FAE5",dark:"#047857",contrastText:"#FFFFFF"},warning:{main:"#F59E0B",light:"#FEF3C7",dark:"#B45309",contrastText:"#FFFFFF"},error:{main:"#EF4444",light:"#FEE2E2",dark:"#B91C1C",contrastText:"#FFFFFF"},info:{main:"#3B82F6",light:"#DBEAFE",dark:"#1D4ED8",contrastText:"#FFFFFF"}},typography:{fontFamily:{primary:'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',code:'"Fira Code", "Roboto Mono", "Courier New", monospace'},fontWeight:{light:300,regular:400,medium:500,semibold:600,bold:700},fontSize:{xs:"0.75rem",sm:"0.875rem",md:"1rem",lg:"1.125rem",xl:"1.25rem","2xl":"1.5rem","3xl":"1.875rem","4xl":"2.25rem","5xl":"3rem"},lineHeight:{none:1,tight:1.25,snug:1.375,normal:1.5,relaxed:1.625,loose:2},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0",wide:"0.025em",wider:"0.05em",widest:"0.1em"}},spacing:{0:"0",1:"0.25rem",2:"0.5rem",3:"0.75rem",4:"1rem",5:"1.25rem",6:"1.5rem",8:"2rem",10:"2.5rem",12:"3rem",16:"4rem",20:"5rem",24:"6rem",32:"8rem",40:"10rem",48:"12rem",56:"14rem",64:"16rem"},shadows:{none:"none",sm:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",md:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",lg:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",xl:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)","2xl":"0 25px 50px -12px rgba(0, 0, 0, 0.25)",inner:"inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)"},borderRadius:{none:"0",sm:"0.125rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},transitions:{default:"all 0.2s ease-in-out",fast:"all 0.1s ease-in-out",slow:"all 0.3s ease-in-out"},zIndex:{negative:-1,0:0,10:10,20:20,30:30,40:40,50:50,auto:"auto",dropdown:1e3,sticky:1100,modal:1300,popover:1400,tooltip:1500},breakpoints:r,mediaQueries:{sm:"@media (min-width: ".concat(r.sm,")"),md:"@media (min-width: ".concat(r.md,")"),lg:"@media (min-width: ".concat(r.lg,")"),xl:"@media (min-width: ".concat(r.xl,")"),"2xl":"@media (min-width: ".concat(r["2xl"],")")}}},7053:(e,t,n)=>{n.d(t,{A:()=>u});var r=n(436),o=n(2284),a=n(4467),i=n(3029),s=n(2901);function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){(0,a.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const u=new(function(){function e(){(0,i.A)(this,e),this.socket=null,this.connected=!1,this.connecting=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=10,this.reconnectInterval=2e3,this.maxReconnectInterval=3e4,this.reconnectDecay=1.5,this.eventListeners={},this.offlineQueue=[],this.isReconnecting=!1,this.isClosing=!1,this.isSuspended=!1,this.messageQueue=[],this.lastError=null,this.heartbeatInterval=3e4,this.heartbeatTimeoutId=null,this.missedHeartbeats=0,this.maxMissedHeartbeats=3,this.connectionTimeoutId=null,this.debug=!1,this.instance=null}return(0,s.A)(e,[{key:"init",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.options=l({autoConnect:!1,autoReconnect:!0,reconnectInterval:2e3,maxReconnectAttempts:10,debug:!1,queueOfflineMessages:!0},e),this.connected=!1,this.connecting=!1,this.debug=this.options.debug,this.options.autoConnect&&this.connect(),this}},{key:"setReconnectOptions",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.maxReconnectAttempts=e.maxAttempts||this.maxReconnectAttempts,this.reconnectInterval=e.initialDelay||this.reconnectInterval,this.maxReconnectInterval=e.maxDelay||this.maxReconnectInterval,this.reconnectDecay=e.useExponentialBackoff?1.5:1,this}},{key:"configureSecurityOptions",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.securityOptions=l({validateMessages:!0,sanitizeMessages:!0,rateLimiting:{enabled:!0,maxMessagesPerSecond:20,burstSize:50}},e),this}},{key:"configurePerformanceOptions",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.performanceOptions=l({compression:{enabled:!0,threshold:1024,level:6},batchingEnabled:!0,batchInterval:50,maxBatchSize:20,offlineQueueEnabled:!0,offlineStorage:{enabled:!0,persistKey:"websocket_offline_queue"}},e),this}},{key:"isConnected",value:function(){try{return null!==this.socket&&void 0!==this.socket&&this.socket.readyState===WebSocket.OPEN}catch(e){return console.error("Error checking connection status:",e),!1}}},{key:"connect",value:function(e){var t=this;return new Promise((function(n,r){if(t.isConnected())n(t);else if(t.connecting)r(new Error("Connection already in progress"));else{var o;t.connecting=!0,e&&(t.endpoint=e),o=t.url?t.url:"ws://localhost:8765",t.debug&&console.log("Connecting to WebSocket at ".concat(o));try{t.socket=new WebSocket(o),t.socket.onopen=function(e){t.connected=!0,t.connecting=!1,t.reconnectAttempts=0,t.debug&&console.log("WebSocket connection established"),t.startHeartbeat(),t.processOfflineQueue(),t.triggerEvent("open",e),t.triggerEvent("connect",e),n(t)},t.socket.onclose=function(e){t.connected=!1,t.connecting=!1,t.debug&&console.log("WebSocket connection closed: ".concat(e.code," ").concat(e.reason)),t.stopHeartbeat(),t.triggerEvent("close",e),t.triggerEvent("disconnect",e),!t.isClosing&&t.options.autoReconnect&&t.reconnect(),t.isClosing=!1},t.socket.onerror=function(e){console.error("WebSocket error occurred:",e),t.debug&&(console.log("WebSocket readyState:",t.socket.readyState),console.log("Connection URL:",o),console.log("Browser:",navigator.userAgent));var n=t._handleConnectionError(e,t.reconnectAttempts);t.connecting&&(t.connecting=!1,r(n)),t.triggerEvent("error",{originalEvent:e,url:o,timestamp:Date.now(),reconnectAttempts:t.reconnectAttempts,error:n})},t.socket.onmessage=function(e){var n=e.data;try{if("string"==typeof n&&n.startsWith("C")){var r=n.substring(1);n=t._decompressMessage(r)}if("string"==typeof n&&(n=JSON.parse(n)),t.debug&&console.log("WebSocket message received:",n),"pong"===n.type)return t.missedHeartbeats=0,void t.triggerEvent("pong",n);if("batch"===n.type&&Array.isArray(n.messages))return n.messages.forEach((function(e){t.triggerEvent("message",e),e.type&&t.triggerEvent(e.type,e)})),void t.triggerEvent("batch",n);t.triggerEvent("message",n),n.type&&t.triggerEvent(n.type,n)}catch(n){t._handleMessageError(n,e.data)}}}catch(e){t.connecting=!1,t.lastError=e,t.debug&&console.error("Error creating WebSocket:",e),r(e)}}}))}},{key:"disconnect",value:function(){if(this.socket){this.isClosing=!0;try{this.stopHeartbeat(),this.socket.close(1e3,"Normal closure"),this.debug&&console.log("WebSocket disconnected")}catch(e){console.error("Error disconnecting WebSocket:",e)}}}},{key:"close",value:function(){return this.disconnect()}},{key:"reconnect",value:function(){var e=this;if(!this.isReconnecting){if(this.isReconnecting=!0,this.reconnectAttempts>=this.maxReconnectAttempts)return this.debug&&console.log("Maximum reconnect attempts (".concat(this.maxReconnectAttempts,") reached")),this.triggerEvent("max_retries_reached",{attempts:this.reconnectAttempts,max:this.maxReconnectAttempts}),void(this.isReconnecting=!1);this.reconnectAttempts++;var t=this.reconnectInterval*Math.pow(this.reconnectDecay,this.reconnectAttempts-1),n=.8*(t=Math.min(t,this.maxReconnectInterval)),r=1.2*t;t=Math.floor(n+Math.random()*(r-n)),this.debug&&console.log("Reconnecting in ".concat(t,"ms (attempt ").concat(this.reconnectAttempts,"/").concat(this.maxReconnectAttempts,")")),this.triggerEvent("reconnecting",{attempt:this.reconnectAttempts,maxAttempts:this.maxReconnectAttempts,delay:t}),setTimeout((function(){e.isReconnecting=!1,e.connect().catch((function(e){console.error("Reconnection failed:",e)}))}),t)}}},{key:"suspend",value:function(){if(!this.isSuspended)return this.isSuspended=!0,this.debug&&console.log("WebSocket connection suspended"),this.wasPreviouslyConnected=this.isConnected(),this.socket&&(this.isClosing=!0,this.stopHeartbeat(),this.socket.close(1e3,"Connection suspended")),this.triggerEvent("suspended",{timestamp:Date.now(),wasPreviouslyConnected:this.wasPreviouslyConnected}),this}},{key:"resume",value:function(){if(this.isSuspended)return this.isSuspended=!1,this.debug&&console.log("Resuming WebSocket connection"),this.wasPreviouslyConnected&&this.reconnect(),this.triggerEvent("resumed",{timestamp:Date.now(),reconnecting:this.wasPreviouslyConnected}),this}},{key:"handleOnline",value:function(){return this.debug&&console.log("Network is online"),this.wasPreviouslyConnected&&this.reconnect(),this.triggerEvent("network_status_change",{status:"online",timestamp:Date.now()}),this}},{key:"handleOffline",value:function(){return this.debug&&console.log("Network is offline"),this.wasPreviouslyConnected=this.isConnected(),this.triggerEvent("network_status_change",{status:"offline",timestamp:Date.now()}),this}},{key:"send",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(r,a){if(t.isConnected())try{var i,s,c,u,p,d;if(null!==(i=t.securityOptions)&&void 0!==i&&null!==(i=i.rateLimiting)&&void 0!==i&&i.enabled){var m=Date.now();t._rateLimitState||(t._rateLimitState={messageCount:0,windowStart:m,burstCount:0,burstStart:m,queue:[]});var h=t._rateLimitState,f=t.securityOptions.rateLimiting,g=f.maxMessagesPerSecond,v=f.burstSize;if(m-h.windowStart>1e3&&(h.messageCount=0,h.windowStart=m),m-h.burstStart>100&&(h.burstCount=0,h.burstStart=m),h.messageCount>=g||h.burstCount>=v)return n.important?h.queue.unshift({message:e,options:n,timestamp:m}):h.queue.push({message:e,options:n,timestamp:m}),t.triggerEvent("rate_limited",{queueLength:h.queue.length,messageCount:h.messageCount,burstCount:h.burstCount}),h.processingQueue||(h.processingQueue=!0,setTimeout((function(){return t._processRateLimitQueue()}),100)),void r({queued:!0,rateLimited:!0});h.messageCount++,h.burstCount++}var y,E=e;if("object"!==(0,o.A)(E)||E.timestamp||(E=l(l({},E),{},{timestamp:Date.now()})),"object"!==(0,o.A)(E)||E.id||(E=l(l({},E),{},{id:t._generateMessageId()})),null!==(s=t.securityOptions)&&void 0!==s&&s.validateMessages){var A=t._validateMessage(E);if(!A.valid)return void a(new Error("Invalid message: ".concat(A.reason)))}if(null!==(c=t.securityOptions)&&void 0!==c&&c.sanitizeMessages&&(E=t._sanitizeMessage(E)),null!==(u=t.securityOptions)&&void 0!==u&&u.authToken&&(E=l(l({},E),{},{auth:t.securityOptions.authToken})),null!==(p=t.performanceOptions)&&void 0!==p&&p.batchingEnabled&&!n.immediate)return t._addToBatch(E,n),void r({queued:!0,batched:!0});var b=!1;if(null!==(d=t.performanceOptions)&&void 0!==d&&null!==(d=d.compression)&&void 0!==d&&d.enabled&&(n.compress||t.performanceOptions.compression.enabled)){var _="string"==typeof E?E:JSON.stringify(E);if(_.length>t.performanceOptions.compression.threshold)try{y=t._compressMessage(_),b=!0,y="C".concat(y)}catch(e){console.error("Error compressing message:",e),y=_}else y=_}else y="string"==typeof E?E:JSON.stringify(E);t.socket.send(y),t.debug&&console.log("WebSocket message sent".concat(b?" (compressed)":"",":"),E),t.triggerEvent("message_sent",{message:E,compressed:b,size:y.length}),r({sent:!0,compressed:b})}catch(e){console.error("Error sending WebSocket message:",e),a(e)}else t.options.queueOfflineMessages?(t.queueMessage(e,n),r({queued:!0})):a(new Error("WebSocket is not connected"))}))}},{key:"_generateMessageId",value:function(){return"".concat(Date.now(),"-").concat(Math.random().toString(36).substring(2,11))}},{key:"_validateMessage",value:function(e){return e?"object"!==(0,o.A)(e)?{valid:!1,reason:"Message must be an object"}:e.type?{valid:!0}:{valid:!1,reason:"Message must have a type"}:{valid:!1,reason:"Message is empty"}}},{key:"_sanitizeMessage",value:function(e){var t=JSON.parse(JSON.stringify(e)),n=function(e){return"string"==typeof e?e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;").replace(/\//g,"&#x2F;"):e},r=function(e){if(!e||"object"!==(0,o.A)(e))return e;if(Array.isArray(e))return e.map((function(e){return r(e)}));var t={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){var i=e[a];"object"===(0,o.A)(i)&&null!==i?t[a]=r(i):t[a]=n(i)}return t};return r(t)}},{key:"_compressMessage",value:function(e){try{return btoa(e)}catch(t){return console.error("Error compressing message:",t),e}}},{key:"_decompressMessage",value:function(e){try{return atob(e)}catch(t){return console.error("Error decompressing message:",t),e}}},{key:"_addToBatch",value:function(e,t){var n=this;this._batch||(this._batch={messages:[],timer:null}),this._batch.messages.push({message:e,options:t}),this._batch.timer||(this._batch.timer=setTimeout((function(){n._sendBatch()}),this.performanceOptions.batchInterval)),this._batch.messages.length>=this.performanceOptions.maxBatchSize&&(clearTimeout(this._batch.timer),this._batch.timer=null,this._sendBatch())}},{key:"_sendBatch",value:function(){var e=this;if(this._batch&&0!==this._batch.messages.length){var t={type:"batch",messages:this._batch.messages.map((function(e){return e.message})),timestamp:Date.now(),count:this._batch.messages.length},n=this._batch.messages;this._batch.messages=[],this._batch.timer=null,this.send(t,{immediate:!0}).catch((function(t){console.error("Error sending batch:",t),n.forEach((function(t){e.queueMessage(t.message,t.options)}))}))}}},{key:"_processRateLimitQueue",value:function(){var e=this;if(this._rateLimitState&&0!==this._rateLimitState.queue.length){var t=Date.now(),n=this._rateLimitState,r=this.securityOptions.rateLimiting.maxMessagesPerSecond;if(t-n.windowStart>1e3&&(n.messageCount=0,n.windowStart=t),n.messageCount<r){var o=n.queue.shift();this.send(o.message,l(l({},o.options),{},{immediate:!0})).catch((function(e){console.error("Error sending queued message:",e)})),n.messageCount++,n.queue.length>0?setTimeout((function(){return e._processRateLimitQueue()}),50):n.processingQueue=!1}else setTimeout((function(){return e._processRateLimitQueue()}),100)}else this._rateLimitState&&(this._rateLimitState.processingQueue=!1)}},{key:"sendMessage",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.send(e,t)}},{key:"queueMessage",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.debug&&console.log("Queuing message for later delivery:",e),this.offlineQueue.push({message:e,options:t}),this.triggerEvent("message_queued_offline",{message:e,queueLength:this.offlineQueue.length}),this.offlineQueue.length}},{key:"processOfflineQueue",value:function(){var e=this;if(0!==this.offlineQueue.length){this.debug&&console.log("Processing offline queue (".concat(this.offlineQueue.length," messages)"));var t=(0,r.A)(this.offlineQueue);this.offlineQueue=[],t.forEach((function(t){var n=t.message,r=t.options;e.send(n,r).catch((function(e){console.error("Error sending queued message:",e)}))})),this.triggerEvent("offline_queue_processed",{processedCount:t.length})}}},{key:"startHeartbeat",value:function(){var e=this;this.stopHeartbeat(),this.missedHeartbeats=0,this.heartbeatTimeoutId=setInterval((function(){if(e.isConnected()){if(e.missedHeartbeats++,e.missedHeartbeats>=e.maxMissedHeartbeats)return e.debug&&console.warn("Missed ".concat(e.missedHeartbeats," heartbeats, reconnecting...")),e.triggerEvent("heartbeat_timeout",{missed:e.missedHeartbeats,max:e.maxMissedHeartbeats}),void e.reconnect();e.send({type:"ping",timestamp:Date.now()}).catch((function(e){console.error("Error sending heartbeat:",e)}))}else e.stopHeartbeat()}),this.heartbeatInterval)}},{key:"stopHeartbeat",value:function(){this.heartbeatTimeoutId&&(clearInterval(this.heartbeatTimeoutId),this.heartbeatTimeoutId=null)}},{key:"addEventListener",value:function(e,t){return this.eventListeners[e]||(this.eventListeners[e]=[]),this.eventListeners[e].push(t),this}},{key:"removeEventListener",value:function(e,t){return this.eventListeners[e]?(this.eventListeners[e]=this.eventListeners[e].filter((function(e){return e!==t})),this):this}},{key:"triggerEvent",value:function(e,t){this.eventListeners[e]&&this.eventListeners[e].forEach((function(n){try{n(t)}catch(t){console.error("Error in ".concat(e," event listener:"),t)}}))}},{key:"getConnectionState",value:function(){var e="CLOSED";if(this.socket)switch(this.socket.readyState){case WebSocket.CONNECTING:e="CONNECTING";break;case WebSocket.OPEN:e="OPEN";break;case WebSocket.CLOSING:e="CLOSING";break;case WebSocket.CLOSED:e="CLOSED"}return{connected:this.isConnected(),connecting:this.connecting,readyState:this.socket?this.socket.readyState:WebSocket.CLOSED,readyStateText:e,reconnectAttempts:this.reconnectAttempts,maxReconnectAttempts:this.maxReconnectAttempts,url:this.url,lastError:this.lastError}}},{key:"getOfflineQueueStatus",value:function(){return{count:this.offlineQueue.length,enabled:this.options.queueOfflineMessages}}},{key:"clearOfflineQueue",value:function(){var e=this.offlineQueue.length;return this.offlineQueue=[],this.triggerEvent("offline_queue_cleared",{count:e}),e}},{key:"_getDefaultWebSocketUrl",value:function(){var e="https:"===window.location.protocol?"wss:":"ws:",t=window.location.host;return"".concat(e,"//").concat(t,"/ws")}},{key:"_createError",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=new Error(t);return r.type=e,r.details=n,r.timestamp=Date.now(),this.debug&&console.error("WebSocketError [".concat(e,"]: ").concat(t),n),this.triggerEvent("error",r),r}},{key:"_handleConnectionError",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;this.lastError=e;var r="CONNECTION_ERROR",o="Failed to connect to WebSocket server";if(e.code)switch(e.code){case 1e3:r="NORMAL_CLOSURE",o="Connection closed normally";break;case 1001:r="GOING_AWAY",o="Server is going away";break;case 1002:r="PROTOCOL_ERROR",o="Protocol error";break;case 1003:r="UNSUPPORTED_DATA",o="Unsupported data";break;case 1005:r="NO_STATUS",o="No status code was provided";break;case 1006:r="ABNORMAL_CLOSURE",o="Connection closed abnormally";break;case 1007:r="INVALID_FRAME_PAYLOAD",o="Invalid frame payload data";break;case 1008:r="POLICY_VIOLATION",o="Policy violation";break;case 1009:r="MESSAGE_TOO_BIG",o="Message too big";break;case 1010:r="MISSING_EXTENSION",o="Required extension is missing";break;case 1011:r="INTERNAL_ERROR",o="Internal server error";break;case 1012:r="SERVICE_RESTART",o="Service is restarting";break;case 1013:r="TRY_AGAIN_LATER",o="Try again later";break;case 1014:r="BAD_GATEWAY",o="Bad gateway";break;case 1015:r="TLS_HANDSHAKE_FAILURE",o="TLS handshake failure";break;default:r="CODE_".concat(e.code),o="WebSocket error code ".concat(e.code)}else e.message&&(e.message.includes("timeout")?(r="CONNECTION_TIMEOUT",o="Connection timed out"):e.message.includes("refused")?(r="CONNECTION_REFUSED",o="Connection refused"):e.message.includes("ENOTFOUND")&&(r="HOST_NOT_FOUND",o="Host not found"));var a=this._createError(r,o,{originalError:e,attempt:n,url:this.url});switch(r){case"NORMAL_CLOSURE":break;case"GOING_AWAY":case"SERVICE_RESTART":this.options.autoReconnect&&setTimeout((function(){return t.reconnect()}),5e3);break;case"TRY_AGAIN_LATER":this.options.autoReconnect&&setTimeout((function(){return t.reconnect()}),1e4);break;case"CONNECTION_REFUSED":case"HOST_NOT_FOUND":if(this.options.fallbackUrls&&this.options.fallbackUrls.length>0){var i=this.options.fallbackUrls[n%this.options.fallbackUrls.length];this.debug&&console.log("Trying fallback URL: ".concat(i)),this.url=i,this.options.autoReconnect&&setTimeout((function(){return t.reconnect()}),1e3)}else this.options.autoReconnect&&this.reconnect();break;case"ABNORMAL_CLOSURE":case"INTERNAL_ERROR":case"BAD_GATEWAY":if(this.options.autoReconnect){var s=Math.min(1e3*Math.pow(2,n),3e4);setTimeout((function(){return t.reconnect()}),s)}break;default:this.options.autoReconnect&&this.reconnect()}return a}},{key:"_handleMessageError",value:function(e,t){var n="MESSAGE_ERROR",r="Failed to process WebSocket message";return e.message&&(e.message.includes("JSON")?(n="INVALID_JSON",r="Invalid JSON in message"):e.message.includes("timeout")?(n="MESSAGE_TIMEOUT",r="Message processing timed out"):e.message.includes("rate limit")&&(n="RATE_LIMITED",r="Rate limit exceeded")),this._createError(n,r,{originalError:e,message:t})}}],[{key:"getInstance",value:function(t){return e.instance||(e.instance=new e,t&&e.instance.init(t)),e.instance}}])}())},7362:(e,t,n)=>{n.d(t,{hl:()=>p,jY:()=>m,kz:()=>u});var r=n(467),o=n(4756),a=n.n(o),i=n(9730);function s(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return c(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){s=!0,a=e},f:function(){try{i||null==n.return||n.return()}finally{if(s)throw a}}}}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var l=Boolean("localhost"===window.location.hostname||"[::1]"===window.location.hostname||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));function u(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if("serviceWorker"in navigator){try{if("true"===localStorage.getItem("disable_sw_temp"))return console.log("Service worker registration skipped: temporarily disabled"),void localStorage.removeItem("disable_sw_temp")}catch(e){}if(new URL({ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_20856_MNNIPVTQVSYMCLWM",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11772_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.PUBLIC_URL||"",window.location.href).origin!==window.location.origin)return void console.log("Service worker registration skipped: different origin");window.addEventListener("load",(function(){var t="".concat({ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_20856_MNNIPVTQVSYMCLWM",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11772_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.PUBLIC_URL||"","/service-worker.js");console.log("Service Worker: Using ".concat(t)),Array.from(document.querySelectorAll("script")).some((function(e){return e.src&&(e.src.includes("socket.io")||e.src.includes("websocket")||e.src.includes("ws"))}))&&console.log("Service Worker: Detected active WebSocket scripts, registering with caution"),l?(function(e,t){fetch(e,{headers:{"Service-Worker":"script"},cache:"reload"}).then((function(n){var r=n.headers.get("content-type");404===n.status||null!=r&&-1===r.indexOf("javascript")?(console.warn("Service Worker: Invalid service worker detected. Attempting to unregister."),navigator.serviceWorker.getRegistrations().then((function(e){var t,n=s(e);try{for(n.s();!(t=n.n()).done;){var r=t.value;console.log("Unregistering service worker:",r.scope),r.unregister()}}catch(e){n.e(e)}finally{n.f()}window.location.reload()}))):(console.log("Service Worker: Valid service worker found. Registering..."),function(e,t){navigator.serviceWorker.register(e).then((function(e){e.onupdatefound=function(){var n=e.installing;null!=n&&(n.onstatechange=function(){"installed"===n.state&&(navigator.serviceWorker.controller?(console.log("Service Worker: New content is available and will be used when all tabs for this page are closed"),t&&t.onUpdate&&t.onUpdate(e)):(console.log("Service Worker: Content is cached for offline use"),t&&t.onSuccess&&t.onSuccess(e)))})}})).catch((function(e){console.error("Service Worker: Error during registration:",e)}))}(e,t))})).catch((function(e){console.log("Service Worker: No internet connection or error occurred:",e),console.log("App is running in offline mode.")}))}(t,e),navigator.serviceWorker.ready.then((function(){console.log("Service Worker: Ready and running in development mode")}))):function(e,t){try{var n=new i.JK(e);"function"!=typeof n.messageSkipWaiting&&(n.messageSkipWaiting=function(){console.log("Service Worker: Custom messageSkipWaiting called"),n.controlling?n.controlling.postMessage("skipWaiting"):navigator.serviceWorker.ready.then((function(e){e.waiting&&e.waiting.postMessage("skipWaiting")}))}),n.addEventListener("installed",(function(e){if(e.isUpdate){if(console.log("Service Worker: Updated service worker installed"),t&&t.onUpdate)try{t.onUpdate(n)}catch(e){console.error("Service Worker: Error in onUpdate callback:",e)}}else if(console.log("Service Worker: New service worker installed"),t&&t.onSuccess)try{t.onSuccess(n)}catch(e){console.error("Service Worker: Error in onSuccess callback:",e)}})),n.addEventListener("activated",(function(e){e.isUpdate?console.log("Service Worker: Updated service worker activated"):console.log("Service Worker: New service worker activated")})),n.addEventListener("waiting",(function(e){if(console.log("Service Worker: New version waiting to be activated"),t&&t.onWaiting)try{t.onWaiting(n)}catch(e){console.error("Service Worker: Error in onWaiting callback:",e)}})),n.addEventListener("controlling",(function(e){console.log("Service Worker: Controlling the page")})),n.addEventListener("redundant",(function(e){console.warn("Service Worker: The installing service worker became redundant")})),n.addEventListener("error",(function(e){console.error("Service Worker: Error during operation:",e)})),n.register().catch((function(e){console.error("Service Worker: Registration failed:",e)}))}catch(e){console.error("Service Worker: Error during registration:",e)}}(t,e)}))}else console.log("Service Worker: Registration skipped - not supported")}function p(){return d.apply(this,arguments)}function d(){return(d=(0,r.A)(a().mark((function e(){var t,n,r,o,i,c,l,u;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("serviceWorker"in navigator){e.next=2;break}return e.abrupt("return",!1);case 2:return e.prev=2,e.next=5,navigator.serviceWorker.getRegistrations();case 5:if(0!==(t=e.sent).length){e.next=9;break}return console.log("No service workers to clean"),e.abrupt("return",!1);case 9:console.log("Forcefully cleaning ".concat(t.length," service workers")),n=s(t),e.prev=11,n.s();case 13:if((r=n.n()).done){e.next=26;break}return o=r.value,e.prev=15,e.next=18,o.unregister();case 18:console.log("Unregistered service worker:",o.scope),e.next=24;break;case 21:e.prev=21,e.t0=e.catch(15),console.error("Failed to unregister service worker:",e.t0);case 24:e.next=13;break;case 26:e.next=31;break;case 28:e.prev=28,e.t1=e.catch(11),n.e(e.t1);case 31:return e.prev=31,n.f(),e.finish(31);case 34:if(!("caches"in window)){e.next=62;break}return e.prev=35,e.next=38,caches.keys();case 38:i=e.sent,c=s(i),e.prev=40,c.s();case 42:if((l=c.n()).done){e.next=49;break}return u=l.value,e.next=46,caches.delete(u);case 46:console.log("Deleted cache:",u);case 47:e.next=42;break;case 49:e.next=54;break;case 51:e.prev=51,e.t2=e.catch(40),c.e(e.t2);case 54:return e.prev=54,c.f(),e.finish(54);case 57:e.next=62;break;case 59:e.prev=59,e.t3=e.catch(35),console.error("Failed to clear caches:",e.t3);case 62:try{localStorage.setItem("disable_sw_temp","true")}catch(e){}return e.abrupt("return",!0);case 66:return e.prev=66,e.t4=e.catch(2),console.error("Error cleaning service workers:",e.t4),e.abrupt("return",!1);case 70:case"end":return e.stop()}}),e,null,[[2,66],[11,28,31,34],[15,21],[35,59],[40,51,54,57]])})))).apply(this,arguments)}function m(){return"serviceWorker"in navigator?(console.log("Fixing WebSocket issues by checking service workers..."),new Promise((function(e){navigator.serviceWorker.getRegistrations().then(function(){var t=(0,r.A)(a().mark((function t(n){var r,o,i,c,l,u,p;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(n.length>0)){t.next=63;break}console.log("Found ".concat(n.length," service worker registrations that might affect WebSockets")),r=s(n),t.prev=3,r.s();case 5:if((o=r.n()).done){t.next=19;break}return i=o.value,console.log("Unregistering service worker to fix WebSocket issues:",i.scope),t.prev=8,t.next=11,i.unregister();case 11:console.log("Successfully unregistered service worker"),t.next=17;break;case 14:t.prev=14,t.t0=t.catch(8),console.error("Failed to unregister service worker:",t.t0);case 17:t.next=5;break;case 19:t.next=24;break;case 21:t.prev=21,t.t1=t.catch(3),r.e(t.t1);case 24:return t.prev=24,r.f(),t.finish(24);case 27:if(!("caches"in window)){t.next=57;break}return t.prev=28,t.next=31,caches.keys();case 31:c=t.sent,l=s(c),t.prev=33,l.s();case 35:if((u=l.n()).done){t.next=43;break}if(!((p=u.value).includes("api")||p.includes("ws")||p.includes("socket")||p.includes("workbox"))){t.next=41;break}return console.log("Deleting potentially problematic cache:",p),t.next=41,caches.delete(p);case 41:t.next=35;break;case 43:t.next=48;break;case 45:t.prev=45,t.t2=t.catch(33),l.e(t.t2);case 48:return t.prev=48,l.f(),t.finish(48);case 51:console.log("Cache cleanup completed"),t.next=57;break;case 54:t.prev=54,t.t3=t.catch(28),console.error("Error cleaning caches:",t.t3);case 57:try{localStorage.setItem("disable_sw_temp","true"),console.log("Temporarily disabled service worker for next page load")}catch(e){console.error("Failed to set localStorage flag:",e)}console.log("WebSocket issues should be fixed. Reloading page..."),setTimeout((function(){window.location.reload()}),1e3),e(!0),t.next=65;break;case 63:console.log("No service workers found that might affect WebSockets"),e(!1);case 65:case"end":return t.stop()}}),t,null,[[3,21,24,27],[8,14],[28,54],[33,45,48,51]])})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){console.error("Error while fixing WebSocket issues:",t),e(!1)}))}))):Promise.resolve(!1)}},8035:(e,t,n)=>{n.d(t,{A:()=>v});var r=n(9360),o=n(436),a=n(4467),i=n(1616);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){(0,a.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var l={app:{components:[],layouts:[],styles:{},data:{}},websocket:{connected:!1,connecting:!1},loading:!1,error:null,themes:[],activeTheme:"default"};const u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:l,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case i.Q3.WS_CONNECT:return c(c({},e),{},{websocket:c(c({},e.websocket),{},{connecting:!0})});case i.Q3.WS_CONNECTED:return c(c({},e),{},{websocket:{connected:!0,connecting:!1}});case i.Q3.WS_DISCONNECTED:return c(c({},e),{},{websocket:{connected:!1,connecting:!1}});case i.Q3.WS_MESSAGE_RECEIVED:var n=t.payload;return"app_data"===n.type?c(c({},e),{},{app:c(c({},e.app),{},{components:n.data.components||[],layouts:n.data.layouts||[],styles:n.data.styles||{},data:n.data.data||{}})}):e;case i.Q3.ADD_COMPONENT:return c(c({},e),{},{app:c(c({},e.app),{},{components:[].concat((0,o.A)(e.app.components),[t.payload])})});case i.Q3.UPDATE_COMPONENT:return c(c({},e),{},{app:c(c({},e.app),{},{components:e.app.components.map((function(e,n){return n===t.payload.index?c(c({},e),t.payload.updates):e}))})});case i.Q3.DELETE_COMPONENT:return c(c({},e),{},{app:c(c({},e.app),{},{components:e.app.components.filter((function(e,n){return n!==t.payload.index}))})});case i.Q3.ADD_LAYOUT:return c(c({},e),{},{app:c(c({},e.app),{},{layouts:[].concat((0,o.A)(e.app.layouts),[t.payload])})});case i.Q3.UPDATE_LAYOUT:return c(c({},e),{},{app:c(c({},e.app),{},{layouts:e.app.layouts.map((function(e,n){return n===t.payload.index?c(c({},e),t.payload.updates):e}))})});case i.Q3.DELETE_LAYOUT:return c(c({},e),{},{app:c(c({},e.app),{},{layouts:e.app.layouts.filter((function(e,n){return n!==t.payload.index}))})});case i.Q3.SAVE_APP_DATA:return c(c({},e),{},{app:c(c({},e.app),{},{components:t.payload.components||e.app.components,layouts:t.payload.layouts||e.app.layouts,styles:t.payload.styles||e.app.styles,data:t.payload.data||e.app.data})});case i.Q3.LOAD_APP_DATA:return c(c({},e),{},{app:c(c({},e.app),{},{components:t.payload.components||[],layouts:t.payload.layouts||[],styles:t.payload.styles||{},data:t.payload.data||{}})});case i.Q3.SET_LOADING:return c(c({},e),{},{loading:t.payload});case i.Q3.SET_ERROR:return c(c({},e),{},{error:t.payload});case i.Q3.CLEAR_ERROR:return c(c({},e),{},{error:null});case"ADD_THEME":return c(c({},e),{},{themes:[].concat((0,o.A)(e.themes),[t.payload])});case"UPDATE_THEME":return c(c({},e),{},{themes:e.themes.map((function(e){return e.id===t.payload.id?t.payload:e}))});case"REMOVE_THEME":return c(c({},e),{},{themes:e.themes.filter((function(e){return e.id!==t.payload.id}))});case"SET_ACTIVE_THEME":return c(c({},e),{},{activeTheme:t.payload});default:return e}};var p=n(7053),d=n(4318),m=function(){return{type:d.Te}},h=function(e){return{type:d.AS,payload:{error:e}}};function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var g=function(e){return function(t){return function(n){var r=performance.now(),o=t(n),a=performance.now()-r;return"undefined"!=typeof window&&window.dispatchEvent(new CustomEvent("redux-action",{detail:{type:n.type,duration:a,timestamp:(new Date).toISOString(),state:e.getState()}})),o}}};const v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,r.U1)({reducer:u,preloadedState:e,middleware:function(e){return e({serializableCheck:{ignoredActions:["WS_MESSAGE_RECEIVED","WS_ERROR","WEBSOCKET_MESSAGE_RECEIVED","WEBSOCKET_ERROR"],ignoredPaths:["websocket.socket","error.originalError","webSocketClient.socket"]},thunk:!0}).concat((t=!1,function(e){return function(n){return function(r){switch(r.type){case d.YG:if(!t){var o=p.A.getInstance();o.connect().then((function(){t=!0,e.dispatch(m())})).catch((function(t){e.dispatch(h(t))})),o.addEventListener("open",(function(){t=!0,e.dispatch(m())})),o.addEventListener("close",(function(){t=!1,e.dispatch({type:d.RH})})),o.addEventListener("error",(function(t){e.dispatch(h(t))}));var i=["app_data","app_data_update","component_added","component_updated","component_deleted","layout_added","layout_updated","layout_deleted","error"];o.addEventListener("message",(function(t){var n;t&&t.type&&i.includes(t.type)&&(e.dispatch((n={type:t.type,data:t},{type:d.H2,payload:n})),e.dispatch({type:"WS_".concat(t.type.toUpperCase()),payload:t}))}))}break;case d.uV:t&&(p.A.getInstance().close(),t=!1);break;case d.WD:if(t){var s=r.payload,c=s.messageType,l=s.data;p.A.getInstance().sendMessage(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){(0,a.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({type:c},l)).catch((function(t){console.error("Error sending message:",t),e.dispatch(h(t))}))}else console.warn("Cannot send message: WebSocket is not connected"),e.dispatch(h(new Error("WebSocket is not connected")))}return n(r)}}}),g);var t},devTools:{name:"App Builder 201",trace:!0,traceLimit:25}})}()},9391:(e,t,n)=>{n.d(t,{OJ:()=>p,As:()=>d});var r=n(467),o=n(5544),a=n(4756),i=n.n(a),s=n(6540),c=n(4702),l=(0,s.createContext)({trackEvent:function(){},trackPageView:function(){},trackError:function(){}}),u=(0,s.createContext)({user:null,isAuthenticated:!1,isLoading:!0,login:function(){},register:function(){},logout:function(){},hasRole:function(){},hasPermission:function(){}}),p=function(e){var t=e.children,n=(0,s.useState)(null),a=(0,o.A)(n,2),p=a[0],d=a[1],m=(0,s.useState)(!0),h=(0,o.A)(m,2),f=h[0],g=h[1],v=(0,s.useContext)(l).trackEvent;(0,s.useEffect)((function(){var e=function(){var e=(0,r.A)(i().mark((function e(){var t;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:try{(0,c.wR)()&&(t=(0,c.wz)(),(0,c.gf)(),t&&(d(t),v("auth_initialized",{userId:t.id||t.username,username:t.username})))}catch(e){console.error("Auth initialization error:",e)}finally{g(!1)}case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();e()}),[v]);var y=function(){var e=(0,r.A)(i().mark((function e(t,n){var r;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return g(!0),e.prev=1,e.next=4,(0,c.iD)(t,n);case 4:if(!(r=e.sent).success){e.next=11;break}return d(r.user),v("auth_login",{userId:r.user.id||r.user.username,username:r.user.username}),e.abrupt("return",r);case 11:throw v("auth_login_error",{error:r.error}),new Error(r.error);case 13:e.next=19;break;case 15:throw e.prev=15,e.t0=e.catch(1),v("auth_login_error",{error:e.t0.message}),e.t0;case 19:return e.prev=19,g(!1),e.finish(19);case 22:case"end":return e.stop()}}),e,null,[[1,15,19,22]])})));return function(t,n){return e.apply(this,arguments)}}(),E=function(){var e=(0,r.A)(i().mark((function e(t){var n;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return g(!0),e.prev=1,e.next=4,(0,c.kz)(t);case 4:if(!(n=e.sent).success){e.next=11;break}return d(n.user),v("auth_register",{userId:n.user.id||n.user.username,username:n.user.username}),e.abrupt("return",n);case 11:throw v("auth_register_error",{error:n.error}),new Error(n.error);case 13:e.next=19;break;case 15:throw e.prev=15,e.t0=e.catch(1),v("auth_register_error",{error:e.t0.message}),e.t0;case 19:return e.prev=19,g(!1),e.finish(19);case 22:case"end":return e.stop()}}),e,null,[[1,15,19,22]])})));return function(t){return e.apply(this,arguments)}}(),A=function(){var e=(0,r.A)(i().mark((function e(){var t;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return g(!0),e.prev=1,e.next=4,(0,c.ri)();case 4:return t=e.sent,d(null),v("auth_logout"),e.abrupt("return",!1!==t.success);case 10:return e.prev=10,e.t0=e.catch(1),console.error("Logout error:",e.t0),d(null),e.abrupt("return",!1);case 15:return e.prev=15,g(!1),e.finish(15);case 18:case"end":return e.stop()}}),e,null,[[1,10,15,18]])})));return function(){return e.apply(this,arguments)}}(),b={user:p,isAuthenticated:!!p,isLoading:f,login:y,register:E,logout:A,hasRole:function(e){return p&&p.roles&&p.roles.includes(e)},hasPermission:function(e){return p&&p.permissions&&p.permissions.includes(e)}};return s.createElement(u.Provider,{value:b},t)},d=function(){return(0,s.useContext)(u)}}},e=>{e.O(0,[874,96],(()=>e(e.s=4941))),e.O()}]);