"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[706],{7706:(e,t,n)=>{n.r(t),n.d(t,{default:()=>zo});var r,l,a,o,i=n(4467),c=n(467),s=n(5544),u=n(7528),p=n(4756),d=n.n(p),m=n(6540),f=n(1468),g=n(7852),b=n(3903),v=n(9237),y=n(1372),h=n(7046),x=n(261),E=n(3598),A=n(4816),w=n(3587),C=n(6020),S=n(8168),k=n(3986),O=n(3016),z=n(7142),P=n(7977),j=n(6531),D=n(9248),T=["value","onChange","min","max","step","unit","units","showSlider","showUnit","placeholder","tooltip","precision"],I=O.A.Text,M=w.I4.div(r||(r=(0,u.A)(["\n  width: 100%;\n"]))),R=w.I4.div(l||(l=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n"]))),L=w.I4.div(a||(a=(0,u.A)(["\n  flex: 1;\n  margin-left: 8px;\n"]))),F=w.I4.select(o||(o=(0,u.A)(["\n  padding: 4px 8px;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  background: white;\n  font-size: 12px;\n  min-width: 50px;\n"])));const B=function(e){var t=e.value,n=e.onChange,r=e.min,l=void 0===r?0:r,a=e.max,o=void 0===a?100:a,i=e.step,c=void 0===i?1:i,u=e.unit,p=void 0===u?"px":u,d=e.units,f=void 0===d?["px","%","rem","em","vh","vw"]:d,g=e.showSlider,b=void 0!==g&&g,v=e.showUnit,y=void 0===v||v,h=e.placeholder,x=void 0===h?"Enter value":h,E=e.tooltip,A=e.precision,w=void 0===A?0:A,C=(0,k.A)(e,T),O=(0,m.useState)(0),B=(0,s.A)(O,2),H=B[0],N=B[1],U=(0,m.useState)(p),V=(0,s.A)(U,2),W=V[0],Z=V[1];(0,m.useEffect)((function(){if(t){var e=X(t);N(e.number),Z(e.unit)}}),[t]);var X=function(e){if("number"==typeof e)return{number:e,unit:W};if("string"==typeof e){var t=e.match(/^(-?\d*\.?\d+)(.*)$/);if(t)return{number:parseFloat(t[1]),unit:t[2]||W}}return{number:0,unit:W}},J=function(e,t){return y&&t?"".concat(e).concat(t):e},G=function(e){if(null!=e){N(e);var t=J(e,W);null==n||n(t)}};return m.createElement(M,null,m.createElement(R,null,m.createElement(z.A,(0,S.A)({value:H,onChange:G,min:l,max:o,step:c,precision:w,placeholder:x,style:{flex:1}},C)),y&&m.createElement(F,{value:W,onChange:function(e){var t=e.target.value;Z(t);var r=J(H,t);null==n||n(r)}},f.map((function(e){return m.createElement("option",{key:e,value:e},e)}))),E&&m.createElement(P.A,{title:E},m.createElement(D.A,{style:{color:"#8c8c8c"}}))),b&&m.createElement(L,null,m.createElement(j.A,{value:H,onChange:G,min:l,max:o,step:c,tooltip:{formatter:function(e){return"".concat(e).concat(W)}}})),(void 0!==l||void 0!==o)&&m.createElement(I,{type:"secondary",style:{fontSize:"12px"}},"Range: ",l," - ",o))};var H,N,U,V,W,Z,X=n(436),J=n(9249),G=n(6552),Y=n(9779),$=n(2702),q=n(8073),K=n(7355),_=n(778),Q=["value","onChange","showPresets","showModeToggle","presets","placeholder"],ee=O.A.Text,te=w.I4.div(H||(H=(0,u.A)(["\n  width: 100%;\n"]))),ne=w.I4.div(N||(N=(0,u.A)(["\n  width: 32px;\n  height: 32px;\n  border-radius: 4px;\n  border: 1px solid #d9d9d9;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: ",";\n  position: relative;\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(45deg, #ccc 25%, transparent 25%), \n                linear-gradient(-45deg, #ccc 25%, transparent 25%), \n                linear-gradient(45deg, transparent 75%, #ccc 75%), \n                linear-gradient(-45deg, transparent 75%, #ccc 75%);\n    background-size: 8px 8px;\n    background-position: 0 0, 0 4px, 4px -4px, -4px 0px;\n    z-index: -1;\n  }\n"])),(function(e){return e.color||"#ffffff"})),re=w.I4.div(U||(U=(0,u.A)(["\n  display: flex;\n  gap: 4px;\n  margin-bottom: 8px;\n"]))),le=(0,w.I4)(J.Ay)(V||(V=(0,u.A)(["\n  font-size: 12px;\n  height: 24px;\n  padding: 0 8px;\n"]))),ae=w.I4.div(W||(W=(0,u.A)(["\n  display: grid;\n  grid-template-columns: repeat(8, 1fr);\n  gap: 4px;\n  margin-top: 8px;\n"]))),oe=w.I4.div(Z||(Z=(0,u.A)(["\n  width: 24px;\n  height: 24px;\n  border-radius: 2px;\n  border: 1px solid #d9d9d9;\n  cursor: pointer;\n  background: ",";\n  \n  &:hover {\n    border-color: #1890ff;\n    transform: scale(1.1);\n  }\n"])),(function(e){return e.color}));const ie=function(e){var t=e.value,n=e.onChange,r=e.showPresets,l=void 0===r||r,a=e.showModeToggle,o=void 0===a||a,i=e.presets,c=void 0===i?[]:i,u=e.placeholder,p=void 0===u?"Enter color":u,d=(0,k.A)(e,Q),f=(0,m.useState)("hex"),g=(0,s.A)(f,2),b=g[0],v=g[1],y=(0,m.useState)(t||"#ffffff"),h=(0,s.A)(y,2),x=h[0],E=h[1],A=(0,m.useState)(""),w=(0,s.A)(A,2),O=w[0],z=w[1],P=["#ffffff","#f5f5f5","#d9d9d9","#bfbfbf","#8c8c8c","#595959","#262626","#000000","#ff4d4f","#ff7a45","#ffa940","#ffec3d","#bae637","#73d13d","#40a9ff","#597ef7","#9254de","#f759ab","#ff85c0","#ffc069"].concat((0,X.A)(C.Ay.colors.primary?[C.Ay.colors.primary[500]]:[]),(0,X.A)(C.Ay.colors.secondary?[C.Ay.colors.secondary[500]]:[]),(0,X.A)(c));(0,m.useEffect)((function(){t&&(E(t),z(j(t,b)))}),[t,b]);var j=function(e,t){if(!e)return"";try{switch(t){case"hex":return e.startsWith("#")?e:"#".concat(e);case"rgb":return D(e);case"hsl":return T(e);default:return e}}catch(t){return e}},D=function(e){if(!e.startsWith("#"))return e;var t=parseInt(e.slice(1,3),16),n=parseInt(e.slice(3,5),16),r=parseInt(e.slice(5,7),16);return"rgb(".concat(t,", ").concat(n,", ").concat(r,")")},T=function(e){return e.startsWith("#")?"hsl(0, 0%, 50%)":e},I=m.createElement("div",{style:{width:280}},o&&m.createElement(m.Fragment,null,m.createElement(re,null,m.createElement(le,{type:"hex"===b?"primary":"default",size:"small",onClick:function(){return v("hex")}},"HEX"),m.createElement(le,{type:"rgb"===b?"primary":"default",size:"small",onClick:function(){return v("rgb")}},"RGB"),m.createElement(le,{type:"hsl"===b?"primary":"default",size:"small",onClick:function(){return v("hsl")}},"HSL")),m.createElement(G.A,{style:{margin:"8px 0"}})),m.createElement(Y.A,(0,S.A)({value:x,onChange:function(e){var t=e.toHexString();E(t),z(j(t,b)),null==n||n(t)},showText:!0,size:"large"},d)),l&&m.createElement(m.Fragment,null,m.createElement(G.A,{style:{margin:"8px 0"}}),m.createElement(ee,{strong:!0,style:{fontSize:"12px"}},"Color Presets"),m.createElement(ae,null,P.slice(0,24).map((function(e,t){return m.createElement(oe,{key:t,color:e,onClick:function(){return E(t=e),z(j(t,b)),void(null==n||n(t));var t},title:e})})))));return m.createElement(te,null,m.createElement($.A.Compact,{style:{width:"100%"}},m.createElement(q.A,{content:I,trigger:"click",placement:"bottomLeft"},m.createElement(ne,{color:x},m.createElement(_.A,{style:{color:"rgba(0,0,0,0.3)"}}))),m.createElement(K.A,{value:O,onChange:function(e){var t,r=e.target.value;z(r),(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(t=r)||/^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/.test(t)||/^hsl\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*\)$/.test(t))&&(E(r),null==n||n(r))},placeholder:p,style:{flex:1}})))};var ce,se,ue,pe,de,me,fe,ge,be,ve,ye,he=n(2284),xe=n(385),Ee=n(4046),Ae=["value","onChange","type","showVisual","showPresets","unit"];function we(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var Ce=O.A.Text,Se=w.I4.div(ce||(ce=(0,u.A)(["\n  width: 100%;\n"]))),ke=w.I4.div(se||(se=(0,u.A)(["\n  position: relative;\n  width: 120px;\n  height: 120px;\n  margin: 16px auto;\n  background: #f5f5f5;\n  border: 2px dashed #d9d9d9;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n"]))),Oe=w.I4.div(ue||(ue=(0,u.A)(["\n  width: 60px;\n  height: 60px;\n  background: #1890ff;\n  opacity: 0.3;\n  border-radius: 4px;\n  position: relative;\n"]))),ze=w.I4.div(pe||(pe=(0,u.A)(["\n  position: absolute;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n"]))),Pe=(0,w.I4)(ze)(de||(de=(0,u.A)(["\n  top: -30px;\n  left: 50%;\n  transform: translateX(-50%);\n"]))),je=(0,w.I4)(ze)(me||(me=(0,u.A)(["\n  right: -60px;\n  top: 50%;\n  transform: translateY(-50%);\n"]))),De=(0,w.I4)(ze)(fe||(fe=(0,u.A)(["\n  bottom: -30px;\n  left: 50%;\n  transform: translateX(-50%);\n"]))),Te=(0,w.I4)(ze)(ge||(ge=(0,u.A)(["\n  left: -60px;\n  top: 50%;\n  transform: translateY(-50%);\n"]))),Ie=w.I4.div(be||(be=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n"]))),Me=w.I4.div(ve||(ve=(0,u.A)(["\n  display: flex;\n  gap: 4px;\n  margin-top: 8px;\n"]))),Re=(0,w.I4)(J.Ay)(ye||(ye=(0,u.A)(["\n  font-size: 12px;\n  height: 24px;\n  padding: 0 8px;\n"])));const Le=function(e){var t=e.value,n=e.onChange,r=e.type,l=void 0===r?"margin":r,a=e.showVisual,o=void 0===a||a,c=e.showPresets,u=void 0===c||c,p=e.unit,d=void 0===p?"px":p,f=((0,k.A)(e,Ae),(0,m.useState)({top:0,right:0,bottom:0,left:0})),g=(0,s.A)(f,2),b=g[0],v=g[1],y=(0,m.useState)(!1),h=(0,s.A)(y,2),x=h[0],E=h[1];(0,m.useEffect)((function(){if(t){var e=A(t);v(e)}}),[t]);var A=function(e){if(!e)return{top:0,right:0,bottom:0,left:0};if("object"===(0,he.A)(e))return{top:parseFloat(e.top)||0,right:parseFloat(e.right)||0,bottom:parseFloat(e.bottom)||0,left:parseFloat(e.left)||0};var t=e.toString().split(/\s+/).map((function(e){return parseFloat(e.replace(/[^\d.-]/g,""))||0}));switch(t.length){case 1:return{top:t[0],right:t[0],bottom:t[0],left:t[0]};case 2:return{top:t[0],right:t[1],bottom:t[0],left:t[1]};case 3:return{top:t[0],right:t[1],bottom:t[2],left:t[1]};case 4:return{top:t[0],right:t[1],bottom:t[2],left:t[3]};default:return{top:0,right:0,bottom:0,left:0}}},w=function(e){var t=e.top,n=e.right,r=e.bottom,l=e.left;return t===n&&n===r&&r===l?"".concat(t).concat(d):t===r&&l===n?"".concat(t).concat(d," ").concat(n).concat(d):"".concat(t).concat(d," ").concat(n).concat(d," ").concat(r).concat(d," ").concat(l).concat(d)},C=function(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?we(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):we(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},b);x?(r.top=t,r.right=t,r.bottom=t,r.left=t):r[e]=t,v(r),null==n||n(w(r))};return m.createElement(Se,null,m.createElement(Ie,null,m.createElement(Ce,{strong:!0,style:{fontSize:"12px"}},"margin"===l?"Margin":"Padding"),m.createElement(J.Ay,{type:x?"primary":"default",size:"small",icon:x?m.createElement(xe.A,null):m.createElement(Ee.A,null),onClick:function(){E(!x)},title:x?"Unlink sides":"Link all sides"})),o&&m.createElement(ke,null,m.createElement(Pe,null,m.createElement(z.A,{size:"small",value:b.top,onChange:function(e){return C("top",e||0)},style:{width:50},min:0})),m.createElement(je,null,m.createElement(z.A,{size:"small",value:b.right,onChange:function(e){return C("right",e||0)},style:{width:50},min:0})),m.createElement(De,null,m.createElement(z.A,{size:"small",value:b.bottom,onChange:function(e){return C("bottom",e||0)},style:{width:50},min:0})),m.createElement(Te,null,m.createElement(z.A,{size:"small",value:b.left,onChange:function(e){return C("left",e||0)},style:{width:50},min:0})),m.createElement(Oe,null)),m.createElement($.A,{direction:"vertical",style:{width:"100%"}},m.createElement(Ie,null,m.createElement(Ce,{style:{fontSize:"12px",minWidth:"30px"}},"Top:"),m.createElement(z.A,{value:b.top,onChange:function(e){return C("top",e||0)},style:{flex:1},min:0,addonAfter:d})),m.createElement(Ie,null,m.createElement(Ce,{style:{fontSize:"12px",minWidth:"30px"}},"Right:"),m.createElement(z.A,{value:b.right,onChange:function(e){return C("right",e||0)},style:{flex:1},min:0,addonAfter:d})),m.createElement(Ie,null,m.createElement(Ce,{style:{fontSize:"12px",minWidth:"30px"}},"Bottom:"),m.createElement(z.A,{value:b.bottom,onChange:function(e){return C("bottom",e||0)},style:{flex:1},min:0,addonAfter:d})),m.createElement(Ie,null,m.createElement(Ce,{style:{fontSize:"12px",minWidth:"30px"}},"Left:"),m.createElement(z.A,{value:b.left,onChange:function(e){return C("left",e||0)},style:{flex:1},min:0,addonAfter:d}))),u&&m.createElement(Me,null,m.createElement(Ce,{style:{fontSize:"12px",marginRight:"8px"}},"Presets:"),["0px","4px","8px","12px","16px","24px","32px","8px 16px","16px 24px"].map((function(e,t){return m.createElement(Re,{key:t,onClick:function(){return function(e){var t=A(e);v(t),null==n||n(w(t))}(e)},title:"Apply ".concat(e)},e)}))))};var Fe,Be,He,Ne,Ue=n(4358),Ve=n(2454),We=["value","onChange","showPreview"],Ze=O.A.Text,Xe=Ue.A.Option,Je=w.I4.div(Fe||(Fe=(0,u.A)(["\n  width: 100%;\n"]))),Ge=w.I4.div(Be||(Be=(0,u.A)(["\n  width: 100%;\n  height: 60px;\n  margin: 12px 0;\n  background: #f5f5f5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 4px;\n  border: ",";\n"])),(function(e){return e.borderStyle||"1px solid #d9d9d9"})),Ye=w.I4.div(He||(He=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 12px;\n"]))),$e=(0,w.I4)(Ze)(Ne||(Ne=(0,u.A)(["\n  min-width: 60px;\n  font-size: 12px;\n  font-weight: 500;\n"])));const qe=function(e){var t=e.value,n=e.onChange,r=e.showPreview,l=void 0===r||r,a=((0,k.A)(e,We),(0,m.useState)("solid")),o=(0,s.A)(a,2),i=o[0],c=o[1],u=(0,m.useState)("1px"),p=(0,s.A)(u,2),d=p[0],f=p[1],g=(0,m.useState)("#d9d9d9"),b=(0,s.A)(g,2),v=b[0],y=b[1];(0,m.useEffect)((function(){if(t){var e=h(t);c(e.style),f(e.width),y(e.color)}}),[t]);var h=function(e){if(!e)return{style:"solid",width:"1px",color:"#d9d9d9"};if("object"===(0,he.A)(e))return{style:e.style||"solid",width:e.width||"1px",color:e.color||"#d9d9d9"};if("string"==typeof e){var t=e.split(/\s+/),n="1px",r="solid",l="#d9d9d9";return t.forEach((function(e){e.match(/^\d+(\.\d+)?(px|em|rem|%)$/)?n=e:["none","solid","dashed","dotted","double","groove","ridge","inset","outset"].includes(e)?r=e:(e.startsWith("#")||e.startsWith("rgb")||e.startsWith("hsl")||x(e))&&(l=e)})),{style:r,width:n,color:l}}return{style:"solid",width:"1px",color:"#d9d9d9"}},x=function(e){return["black","white","red","green","blue","yellow","orange","purple","pink","brown","gray","grey","transparent"].includes(e.toLowerCase())},E=function(e,t,n){return"none"===e?"none":"".concat(t," ").concat(e," ").concat(n)},A=E(i,d,v);return m.createElement(Je,null,m.createElement($.A,{direction:"vertical",style:{width:"100%"}},m.createElement(Ye,null,m.createElement($e,null,"Style:"),m.createElement(Ue.A,{value:i,onChange:function(e){c(e);var t=E(e,d,v);null==n||n(t)},style:{flex:1},size:"small"},[{value:"none",label:"None"},{value:"solid",label:"Solid"},{value:"dashed",label:"Dashed"},{value:"dotted",label:"Dotted"},{value:"double",label:"Double"},{value:"groove",label:"Groove"},{value:"ridge",label:"Ridge"},{value:"inset",label:"Inset"},{value:"outset",label:"Outset"}].map((function(e){return m.createElement(Xe,{key:e.value,value:e.value},e.label)})))),"none"!==i&&m.createElement(m.Fragment,null,m.createElement(Ye,null,m.createElement($e,null,"Width:"),m.createElement("div",{style:{flex:1}},m.createElement(B,{value:d,onChange:function(e){f(e);var t=E(i,e,v);null==n||n(t)},min:0,max:20,step:1,unit:"px",units:["px","em","rem"],size:"small"}))),m.createElement(Ye,null,m.createElement($e,null,"Color:"),m.createElement("div",{style:{flex:1}},m.createElement(ie,{value:v,onChange:function(e){y(e);var t=E(i,d,e);null==n||n(t)},placeholder:"Border color"})))),l&&m.createElement(m.Fragment,null,m.createElement(G.A,{style:{margin:"8px 0"}}),m.createElement(Ze,{style:{fontSize:"12px",marginBottom:"4px"}},"Preview:"),m.createElement(Ge,{borderStyle:A},m.createElement(Ve.A,{style:{fontSize:"24px",color:"#8c8c8c"}})),m.createElement(Ze,{type:"secondary",style:{fontSize:"11px",textAlign:"center"}},A))))};var Ke,_e,Qe,et,tt,nt=n(5039),rt=["value","onChange","showPreview"],lt=O.A.Text,at=w.I4.div(Ke||(Ke=(0,u.A)(["\n  width: 100%;\n"]))),ot=w.I4.div(_e||(_e=(0,u.A)(["\n  width: 100%;\n  height: 80px;\n  margin: 12px 0;\n  background: #ffffff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 8px;\n  box-shadow: ",";\n  border: 1px solid #f0f0f0;\n"])),(function(e){return e.shadowStyle||"none"})),it=w.I4.div(Qe||(Qe=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 12px;\n"]))),ct=(0,w.I4)(lt)(et||(et=(0,u.A)(["\n  min-width: 80px;\n  font-size: 12px;\n  font-weight: 500;\n"]))),st=w.I4.div(tt||(tt=(0,u.A)(["\n  width: 60px;\n  height: 40px;\n  background: #1890ff;\n  border-radius: 4px;\n  opacity: 0.8;\n"])));const ut=function(e){var t=e.value,n=e.onChange,r=e.showPreview,l=void 0===r||r,a=((0,k.A)(e,rt),(0,m.useState)("0px")),o=(0,s.A)(a,2),i=o[0],c=o[1],u=(0,m.useState)("2px"),p=(0,s.A)(u,2),d=p[0],f=p[1],g=(0,m.useState)("4px"),b=(0,s.A)(g,2),v=b[0],y=b[1],h=(0,m.useState)("0px"),x=(0,s.A)(h,2),E=x[0],A=x[1],w=(0,m.useState)("rgba(0, 0, 0, 0.1)"),C=(0,s.A)(w,2),S=C[0],O=C[1],z=(0,m.useState)(!1),P=(0,s.A)(z,2),j=P[0],D=P[1];(0,m.useEffect)((function(){if(t){var e=T(t);c(e.offsetX),f(e.offsetY),y(e.blurRadius),A(e.spreadRadius),O(e.color),D(e.inset)}}),[t]);var T=function(e){if(!e||"none"===e)return{offsetX:"0px",offsetY:"2px",blurRadius:"4px",spreadRadius:"0px",color:"rgba(0, 0, 0, 0.1)",inset:!1};if("object"===(0,he.A)(e))return{offsetX:e.offsetX||"0px",offsetY:e.offsetY||"2px",blurRadius:e.blurRadius||"4px",spreadRadius:e.spreadRadius||"0px",color:e.color||"rgba(0, 0, 0, 0.1)",inset:e.inset||!1};if("string"==typeof e){var t=e.trim(),n=!1;t.startsWith("inset ")&&(n=!0,t=t.replace("inset ",""));var r="rgba(0, 0, 0, 0.1)",l=t.match(/(rgba?\([^)]+\)|hsla?\([^)]+\)|#[a-fA-F0-9]{3,8}|\b\w+\b)$/);l&&(r=l[1],t=t.replace(l[1],"").trim());var a=t.split(/\s+/).filter((function(e){return e}));return{offsetX:a[0]||"0px",offsetY:a[1]||"2px",blurRadius:a[2]||"4px",spreadRadius:a[3]||"0px",color:r,inset:n}}return{offsetX:"0px",offsetY:"2px",blurRadius:"4px",spreadRadius:"0px",color:"rgba(0, 0, 0, 0.1)",inset:!1}},I=function(e,t,n,r,l,a){var o=[e,t,n,r,l].join(" ");return a?"inset ".concat(o):o},M=function(e,t){var r=i,l=d,a=v,o=E,s=S,u=j;switch(e){case"offsetX":r=t,c(t);break;case"offsetY":l=t,f(t);break;case"blurRadius":a=t,y(t);break;case"spreadRadius":o=t,A(t);break;case"color":s=t,O(t);break;case"inset":u=t,D(t)}var p=I(r,l,a,o,s,u);null==n||n(p)},R=I(i,d,v,E,S,j);return m.createElement(at,null,m.createElement($.A,{direction:"vertical",style:{width:"100%"}},m.createElement(it,null,m.createElement(ct,null,"Inset:"),m.createElement(nt.A,{checked:j,onChange:function(e){return M("inset",e)},size:"small"})),m.createElement(it,null,m.createElement(ct,null,"Offset X:"),m.createElement("div",{style:{flex:1}},m.createElement(B,{value:i,onChange:function(e){return M("offsetX",e)},min:-50,max:50,step:1,unit:"px",units:["px","em","rem"],size:"small"}))),m.createElement(it,null,m.createElement(ct,null,"Offset Y:"),m.createElement("div",{style:{flex:1}},m.createElement(B,{value:d,onChange:function(e){return M("offsetY",e)},min:-50,max:50,step:1,unit:"px",units:["px","em","rem"],size:"small"}))),m.createElement(it,null,m.createElement(ct,null,"Blur:"),m.createElement("div",{style:{flex:1}},m.createElement(B,{value:v,onChange:function(e){return M("blurRadius",e)},min:0,max:100,step:1,unit:"px",units:["px","em","rem"],size:"small"}))),m.createElement(it,null,m.createElement(ct,null,"Spread:"),m.createElement("div",{style:{flex:1}},m.createElement(B,{value:E,onChange:function(e){return M("spreadRadius",e)},min:-50,max:50,step:1,unit:"px",units:["px","em","rem"],size:"small"}))),m.createElement(it,null,m.createElement(ct,null,"Color:"),m.createElement("div",{style:{flex:1}},m.createElement(ie,{value:S,onChange:function(e){return M("color",e)},placeholder:"Shadow color"}))),l&&m.createElement(m.Fragment,null,m.createElement(G.A,{style:{margin:"8px 0"}}),m.createElement(lt,{style:{fontSize:"12px",marginBottom:"4px"}},"Preview:"),m.createElement(ot,{shadowStyle:R},m.createElement(st,null)),m.createElement(lt,{type:"secondary",style:{fontSize:"11px",textAlign:"center"}},R))))};var pt,dt,mt,ft,gt=n(3350),bt=["value","onChange","showPreview"],vt=O.A.Text,yt=Ue.A.Option,ht=w.I4.div(pt||(pt=(0,u.A)(["\n  width: 100%;\n"]))),xt=w.I4.div(dt||(dt=(0,u.A)(["\n  width: 100%;\n  padding: 16px;\n  margin: 12px 0;\n  background: #f5f5f5;\n  border-radius: 4px;\n  border: 1px solid #d9d9d9;\n  font-family: ",";\n  font-size: ",";\n  font-weight: ",";\n  line-height: ",";\n  text-align: center;\n"])),(function(e){return e.fontFamily||"inherit"}),(function(e){return e.fontSize||"16px"}),(function(e){return e.fontWeight||"normal"}),(function(e){return e.lineHeight||"1.5"})),Et=w.I4.div(mt||(mt=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 12px;\n"]))),At=(0,w.I4)(vt)(ft||(ft=(0,u.A)(["\n  min-width: 80px;\n  font-size: 12px;\n  font-weight: 500;\n"])));const wt=function(e){var t=e.value,n=e.onChange,r=e.showPreview,l=void 0===r||r,a=((0,k.A)(e,bt),(0,m.useState)("inherit")),o=(0,s.A)(a,2),i=o[0],c=o[1],u=(0,m.useState)("16px"),p=(0,s.A)(u,2),d=p[0],f=p[1],g=(0,m.useState)("normal"),b=(0,s.A)(g,2),v=b[0],y=b[1],h=(0,m.useState)("1.5"),x=(0,s.A)(h,2),E=x[0],A=x[1];(0,m.useEffect)((function(){if(t){var e=w(t);c(e.family),f(e.size),y(e.weight),A(e.lineHeight)}}),[t]);var w=function(e){return e?"object"===(0,he.A)(e)?{family:e.fontFamily||e.family||"inherit",size:e.fontSize||e.size||"16px",weight:e.fontWeight||e.weight||"normal",lineHeight:e.lineHeight||"1.5"}:{family:e,size:d,weight:v,lineHeight:E}:{family:"inherit",size:"16px",weight:"normal",lineHeight:"1.5"}},C=function(e,t){var r=i,l=d,a=v,o=E;switch(e){case"family":r=t,c(t);break;case"size":l=t,f(t);break;case"weight":a=t,y(t);break;case"lineHeight":o=t,A(t)}null==n||n({fontFamily:r,fontSize:l,fontWeight:a,lineHeight:o})};return m.createElement(ht,null,m.createElement($.A,{direction:"vertical",style:{width:"100%"}},m.createElement(Et,null,m.createElement(At,null,"Family:"),m.createElement(Ue.A,{value:i,onChange:function(e){return C("family",e)},style:{flex:1},size:"small",showSearch:!0,placeholder:"Select font family"},[{value:"inherit",label:"Inherit"},{value:"Arial, sans-serif",label:"Arial"},{value:"Helvetica, Arial, sans-serif",label:"Helvetica"},{value:'"Times New Roman", Times, serif',label:"Times New Roman"},{value:"Georgia, serif",label:"Georgia"},{value:'"Courier New", Courier, monospace',label:"Courier New"},{value:"Verdana, Geneva, sans-serif",label:"Verdana"},{value:'"Trebuchet MS", Helvetica, sans-serif',label:"Trebuchet MS"},{value:'"Lucida Sans Unicode", "Lucida Grande", sans-serif',label:"Lucida Sans"},{value:"Impact, Charcoal, sans-serif",label:"Impact"},{value:'"Comic Sans MS", cursive',label:"Comic Sans MS"},{value:'"Palatino Linotype", "Book Antiqua", Palatino, serif',label:"Palatino"},{value:'"Inter", -apple-system, BlinkMacSystemFont, sans-serif',label:"Inter"},{value:'"Roboto", sans-serif',label:"Roboto"},{value:'"Open Sans", sans-serif',label:"Open Sans"},{value:'"Lato", sans-serif',label:"Lato"},{value:'"Montserrat", sans-serif',label:"Montserrat"},{value:'"Source Sans Pro", sans-serif',label:"Source Sans Pro"}].map((function(e){return m.createElement(yt,{key:e.value,value:e.value},m.createElement("span",{style:{fontFamily:e.value}},e.label))})))),m.createElement(Et,null,m.createElement(At,null,"Size:"),m.createElement("div",{style:{flex:1}},m.createElement(B,{value:d,onChange:function(e){return C("size",e)},min:8,max:72,step:1,unit:"px",units:["px","em","rem","%"],size:"small"}))),m.createElement(Et,null,m.createElement(At,null,"Weight:"),m.createElement(Ue.A,{value:v,onChange:function(e){return C("weight",e)},style:{flex:1},size:"small"},[{value:"100",label:"Thin (100)"},{value:"200",label:"Extra Light (200)"},{value:"300",label:"Light (300)"},{value:"normal",label:"Normal (400)"},{value:"500",label:"Medium (500)"},{value:"600",label:"Semi Bold (600)"},{value:"bold",label:"Bold (700)"},{value:"800",label:"Extra Bold (800)"},{value:"900",label:"Black (900)"}].map((function(e){return m.createElement(yt,{key:e.value,value:e.value},e.label)})))),m.createElement(Et,null,m.createElement(At,null,"Line Height:"),m.createElement("div",{style:{flex:1}},m.createElement(B,{value:E,onChange:function(e){return C("lineHeight",e)},min:.5,max:3,step:.1,precision:1,showUnit:!1,size:"small",tooltip:"Line height as a multiplier (e.g., 1.5 = 150%)"}))),l&&m.createElement(m.Fragment,null,m.createElement(G.A,{style:{margin:"8px 0"}}),m.createElement(vt,{style:{fontSize:"12px",marginBottom:"4px"}},"Preview:"),m.createElement(xt,{fontFamily:i,fontSize:d,fontWeight:v,lineHeight:E},m.createElement(gt.A,{style:{marginRight:"8px"}}),"The quick brown fox jumps over the lazy dog"),m.createElement(vt,{type:"secondary",style:{fontSize:"11px",textAlign:"center"}},i," • ",d," • ",v," • ",E))))};var Ct="text",St="number",kt="boolean",Ot="color",zt="select",Pt="spacing",jt="border",Dt="shadow",Tt="font",It="array",Mt="object",Rt={button:{text:{type:Ct,label:"Button Text",placeholder:"Enter button text"},variant:{type:zt,label:"Variant",options:[{value:"primary",label:"Primary"},{value:"secondary",label:"Secondary"},{value:"text",label:"Text"},{value:"link",label:"Link"},{value:"ghost",label:"Ghost"},{value:"dashed",label:"Dashed"}]},size:{type:zt,label:"Size",options:[{value:"small",label:"Small"},{value:"medium",label:"Medium"},{value:"large",label:"Large"}]},disabled:{type:kt,label:"Disabled"},block:{type:kt,label:"Full Width"},onClick:{type:Ct,label:"onClick Handler",placeholder:"Enter function name"}},text:{content:{type:Ct,label:"Text Content",multiline:!0,placeholder:"Enter text content"},variant:{type:zt,label:"Variant",options:[{value:"h1",label:"Heading 1"},{value:"h2",label:"Heading 2"},{value:"h3",label:"Heading 3"},{value:"h4",label:"Heading 4"},{value:"h5",label:"Heading 5"},{value:"h6",label:"Heading 6"},{value:"p",label:"Paragraph"},{value:"span",label:"Span"}]},color:{type:Ot,label:"Text Color"},align:{type:zt,label:"Text Alignment",options:[{value:"left",label:"Left"},{value:"center",label:"Center"},{value:"right",label:"Right"},{value:"justify",label:"Justify"}]}},input:{label:{type:Ct,label:"Input Label",placeholder:"Enter input label"},placeholder:{type:Ct,label:"Placeholder",placeholder:"Enter placeholder text"},type:{type:zt,label:"Input Type",options:[{value:"text",label:"Text"},{value:"password",label:"Password"},{value:"email",label:"Email"},{value:"number",label:"Number"},{value:"tel",label:"Telephone"},{value:"url",label:"URL"}]},required:{type:kt,label:"Required"},disabled:{type:kt,label:"Disabled"},validation:{type:zt,label:"Validation",options:[{value:"none",label:"None"},{value:"email",label:"Email"},{value:"url",label:"URL"},{value:"phone",label:"Phone"},{value:"custom",label:"Custom"}]}},card:{title:{type:Ct,label:"Card Title",placeholder:"Enter card title"},description:{type:Ct,label:"Description",multiline:!0,placeholder:"Enter card description"},image:{type:Ct,label:"Image URL",placeholder:"Enter image URL"},elevation:{type:zt,label:"Elevation",options:[{value:"none",label:"None"},{value:"sm",label:"Small"},{value:"md",label:"Medium"},{value:"lg",label:"Large"}]},bordered:{type:kt,label:"Bordered"}}},Lt={width:{type:Ct,label:"Width",placeholder:"e.g., 100%, 200px",group:"dimensions"},height:{type:Ct,label:"Height",placeholder:"e.g., 100%, 200px",group:"dimensions"},minWidth:{type:Ct,label:"Min Width",placeholder:"e.g., 100px",group:"dimensions"},maxWidth:{type:Ct,label:"Max Width",placeholder:"e.g., 500px",group:"dimensions"},minHeight:{type:Ct,label:"Min Height",placeholder:"e.g., 100px",group:"dimensions"},maxHeight:{type:Ct,label:"Max Height",placeholder:"e.g., 500px",group:"dimensions"},margin:{type:Pt,label:"Margin",group:"spacing"},padding:{type:Pt,label:"Padding",group:"spacing"},fontSize:{type:St,label:"Font Size",min:8,max:72,unit:"px",units:["px","em","rem","%"],group:"typography"},fontWeight:{type:zt,label:"Font Weight",options:[{value:"normal",label:"Normal"},{value:"bold",label:"Bold"},{value:"lighter",label:"Lighter"},{value:"bolder",label:"Bolder"},{value:"100",label:"100"},{value:"200",label:"200"},{value:"300",label:"300"},{value:"400",label:"400"},{value:"500",label:"500"},{value:"600",label:"600"},{value:"700",label:"700"},{value:"800",label:"800"},{value:"900",label:"900"}],group:"typography"},lineHeight:{type:St,label:"Line Height",min:.5,max:3,step:.1,precision:1,showUnit:!1,group:"typography"},fontFamily:{type:Tt,label:"Font Family",group:"typography"},color:{type:Ot,label:"Text Color",group:"colors"},backgroundColor:{type:Ot,label:"Background Color",group:"colors"},border:{type:jt,label:"Border",group:"border"},borderTop:{type:jt,label:"Border Top",group:"border"},borderRight:{type:jt,label:"Border Right",group:"border"},borderBottom:{type:jt,label:"Border Bottom",group:"border"},borderLeft:{type:jt,label:"Border Left",group:"border"},borderRadius:{type:St,label:"Border Radius",min:0,max:50,unit:"px",units:["px","em","rem","%"],group:"border"},boxShadow:{type:Dt,label:"Box Shadow",group:"shadow"},textShadow:{type:Dt,label:"Text Shadow",group:"shadow"},display:{type:zt,label:"Display",options:[{value:"block",label:"Block"},{value:"inline",label:"Inline"},{value:"inline-block",label:"Inline Block"},{value:"flex",label:"Flex"},{value:"grid",label:"Grid"},{value:"none",label:"None"}],group:"layout"},position:{type:zt,label:"Position",options:[{value:"static",label:"Static"},{value:"relative",label:"Relative"},{value:"absolute",label:"Absolute"},{value:"fixed",label:"Fixed"},{value:"sticky",label:"Sticky"}],group:"layout"}},Ft=function(e){return e.replace(/([A-Z])/g," $1").replace(/^./,(function(e){return e.toUpperCase()})).trim()},Bt=["propertyName","value","onChange","componentType","schema","showValidation"],Ht=K.A.TextArea,Nt=Ue.A.Option,Ut=O.A.Text;const Vt=function(e){var t,n=e.propertyName,r=e.value,l=e.onChange,a=e.componentType,o=e.schema,i=e.showValidation,c=void 0===i||i,s=(0,k.A)(e,Bt),u=o||function(e,t,n){return n&&Rt[n]&&Rt[n][e]?Rt[n][e]:Lt[e]?Lt[e]:function(e,t){var n=e.toLowerCase();return n.includes("color")||n.includes("background")?{type:Ot,label:Ft(e)}:"number"==typeof t||"string"==typeof t&&/^\d+(\.\d+)?(px|em|rem|%)?$/.test(t)?{type:St,label:Ft(e)}:"boolean"==typeof t?{type:kt,label:Ft(e)}:n.includes("margin")||n.includes("padding")?{type:Pt,label:Ft(e)}:n.includes("border")&&!n.includes("radius")?{type:jt,label:Ft(e)}:n.includes("shadow")?{type:Dt,label:Ft(e)}:!n.includes("font")||n.includes("size")||n.includes("weight")?Array.isArray(t)?{type:It,label:Ft(e)}:"object"===(0,he.A)(t)&&null!==t?{type:Mt,label:Ft(e)}:{type:Ct,label:Ft(e)}:{type:Tt,label:Ft(e)}}(e,t)}(n,r,a),p=c?function(e,t){if(!t)return{valid:!0};switch(t.type){case St:var n=parseFloat(e);if(isNaN(n))return{valid:!1,error:"Must be a valid number"};if(void 0!==t.min&&n<t.min)return{valid:!1,error:"Must be at least ".concat(t.min)};if(void 0!==t.max&&n>t.max)return{valid:!1,error:"Must be at most ".concat(t.max)};break;case Ot:if(e&&!/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$|^rgb\(|^rgba\(|^hsl\(|^hsla\(/.test(e))return{valid:!1,error:"Must be a valid color value"};break;case zt:if(t.options&&e&&!t.options.some((function(t){return t.value===e})))return{valid:!1,error:"Must be one of the available options"}}return{valid:!0}}(r,u):{valid:!0},d=function(e){l&&l(e,n,u)},f=function(){return!c||p.valid?null:m.createElement(Ut,{type:"danger",style:{fontSize:"12px",display:"block",marginTop:"4px"}},p.error)},g=function(){return u.description||u.tooltip?m.createElement(P.A,{title:u.description||u.tooltip},m.createElement(D.A,{style:{color:"#8c8c8c",marginLeft:"4px"}})):null};switch(u.type){case Ct:return u.multiline?m.createElement("div",null,m.createElement(Ht,(0,S.A)({value:r,onChange:function(e){return d(e.target.value)},placeholder:u.placeholder,rows:u.rows||3,status:p.valid?"":"error"},s)),g(),f()):m.createElement("div",null,m.createElement(K.A,(0,S.A)({value:r,onChange:function(e){return d(e.target.value)},placeholder:u.placeholder,status:p.valid?"":"error"},s)),g(),f());case St:return m.createElement("div",null,m.createElement(B,(0,S.A)({value:r,onChange:d,min:u.min,max:u.max,step:u.step,precision:u.precision,unit:u.unit,units:u.units,showSlider:u.showSlider,showUnit:u.showUnit,placeholder:u.placeholder,tooltip:u.tooltip},s)),g(),f());case kt:return m.createElement("div",{style:{display:"flex",alignItems:"center",gap:"8px"}},m.createElement(nt.A,(0,S.A)({checked:r,onChange:d},s)),g(),f());case Ot:return m.createElement("div",null,m.createElement(ie,(0,S.A)({value:r,onChange:d,showPresets:u.showPresets,showModeToggle:u.showModeToggle,presets:u.presets,placeholder:u.placeholder},s)),g(),f());case zt:return m.createElement("div",null,m.createElement(Ue.A,(0,S.A)({value:r,onChange:d,placeholder:u.placeholder,style:{width:"100%"},status:p.valid?"":"error"},s),null===(t=u.options)||void 0===t?void 0:t.map((function(e){return m.createElement(Nt,{key:e.value,value:e.value},e.label)}))),g(),f());case Pt:return m.createElement("div",null,m.createElement(Le,(0,S.A)({value:r,onChange:d,type:n.includes("margin")?"margin":"padding",showVisual:u.showVisual,showPresets:u.showPresets,unit:u.unit},s)),g(),f());case jt:return m.createElement("div",null,m.createElement(qe,(0,S.A)({value:r,onChange:d,showPreview:u.showPreview},s)),g(),f());case Dt:return m.createElement("div",null,m.createElement(ut,(0,S.A)({value:r,onChange:d,showPreview:u.showPreview},s)),g(),f());case Tt:return m.createElement("div",null,m.createElement(wt,(0,S.A)({value:r,onChange:d,showPreview:u.showPreview},s)),g(),f());case It:return m.createElement("div",null,m.createElement(Ht,(0,S.A)({value:Array.isArray(r)?JSON.stringify(r,null,2):r,onChange:function(e){try{var t=JSON.parse(e.target.value);d(t)}catch(t){d(e.target.value)}},placeholder:u.placeholder||"Enter array as JSON",rows:4,status:p.valid?"":"error"},s)),g(),f());case Mt:return m.createElement("div",null,m.createElement(Ht,(0,S.A)({value:"object"===(0,he.A)(r)?JSON.stringify(r,null,2):r,onChange:function(e){try{var t=JSON.parse(e.target.value);d(t)}catch(t){d(e.target.value)}},placeholder:u.placeholder||"Enter object as JSON",rows:6,status:p.valid?"":"error"},s)),g(),f());case"json":return m.createElement("div",null,m.createElement(Ht,(0,S.A)({value:"string"==typeof r?r:JSON.stringify(r,null,2),onChange:function(e){return d(e.target.value)},placeholder:u.placeholder||"Enter JSON",rows:8,status:p.valid?"":"error",style:{fontFamily:"monospace"}},s)),g(),f());default:return m.createElement("div",null,m.createElement(K.A,(0,S.A)({value:r,onChange:function(e){return d(e.target.value)},placeholder:u.placeholder||"Enter value",status:p.valid?"":"error"},s)),g(),f())}};var Wt,Zt,Xt,Jt=n(6914),Gt=n(2877),Yt=n(7345),$t=n(3108),qt=["properties","onFilter","showGroupFilter","showTypeFilter","placeholder"],Kt=O.A.Text,_t=Ue.A.Option,Qt=w.I4.div(Wt||(Wt=(0,u.A)(["\n  padding: 12px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafafa;\n"]))),en=w.I4.div(Zt||(Zt=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n"]))),tn=w.I4.div(Xt||(Xt=(0,u.A)(["\n  display: flex;\n  flex-wrap: wrap;\n  gap: 4px;\n  margin-top: 8px;\n"])));const nn=function(e){var t=e.properties,n=void 0===t?{}:t,r=e.onFilter,l=e.showGroupFilter,a=void 0===l||l,o=e.showTypeFilter,i=void 0===o||o,c=e.placeholder,u=void 0===c?"Search properties...":c,p=((0,k.A)(e,qt),(0,m.useState)("")),d=(0,s.A)(p,2),f=d[0],g=d[1],b=(0,m.useState)("all"),v=(0,s.A)(b,2),y=v[0],h=v[1],x=(0,m.useState)("all"),E=(0,s.A)(x,2),A=E[0],w=E[1],C=(0,m.useMemo)((function(){var e=new Set,t=new Set;return Object.values(n).forEach((function(n){n.group&&e.add(n.group),n.type&&t.add(n.type)})),{groups:Array.from(e).sort(),types:Array.from(t).sort()}}),[n]),S=C.groups,O=C.types,z=(0,m.useMemo)((function(){var e={};return Object.entries(n).forEach((function(t){var n=(0,s.A)(t,2),r=n[0],l=n[1],a=!f||r.toLowerCase().includes(f.toLowerCase())||l.label&&l.label.toLowerCase().includes(f.toLowerCase())||l.description&&l.description.toLowerCase().includes(f.toLowerCase()),o="all"===y||l.group===y,i="all"===A||l.type===A;a&&o&&i&&(e[r]=l)})),e}),[n,f,y,A]),P=[f&&"search","all"!==y&&"group","all"!==A&&"type"].filter(Boolean),j=function(e){return e.charAt(0).toUpperCase()+e.slice(1).replace(/([A-Z])/g," $1")};return m.createElement(Qt,null,m.createElement(en,null,m.createElement(K.A,{prefix:m.createElement(Gt.A,null),placeholder:u,value:f,onChange:function(e){var t=e.target.value;g(t),null==r||r(z,{searchTerm:t,group:y,type:A})},allowClear:!0,style:{flex:1}}),a&&S.length>0&&m.createElement(Ue.A,{value:y,onChange:function(e){h(e),null==r||r(z,{searchTerm:f,group:e,type:A})},style:{minWidth:120},size:"small"},m.createElement(_t,{value:"all"},"All Groups"),S.map((function(e){return m.createElement(_t,{key:e,value:e},j(e))}))),i&&O.length>0&&m.createElement(Ue.A,{value:A,onChange:function(e){w(e),null==r||r(z,{searchTerm:f,group:y,type:e})},style:{minWidth:100},size:"small"},m.createElement(_t,{value:"all"},"All Types"),O.map((function(e){return m.createElement(_t,{key:e,value:e},j(e))}))),P.length>0&&m.createElement(Yt.A,{onClick:function(){g(""),h("all"),w("all"),null==r||r(n,{searchTerm:"",group:"all",type:"all"})},style:{cursor:"pointer",color:"#8c8c8c"},title:"Clear all filters"})),P.length>0&&m.createElement("div",null,m.createElement($.A,{size:4,style:{marginBottom:4}},m.createElement($t.A,{style:{fontSize:"12px",color:"#8c8c8c"}}),m.createElement(Kt,{type:"secondary",style:{fontSize:"12px"}},"Active filters:")),m.createElement(tn,null,f&&m.createElement(Jt.A,{closable:!0,onClose:function(){g(""),null==r||r(z,{searchTerm:"",group:y,type:A})},size:"small"},'Search: "',f,'"'),"all"!==y&&m.createElement(Jt.A,{closable:!0,onClose:function(){h("all"),null==r||r(z,{searchTerm:f,group:"all",type:A})},size:"small"},"Group: ",j(y)),"all"!==A&&m.createElement(Jt.A,{closable:!0,onClose:function(){w("all"),null==r||r(z,{searchTerm:f,group:y,type:"all"})},size:"small"},"Type: ",j(A)))),m.createElement($.A,{style:{marginTop:8,fontSize:"12px"}},m.createElement(Kt,{type:"secondary"},"Showing ",Object.keys(z).length," of ",Object.keys(n).length," properties")))};var rn,ln,an,on,cn,sn,un,pn,dn=n(9356),mn=n(2120),fn=n(8602),gn=n(1143),bn=n(321),vn=n(4700),yn=n(4103),hn=n(8e3),xn=n(741),En=["groupName","properties","values","onChange","componentType","collapsible","defaultExpanded","showResetAll","showPropertyCount"],An=(dn.A.Panel,O.A.Text),wn=w.I4.div(rn||(rn=(0,u.A)(["\n  margin-bottom: 16px;\n"]))),Cn=w.I4.div(ln||(ln=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 12px;\n  background: #fafafa;\n  border: 1px solid #f0f0f0;\n  border-radius: 4px;\n  cursor: pointer;\n  user-select: none;\n  \n  &:hover {\n    background: #f5f5f5;\n  }\n"]))),Sn=w.I4.div(an||(an=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n"]))),kn=w.I4.div(on||(on=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 4px;\n"]))),On=w.I4.div(cn||(cn=(0,u.A)(["\n  padding: 12px;\n  border-bottom: 1px solid #f0f0f0;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n"]))),zn=w.I4.div(sn||(sn=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 8px;\n"]))),Pn=(0,w.I4)(An)(un||(un=(0,u.A)(["\n  font-weight: 500;\n  font-size: 13px;\n"]))),jn=(0,w.I4)(An)(pn||(pn=(0,u.A)(["\n  font-size: 12px;\n  color: #8c8c8c;\n  display: block;\n  margin-top: 2px;\n"])));const Dn=function(e){var t,n=e.groupName,r=e.properties,l=void 0===r?{}:r,a=e.values,o=void 0===a?{}:a,i=e.onChange,c=e.componentType,u=e.collapsible,p=void 0===u||u,d=e.defaultExpanded,f=void 0===d||d,g=e.showResetAll,b=void 0===g||g,v=e.showPropertyCount,y=void 0===v||v,h=(0,k.A)(e,En),x=(0,m.useState)(f),E=(0,s.A)(x,2),A=E[0],w=E[1],C=function(e,t,n){i&&i(t,e,n)},O=Object.keys(l).filter((function(e){var t=o[e];return t!==(l[e].defaultValue||"")&&""!==t&&null!=t})).length,z=Object.keys(l).length;return 0===z?null:m.createElement(wn,null,m.createElement(Cn,{onClick:function(){p&&w(!A)}},m.createElement(Sn,null,p&&(A?m.createElement(yn.A,{style:{fontSize:"12px"}}):m.createElement(hn.A,{style:{fontSize:"12px"}})),(t=n,{basic:m.createElement(fn.A,null),dimensions:m.createElement(gn.A,null),spacing:m.createElement(bn.A,null),typography:m.createElement(gt.A,null),colors:m.createElement(_.A,null),border:m.createElement(Ve.A,null),shadow:m.createElement(vn.A,null),layout:m.createElement(bn.A,null)}[t.toLowerCase()]||m.createElement(fn.A,null)),m.createElement(An,{strong:!0,style:{fontSize:"14px"}},function(e){return e.charAt(0).toUpperCase()+e.slice(1).replace(/([A-Z])/g," $1")}(n)),y&&m.createElement($.A,{size:4},m.createElement(mn.A,{count:z,size:"small",color:"#f0f0f0",style:{color:"#8c8c8c"}}),O>0&&m.createElement(mn.A,{count:O,size:"small",color:"#1890ff"}))),m.createElement(kn,null,b&&O>0&&m.createElement(P.A,{title:"Reset all properties in this group"},m.createElement(J.Ay,{type:"text",size:"small",icon:m.createElement(xn.A,null),onClick:function(e){e.stopPropagation(),Object.keys(l).forEach((function(e){var t=l[e],n=t.defaultValue||"";C(n,e,t)}))},style:{fontSize:"12px"}})))),A&&m.createElement("div",{style:{border:"1px solid #f0f0f0",borderTop:"none",borderRadius:"0 0 4px 4px"}},Object.entries(l).map((function(e){var t=(0,s.A)(e,2),n=t[0],r=t[1],a=o[n],i=void 0!==a&&""!==a&&null!==a&&a!==(r.defaultValue||"");return m.createElement(On,{key:n},m.createElement(zn,null,m.createElement("div",null,m.createElement(Pn,null,r.label||n,r.required&&m.createElement(An,{type:"danger"}," *")),r.description&&m.createElement(jn,null,r.description)),i&&m.createElement(P.A,{title:"Reset to default"},m.createElement(J.Ay,{type:"text",size:"small",icon:m.createElement(xn.A,null),onClick:function(e){return function(e,t){t.stopPropagation();var n=l[e],r=n.defaultValue||"";C(r,e,n)}(n,e)},style:{fontSize:"12px"}}))),m.createElement(Vt,(0,S.A)({propertyName:n,value:a,onChange:C,componentType:c,schema:r,size:"small"},h)))}))))};var Tn,In,Mn,Rn,Ln,Fn=n(677),Bn=n(7197),Hn=n(234),Nn=n(8597),Un=n(6008),Vn=["component","properties","values","showPreview","showCode","showValidation","onReset"];function Wn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Zn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Wn(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Wn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Xn=O.A.Text,Jn=(O.A.Title,w.I4.div(Tn||(Tn=(0,u.A)(["\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  background: white;\n  border-bottom: 1px solid #f0f0f0;\n"])))),Gn=w.I4.div(In||(In=(0,u.A)(["\n  min-height: 120px;\n  padding: 16px;\n  background: ",";\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  overflow: hidden;\n"])),(function(e){return e.background||"#f5f5f5"})),Yn=w.I4.div(Mn||(Mn=(0,u.A)(["\n  transition: all 0.2s ease;\n  ","\n"])),(function(e){return e.styles||""})),$n=w.I4.pre(Rn||(Rn=(0,u.A)(["\n  background: #f6f8fa;\n  border: 1px solid #e1e4e8;\n  border-radius: 4px;\n  padding: 12px;\n  font-size: 12px;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  overflow-x: auto;\n  max-height: 200px;\n  margin: 0;\n"]))),qn=w.I4.div(Ln||(Ln=(0,u.A)(["\n  margin-top: 8px;\n"])));const Kn=function(e){var t=e.component,n=e.properties,r=void 0===n?{}:n,l=e.values,a=void 0===l?{}:l,o=e.showPreview,i=void 0===o||o,c=e.showCode,u=void 0!==c&&c,p=e.showValidation,d=void 0===p||p,f=e.onReset,g=((0,k.A)(e,Vn),(0,m.useState)(i)),b=(0,s.A)(g,2),v=b[0],y=b[1],h=(0,m.useState)(u),x=(0,s.A)(h,2),E=x[0],A=x[1],w=(0,m.useState)({}),C=(0,s.A)(w,2),O=C[0],z=C[1],P=(0,m.useMemo)((function(){var e={};return Object.entries(a).forEach((function(t){var n=(0,s.A)(t,2),r=n[0],l=n[1];if(null!=l&&""!==l){var a=r.replace(/([A-Z])/g,"-$1").toLowerCase();"object"===(0,he.A)(l)&&null!==l?"font"===r||"fontFamily"===r?Object.entries(l).forEach((function(t){var n=(0,s.A)(t,2),r=n[0],l=n[1],a=r.replace(/([A-Z])/g,"-$1").toLowerCase();e[a]=l})):e[a]=JSON.stringify(l):e[a]=l}})),e}),[a]),j=(0,m.useMemo)((function(){var e=Object.entries(P).map((function(e){var t=(0,s.A)(e,2),n=t[0],r=t[1];return"  ".concat(n,": ").concat(r,";")}));return".component {\n".concat(e.join("\n"),"\n}")}),[P]),D=(0,m.useMemo)((function(){var e={};return Object.entries(P).forEach((function(t){var n=(0,s.A)(t,2),r=n[0],l=n[1],a=r.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}));e[a]=l})),e}),[P]);(0,m.useEffect)((function(){var e={};Object.entries(a).forEach((function(t){var n=(0,s.A)(t,2),l=n[0],a=n[1],o=r[l];if(o&&null!=a&&""!==a)if("number"===o.type){var i=parseFloat(a);isNaN(i)?e[l]="Must be a valid number":void 0!==o.min&&i<o.min?e[l]="Must be at least ".concat(o.min):void 0!==o.max&&i>o.max&&(e[l]="Must be at most ".concat(o.max))}else"color"===o.type&&(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$|^rgb\(|^rgba\(|^hsl\(|^hsla\(/.test(a)||(e[l]="Must be a valid color value"))})),z(e)}),[a,r]);var T=Object.keys(O).length>0,I=Object.keys(a).some((function(e){return void 0!==a[e]&&""!==a[e]&&null!==a[e]}));return m.createElement(Jn,null,m.createElement(Fn.A,{size:"small",title:"Property Preview"},m.createElement($.A,{direction:"vertical",style:{width:"100%"}},m.createElement($.A,{style:{width:"100%",justifyContent:"space-between"}},m.createElement($.A,null,m.createElement(nt.A,{checked:v,onChange:y,checkedChildren:m.createElement(Hn.A,null),unCheckedChildren:m.createElement(Nn.A,null),size:"small"}),m.createElement(Xn,{style:{fontSize:"12px"}},"Live Preview")),m.createElement($.A,null,m.createElement(J.Ay,{type:"text",size:"small",icon:m.createElement(Un.A,null),onClick:function(){return A(!E)},style:{fontSize:"12px"}},E?"Hide":"Show"," CSS"),I&&m.createElement(J.Ay,{type:"text",size:"small",icon:m.createElement(xn.A,null),onClick:f,style:{fontSize:"12px"}},"Reset All"))),v&&m.createElement(Gn,null,m.createElement(Yn,{styles:Object.entries(D).map((function(e){var t=(0,s.A)(e,2),n=t[0],r=t[1];return"".concat(n,": ").concat(r,";")})).join(" ")},function(){var e={style:D,className:"preview-element"};switch(null==t?void 0:t.type){case"button":return m.createElement("button",e,a.text||"Button");case"text":var n=a.variant||"p";return m.createElement(n,e,a.content||"Sample text");case"input":return m.createElement("input",(0,S.A)({},e,{type:a.type||"text",placeholder:a.placeholder||"Enter text",disabled:a.disabled}));case"card":return m.createElement("div",(0,S.A)({},e,{style:Zn(Zn({},D),{},{border:"1px solid #d9d9d9",borderRadius:"4px",padding:"16px",minWidth:"200px"})}),a.title&&m.createElement("h4",{style:{margin:"0 0 8px 0"}},a.title),a.description&&m.createElement("p",{style:{margin:0,color:"#666"}},a.description));default:return m.createElement("div",(0,S.A)({},e,{style:Zn(Zn({},D),{},{padding:"16px",background:"#fff",border:"1px solid #d9d9d9",borderRadius:"4px"})}),(null==t?void 0:t.name)||"Component"," Preview")}}())),E&&m.createElement(m.Fragment,null,m.createElement(G.A,{style:{margin:"8px 0"}}),m.createElement("div",null,m.createElement(Xn,{strong:!0,style:{fontSize:"12px"}},"Generated CSS:"),m.createElement($n,null,j))),d&&T&&m.createElement(qn,null,m.createElement(Bn.A,{message:"Validation Errors",description:m.createElement("ul",{style:{margin:0,paddingLeft:"16px"}},Object.entries(O).map((function(e){var t=(0,s.A)(e,2),n=t[0],r=t[1];return m.createElement("li",{key:n,style:{fontSize:"12px"}},m.createElement("strong",null,n,":")," ",r)}))),type:"error",size:"small",showIcon:!0})),d&&!T&&I&&m.createElement(qn,null,m.createElement(Bn.A,{message:"All properties are valid",type:"success",size:"small",showIcon:!0})))))};var _n,Qn,er,tr,nr=n(2395),rr=n(1005),lr=n(1616),ar=n(2543),or=["name"];function ir(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function cr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ir(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ir(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}O.A.Title;var sr=O.A.Text,ur=nr.A.TabPane,pr=w.I4.div(_n||(_n=(0,u.A)(["\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n"]))),dr=w.I4.div(Qn||(Qn=(0,u.A)(["\n  flex: 1;\n  overflow-y: auto;\n  padding: 0;\n"]))),mr=w.I4.div(er||(er=(0,u.A)(["\n  padding: 16px;\n  border-top: 1px solid #f0f0f0;\n  background: #fafafa;\n"]))),fr=w.I4.div(tr||(tr=(0,u.A)(["\n  padding: 0;\n"])));const gr=function(e){var t=e.component,n=e.onUpdate,r=e.onRealTimeUpdate,l=e.enableRealTimePreview,a=void 0===l||l,o=e.enableCollaboration,c=void 0!==o&&o,u=e.collaborativeSession,p=void 0===u?null:u,d=(0,f.wA)(),g=(0,m.useState)("basic"),v=(0,s.A)(g,2),y=v[0],h=v[1],x=(0,m.useState)({}),E=(0,s.A)(x,2),A=E[0],w=E[1],C=(0,m.useState)({}),S=(0,s.A)(C,2),O=(S[0],S[1]),z=(0,m.useState)(!1),j=(0,s.A)(z,2),D=j[0],T=j[1],I=(0,m.useState)(a),M=(0,s.A)(I,2),R=M[0],L=M[1],F=(0,m.useState)(!1),B=(0,s.A)(F,2),H=B[0],N=B[1],U=(0,m.useState)(null),V=(0,s.A)(U,2),W=V[0],Z=V[1],X=(0,m.useRef)(null),G=(0,m.useRef)({});(0,m.useEffect)((function(){if(t){var e=cr(cr({name:t.name},t.props),t.style);w(e),T(!1)}}),[t]);var Y=(0,m.useMemo)((function(){return null!=t&&t.type?(e=t.type,Rt[e]||{}):{};var e}),[null==t?void 0:t.type]),q=(0,m.useMemo)((function(){return e={},Object.entries(Lt).forEach((function(t){var n=(0,s.A)(t,2),r=n[0],l=n[1],a=l.group||"other";e[a]||(e[a]={}),e[a][r]=l})),e;var e}),[]),K=(0,m.useMemo)((function(){return(0,ar.debounce)((function(e){R&&r&&(N(!0),r(e),Z(new Date),setTimeout((function(){return N(!1)}),300))}),300)}),[R,r]),_=(0,m.useCallback)((function(e){n&&n(e)}),[n]),Q=(0,m.useCallback)((function(e,n,r){var l=cr(cr({},A),{},(0,i.A)({},e,n));w(l),T(!0);var a=cr(cr({},t),{},{name:"name"===e?n:l.name,props:cr(cr({},t.props),"name"===e||Lt[e]?{}:(0,i.A)({},e,n)),style:cr(cr({},t.style),Lt[e]?(0,i.A)({},e,n):{})});_(a),R&&K(a),G.current=l}),[A,t,R,_,K]),ee=function(){if(t){var e=cr(cr({name:t.name},t.props),t.style);w(e),T(!1)}},te=function(e,t){O(e)};(0,m.useEffect)((function(){return function(){X.current&&clearTimeout(X.current),K.cancel()}}),[K]);var ne=(0,m.useCallback)((function(e){if(L(e),e&&D){var n=cr(cr({},t),{},{name:A.name||t.name,props:cr({},t.props),style:cr({},t.style)});Object.entries(A).forEach((function(e){var t=(0,s.A)(e,2),r=t[0],l=t[1];"name"!==r&&(Lt[r]?n.style[r]=l:n.props[r]=l)})),K(n)}}),[D,t,A,K]);return t?m.createElement(pr,null,m.createElement("div",{style:{padding:"12px 16px",borderBottom:"1px solid #f0f0f0",background:"#fafafa",display:"flex",alignItems:"center",justifyContent:"space-between"}},m.createElement($.A,null,m.createElement(sr,{strong:!0,style:{fontSize:"14px"}},t.name||t.type," Properties"),H&&m.createElement(rr.A,{spin:!0,style:{color:"#1890ff"}})),m.createElement($.A,null,a&&m.createElement(P.A,{title:"Toggle real-time preview updates"},m.createElement(nt.A,{size:"small",checked:R,onChange:ne,checkedChildren:m.createElement(Hn.A,null),unCheckedChildren:m.createElement(Hn.A,null)})),W&&R&&m.createElement(mn.A,{status:"success",text:"Updated ".concat(W.toLocaleTimeString()),style:{fontSize:"11px"}}),c&&p&&m.createElement(mn.A,{count:p.collaboratorCount||0,showZero:!1,style:{backgroundColor:"#52c41a"}},m.createElement(P.A,{title:"Active collaborators"},m.createElement(J.Ay,{size:"small",type:"text",icon:m.createElement(rr.A,null)}))))),m.createElement(Kn,{component:t,properties:cr(cr({},Y),Lt),values:A,onReset:ee,showPreview:!0,showCode:!1,showValidation:!0,realTimeEnabled:R}),m.createElement(dr,null,m.createElement(nr.A,{activeKey:y,onChange:h,size:"small"},m.createElement(ur,{tab:"Properties",key:"basic"},m.createElement(fr,null,m.createElement(nn,{properties:Y,onFilter:te,placeholder:"Search component properties..."}),m.createElement(Dn,{groupName:"basic",properties:{name:{type:"text",label:"Component Name",required:!0,placeholder:"Enter component name"}},values:A,onChange:Q,componentType:t.type,defaultExpanded:!0}),Object.keys(Y).length>0&&m.createElement(Dn,{groupName:"component",properties:Y,values:A,onChange:Q,componentType:t.type,defaultExpanded:!0}))),m.createElement(ur,{tab:"Styling",key:"style"},m.createElement(fr,null,m.createElement(nn,{properties:Lt,onFilter:te,placeholder:"Search style properties..."}),Object.entries(q).map((function(e){var n=(0,s.A)(e,2),r=n[0],l=n[1];return m.createElement(Dn,{key:r,groupName:r,properties:l,values:A,onChange:Q,componentType:t.type,defaultExpanded:"dimensions"===r||"colors"===r})})))),m.createElement(ur,{tab:"Advanced",key:"advanced"},m.createElement(fr,null,m.createElement(Dn,{groupName:"advanced",properties:{customProps:{type:"json",label:"Custom Properties",placeholder:"Enter custom properties as JSON",description:"Additional properties not covered by the standard options"},customStyles:{type:"json",label:"Custom Styles",placeholder:"Enter custom styles as JSON",description:"Additional CSS styles not covered by the standard options"}},values:A,onChange:Q,componentType:t.type,defaultExpanded:!0}))))),m.createElement(mr,null,m.createElement($.A,{style:{width:"100%",justifyContent:"space-between"}},m.createElement($.A,null,D&&m.createElement(sr,{type:"warning",style:{fontSize:"12px"}},"Unsaved changes")),m.createElement($.A,null,m.createElement(J.Ay,{size:"small",icon:m.createElement(xn.A,null),onClick:ee,disabled:!D},"Reset"),m.createElement(J.Ay,{type:"primary",size:"small",icon:m.createElement(b.A,null),onClick:function(){if(t){var e=A.name,r=(0,k.A)(A,or),l={},a={};Object.entries(r).forEach((function(e){var t=(0,s.A)(e,2),n=t[0],r=t[1];Lt[n]?a[n]=r:l[n]=r}));var o=cr(cr({},t),{},{name:e||t.name,props:cr(cr({},t.props),l),style:cr(cr({},t.style),a)});d((0,lr.ZP)(o)),T(!1),n&&n(o)}},disabled:!D},"Apply Changes"))))):m.createElement(pr,null,m.createElement("div",{style:{padding:"24px",textAlign:"center"}},m.createElement(sr,{type:"secondary"},"Select a component to edit its properties")))};var br,vr,yr,hr,xr,Er,Ar,wr,Cr,Sr,kr=n(5448),Or=n(9740),zr=n(9029),Pr=n(149),jr=n(5061),Dr=n(8027),Tr=n(1250),Ir=n(7011),Mr=n(3674),Rr=n(5710),Lr=n(5824),Fr=n(8620),Br=n(9932),Hr=n(9277),Nr=n(7713),Ur=n(6362),Vr=n(7265),Wr=n(3341),Zr=n(845),Xr=n(9748);function Jr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Gr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Jr(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Jr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Yr=K.A.Search,$r=O.A.Text,qr=O.A.Title,Kr=(dn.A.Panel,Tr.Ay.div(br||(br=(0,u.A)(["\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n"])))),_r=Tr.Ay.div(vr||(vr=(0,u.A)(["\n  padding: 16px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  \n  .ant-typography {\n    color: white !important;\n    margin-bottom: 8px;\n  }\n"]))),Qr=Tr.Ay.div(yr||(yr=(0,u.A)(["\n  padding: 12px 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n"]))),el=(0,Tr.Ay)(Fn.A)(hr||(hr=(0,u.A)(["\n  margin: 4px;\n  cursor: grab;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  background: ",";\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n    border-color: #1890ff;\n  }\n  \n  &:active {\n    cursor: grabbing;\n    transform: scale(0.98);\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    text-align: center;\n  }\n"])),(function(e){return e.isDragging?"#e6f7ff":"#fff"})),tl=Tr.Ay.div(xr||(xr=(0,u.A)(["\n  font-size: 24px;\n  color: #1890ff;\n  margin-bottom: 8px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: rgba(24, 144, 255, 0.1);\n  margin: 0 auto 8px;\n  transition: all 0.3s ease;\n  \n  ",":hover & {\n    background: rgba(24, 144, 255, 0.2);\n    transform: scale(1.1);\n  }\n"])),el),nl=Tr.Ay.div(Er||(Er=(0,u.A)(["\n  font-size: 12px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 4px;\n"]))),rl=Tr.Ay.div(Ar||(Ar=(0,u.A)(["\n  font-size: 10px;\n  color: #666;\n  line-height: 1.2;\n"]))),ll=Tr.Ay.div(wr||(wr=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 16px;\n  background: #f0f2f5;\n  border-bottom: 1px solid #d9d9d9;\n  font-weight: 600;\n  color: #333;\n"]))),al=Tr.Ay.div(Cr||(Cr=(0,u.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\n  gap: 8px;\n  padding: 16px;\n"]))),ol=Tr.Ay.div(Sr||(Sr=(0,u.A)(["\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  color: #bbb;\n  font-size: 12px;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  \n  ",":hover & {\n    opacity: 1;\n  }\n"])),el);const il=function(e){var t=e.onAddComponent,n=e.onDragStart,r=e.onDragEnd,l=(0,m.useState)(""),a=(0,s.A)(l,2),o=a[0],i=a[1],c=(0,m.useState)(["Layout","Basic Components"]),u=(0,s.A)(c,2),p=u[0],d=u[1],f=(0,m.useState)(!0),g=(0,s.A)(f,2),b=g[0],v=g[1],h=(0,m.useState)(null),x=(0,s.A)(h,2),E=x[0],A=x[1],w=(0,m.useRef)(null),C=[{title:"Layout",description:"Structural components for organizing content",color:"#52c41a",components:[{type:"header",icon:m.createElement(gt.A,null),label:"Header",description:"Page or section header with title and navigation",usage:"Use for page titles, navigation bars, or section headers",tags:["layout","navigation","title"]},{type:"section",icon:m.createElement(bn.A,null),label:"Section",description:"Container for grouping related content",usage:"Organize content into logical sections",tags:["layout","container","organization"]},{type:"card",icon:m.createElement(Ir.A,null),label:"Card",description:"Flexible content container with optional header and footer",usage:"Display content in a clean, contained format",tags:["layout","container","content"]},{type:"tabs",icon:m.createElement(Mr.A,null),label:"Tabs",description:"Tabbed interface for organizing content",usage:"Switch between different views or content sections",tags:["layout","navigation","organization"]},{type:"divider",icon:m.createElement(Mr.A,null),label:"Divider",description:"Visual separator between content sections",usage:"Separate content sections visually",tags:["layout","separator","visual"]}]},{title:"Basic Components",description:"Essential UI elements for content and interaction",color:"#1890ff",components:[{type:"text",icon:m.createElement(Rr.A,null),label:"Text",description:"Formatted text content with typography options",usage:"Display paragraphs, headings, and formatted text",tags:["content","text","typography"]},{type:"button",icon:m.createElement(y.A,null),label:"Button",description:"Interactive button for user actions",usage:"Trigger actions, submit forms, or navigate",tags:["interaction","action","click"]},{type:"image",icon:m.createElement(Lr.A,null),label:"Image",description:"Display images with responsive sizing",usage:"Show photos, illustrations, or graphics",tags:["media","visual","content"]},{type:"list",icon:m.createElement(Fr.A,null),label:"List",description:"Ordered or unordered list of items",usage:"Display collections of related items",tags:["content","organization","items"]},{type:"tag",icon:m.createElement(Br.A,null),label:"Tag",description:"Small label for categorization or status",usage:"Label content, show status, or categorize",tags:["label","status","category"]}]},{title:"Form Components",description:"Interactive elements for user input and data collection",color:"#722ed1",components:[{type:"form",icon:m.createElement(Hr.A,null),label:"Form",description:"Container for form fields with validation",usage:"Collect user input with validation and submission",tags:["input","validation","data"]},{type:"input",icon:m.createElement(Hr.A,null),label:"Input",description:"Text input field for user data entry",usage:"Collect text, numbers, or other typed input",tags:["input","text","data"]},{type:"select",icon:m.createElement(Hr.A,null),label:"Select",description:"Dropdown selection from predefined options",usage:"Choose from a list of predefined options",tags:["input","selection","dropdown"]},{type:"checkbox",icon:m.createElement(Nr.A,null),label:"Checkbox",description:"Boolean input for yes/no or multiple selections",usage:"Select multiple options or toggle settings",tags:["input","boolean","selection"]},{type:"datepicker",icon:m.createElement(Ur.A,null),label:"Date Picker",description:"Calendar interface for date selection",usage:"Select dates, date ranges, or schedule events",tags:["input","date","calendar"]},{type:"slider",icon:m.createElement(Vr.A,null),label:"Slider",description:"Range input with visual slider interface",usage:"Select numeric values within a range",tags:["input","range","numeric"]}]},{title:"Data Components",description:"Components for displaying and visualizing data",color:"#fa8c16",components:[{type:"table",icon:m.createElement(Wr.A,null),label:"Table",description:"Structured data display with sorting and filtering",usage:"Display tabular data with advanced features",tags:["data","table","structured"]},{type:"chart",icon:m.createElement(Zr.A,null),label:"Chart",description:"Visual data representation with multiple chart types",usage:"Visualize data trends and comparisons",tags:["data","visualization","analytics"]},{type:"statistic",icon:m.createElement(Zr.A,null),label:"Statistic",description:"Highlighted numeric data with formatting",usage:"Display key metrics and KPIs prominently",tags:["data","metrics","numbers"]}]}],S=(0,m.useMemo)((function(){return o?C.map((function(e){return Gr(Gr({},e),{},{components:e.components.filter((function(e){return e.label.toLowerCase().includes(o.toLowerCase())||e.description.toLowerCase().includes(o.toLowerCase())||e.tags.some((function(e){return e.toLowerCase().includes(o.toLowerCase())}))}))})})).filter((function(e){return e.components.length>0})):C}),[o]),k=function(e){A(null),r&&r()};return m.createElement(Kr,null,m.createElement(_r,null,m.createElement(qr,{level:5,style:{margin:0,color:"white"}},"Component Palette"),m.createElement($r,{style:{color:"rgba(255, 255, 255, 0.8)"}},"Drag components to the canvas or click to add")),m.createElement(Qr,null,m.createElement(Yr,{placeholder:"Search components...",value:o,onChange:function(e){return i(e.target.value)},prefix:m.createElement(Gt.A,null),allowClear:!0,style:{marginBottom:8}}),m.createElement($.A,null,m.createElement($r,{style:{fontSize:12}},"Show descriptions:"),m.createElement(nt.A,{size:"small",checked:b,onChange:v}))),S.map((function(e){return m.createElement("div",{key:e.title},m.createElement(ll,{onClick:function(){return t=e.title,void d((function(e){return e.includes(t)?e.filter((function(e){return e!==t})):[].concat((0,X.A)(e),[t])}));var t}},m.createElement($.A,null,m.createElement("div",{style:{width:12,height:12,borderRadius:"50%",backgroundColor:e.color}}),m.createElement("span",null,e.title),m.createElement(mn.A,{count:e.components.length,size:"small"})),m.createElement(yn.A,{style:{transform:p.includes(e.title)?"rotate(180deg)":"rotate(0deg)",transition:"transform 0.3s ease"}})),p.includes(e.title)&&m.createElement(al,null,e.components.map((function(e){return m.createElement(el,{key:e.type,size:"small",hoverable:!0,isDragging:(null==E?void 0:E.type)===e.type,draggable:!0,onDragStart:function(t){return function(e,t){if(A(t),e.dataTransfer.setData("application/json",JSON.stringify({type:t.type,label:t.label,source:"palette"})),e.dataTransfer.effectAllowed="copy",w.current){var r=w.current.cloneNode(!0);r.style.transform="rotate(5deg)",r.style.opacity="0.8",e.dataTransfer.setDragImage(r,60,30)}n&&n(t)}(t,e)},onDragEnd:k,onClick:function(){return t(e.type)},ref:(null==E?void 0:E.type)===e.type?w:null},m.createElement(ol,null,m.createElement(Xr.A,null)),m.createElement(tl,null,e.icon),m.createElement(nl,null,e.label),b&&m.createElement(rl,null,e.description),m.createElement(P.A,{title:m.createElement("div",null,m.createElement("div",{style:{fontWeight:"bold",marginBottom:4}},e.label),m.createElement("div",{style:{marginBottom:8}},e.description),m.createElement("div",{style:{fontSize:11,color:"#ccc"}},m.createElement("strong",null,"Usage:")," ",e.usage),m.createElement("div",{style:{fontSize:11,color:"#ccc",marginTop:4}},m.createElement("strong",null,"Tags:")," ",e.tags.join(", "))),placement:"right"},m.createElement(D.A,{style:{position:"absolute",top:4,left:4,fontSize:10,color:"#bbb",opacity:.7}})))}))))})),0===S.length&&m.createElement("div",{style:{padding:32,textAlign:"center",color:"#999"}},m.createElement(Gt.A,{style:{fontSize:24,marginBottom:8}}),m.createElement("div",null,'No components found matching "',o,'"'),m.createElement(J.Ay,{type:"link",size:"small",onClick:function(){return i("")},style:{padding:0,marginTop:8}},"Clear search")))};var cl=n(9467),sl=n(6955),ul=n(7308),pl=n(9091),dl=n(448),ml=n(2648),fl=n(1656),gl=n(5163),bl=n(7826),vl=n(9445),yl=n(9459);function hl(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var xl,El,Al,wl,Cl,Sl,kl,Ol,zl,Pl,jl,Dl,Tl;function Il(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ml(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Il(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Il(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Rl=O.A.Title,Ll=O.A.Text,Fl=(O.A.Paragraph,Ue.A.Option,{mobile:{name:"Mobile",icon:m.createElement(pl.A,null),width:375,height:667,scale:.8,frame:!0},tablet:{name:"Tablet",icon:m.createElement(dl.A,null),width:768,height:1024,scale:.7,frame:!0},desktop:{name:"Desktop",icon:m.createElement(ml.A,null),width:1200,height:800,scale:1,frame:!1}}),Bl=Tr.Ay.div(xl||(xl=(0,u.A)(["\n  position: relative;\n  height: 100%;\n  background: #f5f5f5;\n  border-radius: 8px;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n"]))),Hl=Tr.Ay.div(El||(El=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 16px;\n  background: white;\n  border-bottom: 1px solid #e8e8e8;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  z-index: 10;\n  flex-wrap: wrap;\n  gap: 8px;\n\n  @media (max-width: 768px) {\n    padding: 6px 12px;\n    gap: 6px;\n  }\n"]))),Nl=Tr.Ay.div(Al||(Al=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 4px;\n  background: #f5f5f5;\n  border-radius: 6px;\n"]))),Ul=(0,Tr.Ay)(J.Ay)(wl||(wl=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  border: none;\n  background: ",";\n  color: ",";\n  box-shadow: none;\n\n  &:hover {\n    background: ",";\n    color: ",";\n  }\n"])),(function(e){return e.active?"#1890ff":"transparent"}),(function(e){return e.active?"white":"#666"}),(function(e){return e.active?"#40a9ff":"#e6f7ff"}),(function(e){return e.active?"white":"#1890ff"})),Vl=Tr.Ay.div(Cl||(Cl=(0,u.A)(["\n  position: relative;\n  margin: 20px auto;\n  background: ",";\n  border-radius: ",";\n  padding: ",";\n  box-shadow: ",";\n  transition: all 0.3s ease;\n\n  ","\n\n  ","\n"])),(function(e){return"mobile"===e.deviceType?"#333":"tablet"===e.deviceType?"#444":"transparent"}),(function(e){return"mobile"===e.deviceType?"25px":"tablet"===e.deviceType?"15px":"0"}),(function(e){return"mobile"===e.deviceType?"20px 10px":"tablet"===e.deviceType?"15px":"0"}),(function(e){return e.frame?"0 8px 32px rgba(0, 0, 0, 0.3)":"none"}),(function(e){return"mobile"===e.deviceType&&"\n    &::before {\n      content: '';\n      position: absolute;\n      top: 8px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 60px;\n      height: 4px;\n      background: #666;\n      border-radius: 2px;\n    }\n\n    &::after {\n      content: '';\n      position: absolute;\n      bottom: 8px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 40px;\n      height: 40px;\n      border: 2px solid #666;\n      border-radius: 50%;\n    }\n  "}),(function(e){return"tablet"===e.deviceType&&"\n    &::before {\n      content: '';\n      position: absolute;\n      bottom: 6px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 30px;\n      height: 30px;\n      border: 2px solid #666;\n      border-radius: 50%;\n    }\n  "})),Wl=Tr.Ay.div(Sl||(Sl=(0,u.A)(["\n  width: ","px;\n  height: ","px;\n  max-width: 100%;\n  max-height: 100%;\n  background: white;\n  border-radius: ",";\n  overflow: auto;\n  position: relative;\n  transform: scale(",");\n  transform-origin: top center;\n  transition: all 0.3s ease;\n\n  @media (max-width: 1200px) {\n    transform: scale(",");\n  }\n\n  @media (max-width: 768px) {\n    transform: scale(",");\n  }\n"])),(function(e){return e.deviceWidth}),(function(e){return e.deviceHeight}),(function(e){return"mobile"===e.deviceType?"8px":"tablet"===e.deviceType?"6px":"0"}),(function(e){return e.scale}),(function(e){return Math.min(e.scale,.8)}),(function(e){return Math.min(e.scale,.6)})),Zl=Tr.Ay.div(kl||(kl=(0,u.A)(["\n  flex: 1;\n  position: relative;\n  overflow: auto;\n  background: ",";\n  background-size: ","px ","px;\n  background-position: ","px ","px;\n"])),(function(e){return e.showGrid?"radial-gradient(circle, #ddd 1px, transparent 1px)":"#f5f5f5"}),(function(e){return e.gridSize||20}),(function(e){return e.gridSize||20}),(function(e){var t;return(null===(t=e.gridOffset)||void 0===t?void 0:t.x)||0}),(function(e){var t;return(null===(t=e.gridOffset)||void 0===t?void 0:t.y)||0})),Xl=(Tr.Ay.div(Ol||(Ol=(0,u.A)(["\n  min-height: 100%;\n  min-width: 100%;\n  position: relative;\n  transform: scale(",");\n  transform-origin: top left;\n  transition: transform 0.2s ease;\n  padding: ",";\n"])),(function(e){return e.zoom||1}),(function(e){return e.previewMode?"0":"32px"})),Tr.Ay.div(zl||(zl=(0,u.A)(["\n  position: relative;\n  margin: 8px 0;\n  border: ",";\n  border-radius: 4px;\n  background: ",";\n  transition: all 0.3s ease;\n  cursor: ",";\n  \n  &:hover {\n    border-color: ",";\n    box-shadow: ",";\n    transform: ",";\n  }\n  \n  ","\n"])),(function(e){return e.isSelected?"2px solid #1890ff":"1px dashed transparent"}),(function(e){return e.isSelected?"rgba(24, 144, 255, 0.05)":"white"}),(function(e){return e.previewMode?"default":"pointer"}),(function(e){return e.previewMode?"transparent":"#1890ff"}),(function(e){return e.previewMode?"none":"0 2px 8px rgba(24, 144, 255, 0.2)"}),(function(e){return e.previewMode?"none":"translateY(-1px)"}),(function(e){return e.isDragOver&&"\n    border-color: #52c41a !important;\n    background: rgba(82, 196, 26, 0.1) !important;\n    transform: scale(1.02);\n  "}))),Jl=Tr.Ay.div(Pl||(Pl=(0,u.A)(["\n  position: absolute;\n  top: -2px;\n  right: -2px;\n  display: flex;\n  gap: 4px;\n  background: rgba(255, 255, 255, 0.95);\n  padding: 4px;\n  border-radius: 4px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n  opacity: ",";\n  transform: translateY(",");\n  transition: all 0.3s ease;\n  z-index: 5;\n"])),(function(e){return e.visible?1:0}),(function(e){return e.visible?"0":"-10px"})),Gl=(0,Tr.Ay)(J.Ay)(jl||(jl=(0,u.A)(["\n  width: 24px;\n  height: 24px;\n  padding: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: none;\n  box-shadow: none;\n  \n  &:hover {\n    background: #f0f0f0;\n    transform: scale(1.1);\n  }\n"]))),Yl=Tr.Ay.div(Dl||(Dl=(0,u.A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border: 2px dashed #d9d9d9;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.8);\n  opacity: ",";\n  pointer-events: ",";\n  transition: all 0.3s ease;\n  z-index: 1;\n  \n  ","\n"])),(function(e){return e.visible?1:0}),(function(e){return e.visible?"auto":"none"}),(function(e){return e.isActive&&"\n    border-color: #52c41a;\n    background: rgba(82, 196, 26, 0.1);\n    \n    &::before {\n      content: 'Drop component here';\n      color: #52c41a;\n      font-weight: 600;\n      font-size: 16px;\n    }\n  "})),$l=Tr.Ay.div(Tl||(Tl=(0,u.A)(["\n  position: absolute;\n  background: rgba(255, 255, 255, 0.9);\n  border: 1px solid #e8e8e8;\n  font-size: 10px;\n  color: #666;\n  z-index: 5;\n  \n  ","\n  \n  ","\n"])),(function(e){return"horizontal"===e.orientation&&"\n    top: 0;\n    left: 32px;\n    right: 0;\n    height: 20px;\n    border-bottom: 1px solid #e8e8e8;\n    background-image: repeating-linear-gradient(\n      to right,\n      transparent,\n      transparent 9px,\n      #e8e8e8 9px,\n      #e8e8e8 10px\n    );\n  "}),(function(e){return"vertical"===e.orientation&&"\n    top: 20px;\n    left: 0;\n    bottom: 0;\n    width: 32px;\n    border-right: 1px solid #e8e8e8;\n    background-image: repeating-linear-gradient(\n      to bottom,\n      transparent,\n      transparent 9px,\n      #e8e8e8 9px,\n      #e8e8e8 10px\n    );\n  "}));const ql=function(e){var t=e.components,n=void 0===t?[]:t,r=e.onSelectComponent,l=e.onDeleteComponent,a=e.onUpdateComponent,o=e.onMoveComponent,c=e.previewMode,u=void 0!==c&&c,p=e.selectedComponentId,d=e.onDrop,g=e.onDragOver,b=e.onDragLeave,v=e.realTimeUpdates,y=void 0===v||v,A=e.websocketConnected,w=void 0!==A&&A,C=(0,m.useState)("desktop"),k=(0,s.A)(C,2),O=k[0],z=k[1],j=(0,m.useState)(1),D=(0,s.A)(j,2),T=D[0],I=D[1],M=(0,m.useState)(!0),R=(0,s.A)(M,2),L=R[0],F=R[1],B=(0,m.useState)(20),H=(0,s.A)(B,2),N=H[0],U=(H[1],(0,m.useState)(!1)),V=(0,s.A)(U,2),W=V[0],Z=V[1],X=(0,m.useState)(!0),Y=(0,s.A)(X,2),q=Y[0],_=Y[1],Q=(0,m.useState)(!1),ee=(0,s.A)(Q,2),te=ee[0],ne=ee[1],re=(0,m.useState)(null),le=(0,s.A)(re,2),ae=le[0],oe=le[1],ie=(0,m.useState)(null),ce=(0,s.A)(ie,2),se=ce[0],ue=ce[1],pe=(0,m.useState)(!1),de=(0,s.A)(pe,2),me=de[0],fe=de[1],ge=(0,m.useState)(null),be=(0,s.A)(ge,2),ve=be[0],ye=be[1],he=(0,m.useRef)(null),xe=(0,m.useRef)(null),Ee=((0,f.wA)(),(0,f.d4)((function(e){return e.websocket||{}})),(0,f.d4)((function(e){var t;return null===(t=e.websocket)||void 0===t?void 0:t.service}))),Ae=Fl[O],we=(0,m.useMemo)((function(){return{width:Ae.width,height:Ae.height,scale:Ae.scale}}),[Ae]),Ce=(0,yl.A)({components:n,onUpdateComponent:a,onAddComponent:function(e){a&&a(e)},onDeleteComponent:l,websocketService:Ee,enableWebSocket:y&&w}),Se=Ce.isUpdating,ke=Ce.lastUpdateTime,Oe=Ce.websocketConnected,ze=Ce.updateComponent,Pe=(Ce.addComponent,Ce.deleteComponent,Ce.getAllComponents),je=(Ce.forceUpdate,function(e){var t=e.components,n=void 0===t?[]:t,r=e.containerHeight,l=void 0===r?600:r,a=e.itemHeight,o=void 0===a?100:a,c=e.overscan,u=void 0===c?5:c,p=e.enableVirtualization,d=void 0===p||p,f=e.enablePerformanceMonitoring,g=void 0===f||f,b=(0,m.useState)(0),v=(0,s.A)(b,2),y=v[0],h=v[1],x=(0,m.useState)(null),E=(0,s.A)(x,2),A=E[0],w=E[1],C=(0,m.useState)({start:0,end:0}),S=(0,s.A)(C,2),k=S[0],O=S[1],z=(0,m.useState)(0),P=(0,s.A)(z,2),j=P[0],D=P[1],T=(0,m.useState)(60),I=(0,s.A)(T,2),M=I[0],R=I[1],L=(0,m.useState)(0),F=(0,s.A)(L,2),B=F[0],H=F[1],N=(0,m.useRef)(0),U=(0,m.useRef)(0),V=(0,m.useRef)(performance.now()),W=(0,m.useRef)(new Map),Z=(0,m.useRef)(null),X=(0,m.useCallback)((function(){if(!d||!A||0===n.length)return{start:0,end:n.length};var e=Math.floor(y/o),t=Math.min(e+Math.ceil(l/o)+u,n.length);return{start:Math.max(0,e-u),end:t}}),[y,o,l,u,n.length,d,A]);(0,m.useEffect)((function(){var e=X();O(e)}),[X]);var J=(0,m.useCallback)((0,ar.throttle)((function(e){e.target&&h(e.target.scrollTop)}),16),[]),G=(0,m.useMemo)((function(){return d?n.slice(k.start,k.end).map((function(e,t){return{component:e,index:k.start+t}})):n.map((function(e,t){return{component:e,index:t}}))}),[n,k,d]),Y=(0,m.useCallback)((function(e,t){var r="".concat(e,"_").concat(JSON.stringify(n.find((function(t){return t.id===e}))));if(W.current.has(r))return W.current.get(r);var l=t();if(W.current.set(r,l),W.current.size>100){var a=W.current.keys().next().value;W.current.delete(a)}return l}),[n]),$=(0,m.useCallback)((function(){g&&(N.current=performance.now())}),[g]),q=(0,m.useCallback)((function(){if(g&&N.current>0){var e=performance.now()-N.current;D(e),N.current=0}}),[g]);(0,m.useEffect)((function(){if(g){var e,t=function(){var n=performance.now(),r=n-V.current;if(r>=1e3){var l=Math.round(1e3*U.current/r);R(l),U.current=0,V.current=n}else U.current++;e=requestAnimationFrame(t)};return e=requestAnimationFrame(t),function(){e&&cancelAnimationFrame(e)}}}),[g]),(0,m.useEffect)((function(){if(g&&performance.memory){var e=function(){var e=performance.memory,t=Math.round(e.usedJSHeapSize/1024/1024);H(t)},t=setInterval(e,5e3);return e(),function(){return clearInterval(t)}}}),[g]),(0,m.useEffect)((function(){if(d)return Z.current=new IntersectionObserver((function(e){e.forEach((function(e){e.isIntersecting&&e.target.dataset.componentId}))}),{root:A,rootMargin:"".concat(u*o,"px"),threshold:.1}),function(){Z.current&&Z.current.disconnect()}}),[A,u,o,d]),(0,m.useEffect)((function(){var e=new Set(n.map((function(e){return e.id})));new Set(Array.from(W.current.keys()).map((function(e){return e.split("_")[0]}))).forEach((function(t){e.has(t)||Array.from(W.current.keys()).filter((function(e){return e.startsWith(t)})).forEach((function(e){return W.current.delete(e)}))}))}),[n]);var K=(0,m.useCallback)((function(){return d?{ref:w,onScroll:J,style:{height:l,overflow:"auto",position:"relative"}}:{}}),[d,l,J]),_=(0,m.useCallback)((function(){if(!d)return{before:{},after:{}};var e=n.length*o;return{before:{style:{height:k.start*o,width:"100%"}},after:{style:{height:e-k.end*o,width:"100%"}}}}),[d,n.length,o,k]);return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?hl(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):hl(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({visibleComponents:G,visibleRange:k,getContainerProps:K,getSpacerProps:_,renderTime:j,frameRate:M,memoryUsage:B,startRenderMeasurement:$,endRenderMeasurement:q,getCachedComponent:Y},{clearCache:function(){return W.current.clear()},getCacheSize:function(){return W.current.size},getPerformanceMetrics:function(){return{renderTime:j,frameRate:M,memoryUsage:B,cacheSize:W.current.size,visibleComponents:G.length,totalComponents:n.length}},shouldRender:function(e){if(!d)return!0;var t=n.findIndex((function(t){return t.id===e}));return t>=k.start&&t<k.end}})}({components:Pe(),containerHeight:we.height,itemHeight:"mobile"===O?60:"tablet"===O?80:100,enableVirtualization:n.length>20,enablePerformanceMonitoring:!0})),De=je.visibleComponents,Te=je.getContainerProps,Ie=je.getSpacerProps,Me=(je.renderTime,je.frameRate,je.memoryUsage,je.startRenderMeasurement),Re=je.endRenderMeasurement,Le=je.getCachedComponent,Fe=(0,m.useCallback)((function(e){z(e);var t=Fl[e];t.scale!==T&&I(t.scale)}),[T]),Be=(0,m.useCallback)((function(e,t){y&&(ze(e,t,!(arguments.length>2&&void 0!==arguments[2])||arguments[2]),fe(!0),ye(new Date),setTimeout((function(){return fe(!1)}),500))}),[y,ze]);(0,m.useEffect)((function(){Se!==me&&fe(Se),ke&&ke!==ve&&ye(ke)}),[Se,ke,me,ve]),(0,m.useEffect)((function(){return function(){xe.current&&clearTimeout(xe.current)}}),[]);var He=(0,m.useCallback)((function(e){e.preventDefault(),ne(!0)}),[]),Ne=(0,m.useCallback)((function(e){e.preventDefault(),e.dataTransfer.dropEffect="copy",g&&g(e)}),[g]),Ue=(0,m.useCallback)((function(e){e.preventDefault(),e.currentTarget.contains(e.relatedTarget)||(ne(!1),oe(null),b&&b(e))}),[b]),We=(0,m.useCallback)((function(e){if(e.preventDefault(),ne(!1),oe(null),d){var t,n=null===(t=he.current)||void 0===t?void 0:t.getBoundingClientRect();if(n){var r=(e.clientX-n.left)/T,l=(e.clientY-n.top)/T,a=q?Math.round(r/N)*N:r,o=q?Math.round(l/N)*N:l;d(e,{x:a,y:o})}}}),[d,T,q,N]),Ze=(0,m.useCallback)((function(e,t){e.preventDefault(),e.stopPropagation(),oe(t)}),[]),Xe=(0,m.useCallback)((function(e,t){e.preventDefault(),e.stopPropagation(),e.currentTarget.contains(e.relatedTarget)||oe(null)}),[]),Je=(0,m.useCallback)((function(e,t){var n=e.component||e;return void 0!==e.index&&e.index,Le(n.id,(function(){Me();var e=n.id===p,t=se===n.id,a=ae===n.id,i=function(){var e,t,r,l,a,o,i,c,s,p,d,f={fontSize:"mobile"===O?"14px":"tablet"===O?"15px":"16px",padding:"mobile"===O?"8px":"tablet"===O?"12px":"16px"};switch(n.type){case"text":return m.createElement(Ll,{style:f},(null===(e=n.props)||void 0===e?void 0:e.content)||"Sample text");case"button":return m.createElement(J.Ay,{type:(null===(t=n.props)||void 0===t?void 0:t.type)||"default",size:"mobile"===O?"small":"middle",style:{fontSize:f.fontSize}},(null===(r=n.props)||void 0===r?void 0:r.text)||"Button");case"header":return m.createElement(Rl,{level:(null===(l=n.props)||void 0===l?void 0:l.level)||("mobile"===O?4:2),style:f},(null===(a=n.props)||void 0===a?void 0:a.text)||"Header");case"card":return m.createElement(Fn.A,{title:(null===(o=n.props)||void 0===o?void 0:o.title)||"Card Title",size:"mobile"===O?"small":"default",style:{fontSize:f.fontSize}},(null===(i=n.props)||void 0===i?void 0:i.content)||"Card content");case"image":return m.createElement("img",{src:(null===(c=n.props)||void 0===c?void 0:c.src)||"https://via.placeholder.com/150",alt:(null===(s=n.props)||void 0===s?void 0:s.alt)||"Image",style:{maxWidth:"100%",height:"auto",borderRadius:"mobile"===O?"4px":"6px"}});case"divider":return m.createElement(G.A,{style:f},null===(p=n.props)||void 0===p?void 0:p.text);case"input":return m.createElement(K.A,{placeholder:(null===(d=n.props)||void 0===d?void 0:d.placeholder)||"Enter text",disabled:!u,size:"mobile"===O?"small":"middle",style:f});case"form":return m.createElement(cl.A,{layout:"vertical",size:"mobile"===O?"small":"middle"},m.createElement(cl.A.Item,{label:"Sample Field"},m.createElement(K.A,{placeholder:"Sample input",disabled:!u,style:f})));case"table":return m.createElement(sl.A,{columns:[{title:"Name",dataIndex:"name",key:"name"},{title:"Age",dataIndex:"age",key:"age"}],dataSource:[{key:"1",name:"John",age:32},{key:"2",name:"Jane",age:28}],size:"mobile"===O?"small":"middle",scroll:"mobile"===O?{x:!0}:void 0});default:return m.createElement("div",{style:{padding:f.padding,border:"1px dashed #ccc",textAlign:"center",fontSize:f.fontSize,borderRadius:"mobile"===O?"4px":"6px"}},n.type," Component")}};return m.createElement(Xl,{key:n.id,isSelected:e,previewMode:u,isDragOver:a,onClick:function(e){e.stopPropagation(),!u&&r&&r(n)},onMouseEnter:function(){return ue(n.id)},onMouseLeave:function(){return ue(null)},onDragOver:function(e){return Ze(e,n.id)},onDragLeave:function(e){return Xe(e,n.id)},style:{padding:"mobile"===O?"8px":"tablet"===O?"12px":"16px",position:"relative",margin:"mobile"===O?"4px 0":"8px 0"}},m.createElement(i,null),!u&&(e||t)&&m.createElement(Jl,{visible:e||t},m.createElement(P.A,{title:"Edit"},m.createElement(Gl,{icon:m.createElement(x.A,null),size:"small",onClick:function(e){e.stopPropagation(),y&&Be(n.id,{editing:!0})}})),m.createElement(P.A,{title:"Copy"},m.createElement(Gl,{icon:m.createElement(h.A,null),size:"small",onClick:function(e){if(e.stopPropagation(),y){var t=Ml(Ml({},n),{},{id:Date.now().toString()});Be(t.id,t)}}})),"desktop"===O&&m.createElement(m.Fragment,null,m.createElement(P.A,{title:"Move Up"},m.createElement(Gl,{icon:m.createElement(fl.A,null),size:"small",onClick:function(e){e.stopPropagation(),o&&o(n.id,"up"),y&&Be(n.id,{moved:"up"})}})),m.createElement(P.A,{title:"Move Down"},m.createElement(Gl,{icon:m.createElement(gl.A,null),size:"small",onClick:function(e){e.stopPropagation(),o&&o(n.id,"down"),y&&Be(n.id,{moved:"down"})}}))),m.createElement(P.A,{title:"Delete"},m.createElement(Gl,{icon:m.createElement(E.A,null),size:"small",danger:!0,onClick:function(e){e.stopPropagation(),l&&l(n.id),y&&Be(n.id,{deleted:!0})}}))))}))}),[p,se,ae,O,u,y,Le,Me,Re,Be,o,l]);return m.createElement(Bl,null,m.createElement(Hl,null,m.createElement($.A,null,m.createElement(Ll,{strong:!0},"Preview"),m.createElement(G.A,{type:"vertical"}),m.createElement(Nl,null,Object.entries(Fl).map((function(e){var t=(0,s.A)(e,2),n=t[0],r=t[1];return m.createElement(Ul,{key:n,size:"small",active:O===n,onClick:function(){return Fe(n)},icon:r.icon},!u&&r.name)})))),m.createElement($.A,null,y&&m.createElement(m.Fragment,null,m.createElement(mn.A,{status:Oe?"success":"error",text:Oe?"Live":"Offline"}),(me||Se)&&m.createElement(rr.A,{spin:!0}),!1,m.createElement(G.A,{type:"vertical"})),!u&&m.createElement(m.Fragment,null,m.createElement(P.A,{title:"Zoom Out"},m.createElement(J.Ay,{icon:m.createElement(bl.A,null),size:"small",onClick:function(){return I((function(e){return Math.max(e-.1,.5)}))},disabled:T<=.5})),m.createElement(Ll,{style:{minWidth:40,textAlign:"center"}},Math.round(100*T),"%"),m.createElement(P.A,{title:"Zoom In"},m.createElement(J.Ay,{icon:m.createElement(vl.A,null),size:"small",onClick:function(){return I((function(e){return Math.min(e+.1,2)}))},disabled:T>=2})),m.createElement(J.Ay,{size:"small",onClick:function(){return I(Ae.scale)}},"Reset"),m.createElement(G.A,{type:"vertical"}))),!u&&m.createElement($.A,null,m.createElement(P.A,{title:"Toggle Grid"},m.createElement(nt.A,{checked:L,onChange:F,checkedChildren:m.createElement(Ve.A,null),unCheckedChildren:m.createElement(Ve.A,null),size:"small"})),m.createElement(P.A,{title:"Toggle Rulers"},m.createElement(nt.A,{checked:W,onChange:Z,size:"small"})),m.createElement(P.A,{title:"Snap to Grid"},m.createElement(nt.A,{checked:q,onChange:_,size:"small"}))),y&&ve&&m.createElement($.A,null,m.createElement(Ll,{type:"secondary",style:{fontSize:"12px"}},"Updated: ",ve.toLocaleTimeString()))),m.createElement(Zl,{showGrid:L&&!u&&"desktop"===O,gridSize:N,onDragEnter:He,onDragOver:Ne,onDragLeave:Ue,onDrop:We},W&&!u&&"desktop"===O&&m.createElement(m.Fragment,null,m.createElement($l,{orientation:"horizontal"}),m.createElement($l,{orientation:"vertical"})),m.createElement(Vl,{deviceType:O,frame:Ae.frame},m.createElement(Wl,(0,S.A)({},Te(),{ref:he,deviceWidth:we.width,deviceHeight:we.height,deviceType:O,scale:T,onClick:function(){return r&&r(null)}}),!1,m.createElement("div",Ie().before),De.length>0?De.map((function(e,t){return Je(e,t)})):0===n.length?m.createElement(ul.A,{description:m.createElement("span",null,"No components added yet.",m.createElement("br",null),u?"Add components to see them here.":"Drag components from the palette to get started."),style:{margin:"mobile"===O?"50px 20px":"100px 0",fontSize:"mobile"===O?"14px":"16px"}}):null,m.createElement("div",Ie().after))),m.createElement(Yl,{visible:te&&!u,isActive:te})))};var Kl,_l,Ql,ea,ta,na,ra,la,aa,oa,ia,ca,sa,ua,pa=n(1295),da=n(9552),ma=(0,Tr.i7)(Kl||(Kl=(0,u.A)(["\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.7;\n    transform: scale(1.05);\n  }\n"]))),fa=(0,Tr.i7)(_l||(_l=(0,u.A)(["\n  0%, 100% { transform: translateX(0); }\n  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }\n  20%, 40%, 60%, 80% { transform: translateX(3px); }\n"]))),ga=(0,Tr.i7)(Ql||(Ql=(0,u.A)(["\n  0%, 20%, 53%, 80%, 100% {\n    transform: translate3d(0, 0, 0);\n  }\n  40%, 43% {\n    transform: translate3d(0, -8px, 0);\n  }\n  70% {\n    transform: translate3d(0, -4px, 0);\n  }\n  90% {\n    transform: translate3d(0, -2px, 0);\n  }\n"]))),ba=(0,Tr.i7)(ea||(ea=(0,u.A)(["\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n"]))),va=(0,Tr.i7)(ta||(ta=(0,u.A)(["\n  0%, 100% {\n    box-shadow: 0 0 5px rgba(24, 144, 255, 0.5);\n  }\n  50% {\n    box-shadow: 0 0 20px rgba(24, 144, 255, 0.8);\n  }\n"]))),ya=Tr.Ay.div(na||(na=(0,u.A)(["\n  position: absolute;\n  width: 100%;\n  height: 4px;\n  background: ",";\n  border-radius: 2px;\n  z-index: 1000;\n  animation: "," 1.5s ease-in-out infinite;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    left: -6px;\n    top: -2px;\n    width: 8px;\n    height: 8px;\n    background: ",";\n    border-radius: 50%;\n    box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);\n  }\n  \n  &::after {\n    content: '';\n    position: absolute;\n    right: -6px;\n    top: -2px;\n    width: 8px;\n    height: 8px;\n    background: ",";\n    border-radius: 50%;\n    box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);\n  }\n"])),(function(e){return e.isValid?"#52c41a":"#ff4d4f"}),ma,(function(e){return e.isValid?"#52c41a":"#ff4d4f"}),(function(e){return e.isValid?"#52c41a":"#ff4d4f"})),ha=Tr.Ay.div(ra||(ra=(0,u.A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border: 2px dashed ",";\n  border-radius: 8px;\n  background: ",";\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 999;\n  animation: "," 0.3s ease-out;\n  \n  ","\n  \n  ","\n"])),(function(e){return e.isValid?"#52c41a":"#ff4d4f"}),(function(e){return e.isValid?"rgba(82, 196, 26, 0.1)":"rgba(255, 77, 79, 0.1)"}),ba,(function(e){return e.isValid&&(0,Tr.AH)(la||(la=(0,u.A)(["\n    animation: "," 1.5s ease-in-out infinite;\n  "])),ma)}),(function(e){return!e.isValid&&(0,Tr.AH)(aa||(aa=(0,u.A)(["\n    animation: "," 0.5s ease-in-out;\n  "])),fa)})),xa=Tr.Ay.div(oa||(oa=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 12px 16px;\n  background: white;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  font-weight: 600;\n  color: ",";\n  animation: "," 0.3s ease-out 0.1s both;\n"])),(function(e){return e.isValid?"#52c41a":"#ff4d4f"}),ba),Ea=Tr.Ay.div(ia||(ia=(0,u.A)(["\n  position: fixed;\n  pointer-events: none;\n  z-index: 9999;\n  opacity: 0.7;\n  transform: rotate(5deg) scale(0.9);\n  filter: blur(1px);\n  transition: all 0.1s ease-out;\n  border: 2px solid #1890ff;\n  border-radius: 4px;\n  background: rgba(255, 255, 255, 0.9);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\n"]))),Aa=Tr.Ay.div(ca||(ca=(0,u.A)(["\n  padding: 8px 12px;\n  background: white;\n  border: 2px solid #1890ff;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 500;\n  color: #1890ff;\n  animation: "," 2s ease-in-out infinite;\n"])),va),wa=Tr.Ay.div(sa||(sa=(0,u.A)(["\n  position: absolute;\n  top: -2px;\n  left: -2px;\n  right: -2px;\n  bottom: -2px;\n  border: 2px solid #1890ff;\n  border-radius: 6px;\n  background: rgba(24, 144, 255, 0.05);\n  pointer-events: none;\n  z-index: 10;\n  animation: "," 0.2s ease-out;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: -4px;\n    left: -4px;\n    right: -4px;\n    bottom: -4px;\n    border: 1px solid rgba(24, 144, 255, 0.3);\n    border-radius: 8px;\n    animation: "," 2s ease-in-out infinite;\n  }\n"])),ba,ma),Ca=Tr.Ay.div(ua||(ua=(0,u.A)(["\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: #52c41a;\n  color: white;\n  padding: 8px 12px;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-weight: 600;\n  z-index: 1001;\n  animation: "," 0.6s ease-out;\n  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);\n"])),ga),Sa=function(e){var t=e.position,n=e.isValid,r=void 0===n||n,l=e.visible;return void 0!==l&&l&&t?m.createElement(ya,{isValid:r,style:{top:t.y,left:t.x,width:t.width||"100%"}}):null},ka=function(e){var t=e.isValid,n=void 0===t||t,r=e.visible,l=void 0!==r&&r,a=e.message;return l?m.createElement(ha,{isValid:n},m.createElement(xa,{isValid:n},n?m.createElement(pa.A,null):m.createElement(da.A,null),a||(n?"Drop here to add component":"Invalid drop target"))):null},Oa=function(e){var t=e.visible,n=void 0!==t&&t,r=e.position,l=e.children,a=e.componentType,o=(0,m.useRef)(null);return(0,m.useEffect)((function(){o.current&&n&&r&&(o.current.style.left="".concat(r.x,"px"),o.current.style.top="".concat(r.y,"px"))}),[n,r]),n?m.createElement(Ea,{ref:o},l||m.createElement(Aa,null,m.createElement(Xr.A,null),a||"Component")):null},za=function(e){var t=e.visible,n=void 0!==t&&t,r=e.targetRef,l=(0,m.useRef)(null);return(0,m.useEffect)((function(){if(l.current&&null!=r&&r.current&&n){var e=r.current.getBoundingClientRect(),t=l.current;t.style.position="fixed",t.style.top="".concat(e.top,"px"),t.style.left="".concat(e.left,"px"),t.style.width="".concat(e.width,"px"),t.style.height="".concat(e.height,"px")}}),[n,r]),n?m.createElement(wa,{ref:l}):null},Pa=function(e){var t=e.visible,n=void 0!==t&&t,r=e.message,l=void 0===r?"Component added!":r;return n?m.createElement(Ca,null,m.createElement(pa.A,null),l):null};const ja=function(e){var t=e.isDragging,n=void 0!==t&&t,r=e.isOver,l=void 0!==r&&r,a=e.isValid,o=void 0===a||a,i=e.dropPosition,c=e.ghostPosition,s=e.hoveredElement,u=e.draggedComponent,p=e.showSuccess,d=void 0!==p&&p,f=e.successMessage,g=e.dropMessage,b=e.children;return m.createElement(m.Fragment,null,m.createElement(Sa,{position:i,isValid:o,visible:n&&l&&i}),m.createElement(ka,{isValid:o,visible:n&&l,message:g}),m.createElement(Oa,{visible:n,position:c,componentType:null==u?void 0:u.type}),m.createElement(za,{visible:!n&&s,targetRef:s}),m.createElement(Pa,{visible:d,message:f}),b)};var Da,Ta,Ia,Ma,Ra=n(7206),La=n(5763),Fa=n(1190),Ba=n(4463),Ha=n(2630),Na=Tr.Ay.div(Da||(Da=(0,u.A)(["\n  position: fixed;\n  z-index: 10000;\n  background: white;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  border: 1px solid #e8e8e8;\n  min-width: 180px;\n  overflow: hidden;\n  \n  .ant-menu {\n    border: none;\n    box-shadow: none;\n  }\n  \n  .ant-menu-item {\n    margin: 0;\n    padding: 8px 16px;\n    height: auto;\n    line-height: 1.4;\n    \n    &:hover {\n      background: #f0f2f5;\n    }\n    \n    &.ant-menu-item-disabled {\n      color: #bfbfbf;\n      cursor: not-allowed;\n      \n      &:hover {\n        background: transparent;\n      }\n    }\n  }\n  \n  .ant-menu-item-icon {\n    margin-right: 8px;\n    font-size: 14px;\n  }\n"]))),Ua=Tr.Ay.div(Ta||(Ta=(0,u.A)(["\n  padding: 4px 0;\n  \n  &:not(:last-child) {\n    border-bottom: 1px solid #f0f0f0;\n  }\n"]))),Va=Tr.Ay.div(Ia||(Ia=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n"]))),Wa=Tr.Ay.span(Ma||(Ma=(0,u.A)(["\n  font-size: 11px;\n  color: #999;\n  margin-left: 16px;\n"])));const Za=function(e){var t=e.visible,n=e.x,r=e.y,l=e.onClose,a=e.selectedComponent,o=e.selectedComponents,i=void 0===o?[]:o,c=e.onCopy,s=e.onPaste,u=e.onDelete,p=e.onEdit,d=e.onDuplicate,f=e.onMoveUp,g=e.onMoveDown,b=e.onToggleVisibility,v=e.onToggleLock,y=e.onGroup,A=e.onUngroup,w=e.onCopyStyle,C=e.onPasteStyle,S=e.onProperties,k=e.clipboardHasData,O=void 0!==k&&k,z=e.canMoveUp,P=void 0===z||z,j=e.canMoveDown,D=void 0===j||j,T=e.canGroup,I=void 0!==T&&T,M=e.canUngroup,R=void 0!==M&&M,L=(0,m.useRef)(null);if((0,m.useEffect)((function(){if(t&&L.current){var e=L.current,l=e.getBoundingClientRect(),a=window.innerWidth,o=window.innerHeight,i=n,c=r;n+l.width>a&&(i=a-l.width-10),r+l.height>o&&(c=o-l.height-10),e.style.left="".concat(Math.max(10,i),"px"),e.style.top="".concat(Math.max(10,c),"px")}}),[t,n,r]),(0,m.useEffect)((function(){var e=function(e){"Escape"===e.key&&t&&l()};return document.addEventListener("keydown",e),function(){return document.removeEventListener("keydown",e)}}),[t,l]),!t)return null;var F=i.length>1,B=a||i.length>0,H=[{section:"edit",items:[{key:"edit",icon:m.createElement(x.A,null),label:"Edit Properties",shortcut:"Enter",disabled:!B||F,onClick:function(){null==p||p(a),l()}},{key:"copy",icon:m.createElement(h.A,null),label:F?"Copy ".concat(i.length," Components"):"Copy",shortcut:"Ctrl+C",disabled:!B,onClick:function(){null==c||c(F?i:a),l()}},{key:"paste",icon:m.createElement(h.A,{style:{transform:"scaleX(-1)"}}),label:"Paste",shortcut:"Ctrl+V",disabled:!O,onClick:function(){null==s||s(),l()}},{key:"duplicate",icon:m.createElement(h.A,null),label:F?"Duplicate Selection":"Duplicate",shortcut:"Ctrl+D",disabled:!B,onClick:function(){null==d||d(F?i:a),l()}}]},{section:"arrange",items:[{key:"move-up",icon:m.createElement(fl.A,null),label:"Move Up",shortcut:"Ctrl+↑",disabled:!B||!P,onClick:function(){F?i.forEach((function(e){return null==f?void 0:f(e)})):null==f||f(a),l()}},{key:"move-down",icon:m.createElement(gl.A,null),label:"Move Down",shortcut:"Ctrl+↓",disabled:!B||!D,onClick:function(){F?i.forEach((function(e){return null==g?void 0:g(e)})):null==g||g(a),l()}}]},{section:"visibility",items:[{key:"toggle-visibility",icon:!1!==(null==a?void 0:a.visible)?m.createElement(Nn.A,null):m.createElement(Hn.A,null),label:!1!==(null==a?void 0:a.visible)?"Hide":"Show",shortcut:"Ctrl+H",disabled:!B,onClick:function(){F?i.forEach((function(e){return null==b?void 0:b(e)})):null==b||b(a),l()}},{key:"toggle-lock",icon:null!=a&&a.locked?m.createElement(Ee.A,null):m.createElement(La.A,null),label:null!=a&&a.locked?"Unlock":"Lock",shortcut:"Ctrl+L",disabled:!B,onClick:function(){F?i.forEach((function(e){return null==v?void 0:v(e)})):null==v||v(a),l()}}]},{section:"group",items:[{key:"group",icon:m.createElement(Fa.A,null),label:"Group",shortcut:"Ctrl+G",disabled:!I||i.length<2,onClick:function(){null==y||y(i),l()}},{key:"ungroup",icon:m.createElement(Ba.A,null),label:"Ungroup",shortcut:"Ctrl+Shift+G",disabled:!R,onClick:function(){null==A||A(a),l()}}]},{section:"style",items:[{key:"copy-style",icon:m.createElement(Ha.A,null),label:"Copy Style",disabled:!B||F,onClick:function(){null==w||w(a),l()}},{key:"paste-style",icon:m.createElement(Ha.A,{style:{transform:"scaleX(-1)"}}),label:"Paste Style",disabled:!B,onClick:function(){F?i.forEach((function(e){return null==C?void 0:C(e)})):null==C||C(a),l()}}]},{section:"actions",items:[{key:"properties",icon:m.createElement(fn.A,null),label:"Properties",shortcut:"F4",disabled:!B||F,onClick:function(){null==S||S(a),l()}},{key:"delete",icon:m.createElement(E.A,null),label:F?"Delete ".concat(i.length," Components"):"Delete",shortcut:"Delete",disabled:!B,danger:!0,onClick:function(){F?i.forEach((function(e){return null==u?void 0:u(e)})):null==u||u(a),l()}}]}];return m.createElement(Na,{ref:L,style:{left:n,top:r},onClick:function(e){return e.stopPropagation()}},m.createElement(Ra.A,{mode:"vertical",selectable:!1},H.map((function(e,t){return m.createElement(Ua,{key:e.section},e.items.map((function(e){return m.createElement(Ra.A.Item,{key:e.key,icon:e.icon,disabled:e.disabled,danger:e.danger,onClick:e.onClick},m.createElement(Va,null,m.createElement("span",null,e.label),e.shortcut&&m.createElement(Wa,null,e.shortcut)))})))}))))};function Xa(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ja(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Xa(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Xa(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Ga,Ya,$a,qa,Ka,_a;function Qa(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function eo(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Qa(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Qa(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var to=kr.A.Sider,no=kr.A.Content,ro=(0,Tr.Ay)(kr.A)(Ga||(Ga=(0,u.A)(["\n  height: 100vh;\n  background: #f5f5f5;\n"]))),lo=Tr.Ay.div(Ya||(Ya=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 16px;\n  background: white;\n  border-bottom: 1px solid #e8e8e8;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  z-index: 100;\n"]))),ao=Tr.Ay.div($a||($a=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n"]))),oo=(0,Tr.Ay)(to)(qa||(qa=(0,u.A)(["\n  background: white;\n  border-right: 1px solid #e8e8e8;\n  overflow: auto;\n  \n  .ant-layout-sider-children {\n    padding: 16px;\n  }\n"]))),io=(0,Tr.Ay)(no)(Ka||(Ka=(0,u.A)(["\n  display: flex;\n  flex-direction: column;\n  background: #f5f5f5;\n  position: relative;\n"]))),co=Tr.Ay.div(_a||(_a=(0,u.A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  backdrop-filter: blur(2px);\n"])));const so=function(){var e,t,n,r,l=(0,f.wA)(),a=(0,f.d4)((function(e){return e.components||[]})),o=(0,m.useState)(!1),i=(0,s.A)(o,2),c=i[0],u=i[1],p=(0,m.useState)(!1),d=(0,s.A)(p,2),g=d[0],v=d[1],y=(0,m.useState)(null),h=(0,s.A)(y,2),x=h[0],E=h[1],w=(0,m.useState)(!1),C=(0,s.A)(w,2),S=C[0],k=C[1],O=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50,n=(0,m.useState)([e]),r=(0,s.A)(n,2),l=r[0],a=r[1],o=(0,m.useState)(0),i=(0,s.A)(o,2),c=i[0],u=i[1],p=(0,m.useRef)(!1),d=l[c],f=(0,m.useCallback)((function(e){p.current?p.current=!1:(a((function(n){var r=n.slice(0,c+1);return r.push(e),r.length>t?(r.shift(),r):r})),u((function(e){return Math.min(e+1,t-1)})))}),[c,t]),g=(0,m.useCallback)((function(){return c>0?(p.current=!0,u((function(e){return e-1})),l[c-1]):d}),[c,l,d]),b=(0,m.useCallback)((function(){return c<l.length-1?(p.current=!0,u((function(e){return e+1})),l[c+1]):d}),[c,l,d]),v=c>0,y=c<l.length-1,h=(0,m.useCallback)((function(){a([d]),u(0)}),[d]),x=(0,m.useCallback)((function(){return{totalStates:l.length,currentIndex:c,canUndo:v,canRedo:y}}),[l.length,c,v,y]);return{state:d,pushState:f,undo:g,redo:b,canUndo:v,canRedo:y,clearHistory:h,getHistoryInfo:x}}(a),z=O.state,j=O.pushState,D=O.undo,T=O.redo,I=O.canUndo,M=O.canRedo,R=function(){var e=(0,m.useState)({visible:!1,x:0,y:0,items:[]}),t=(0,s.A)(e,2),n=t[0],r=t[1],l=(0,m.useCallback)((function(e,t){e.preventDefault(),r({visible:!0,x:e.clientX,y:e.clientY,items:t||[]})}),[]),a=(0,m.useCallback)((function(){r((function(e){return Ja(Ja({},e),{},{visible:!1})}))}),[]);return(0,m.useEffect)((function(){var e=function(){n.visible&&a()};return document.addEventListener("click",e),function(){document.removeEventListener("click",e)}}),[n.visible,a]),{contextMenu:n,showContextMenu:l,hideContextMenu:a}}(),L=R.contextMenu,F=R.showContextMenu,B=R.hideContextMenu,H=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:200,t=(0,m.useState)(!1),n=(0,s.A)(t,2),r=n[0],l=n[1],a=(0,m.useState)(""),o=(0,s.A)(a,2),i=o[0],c=o[1],u=(0,m.useRef)(null),p=(0,m.useCallback)((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Loading...";u.current&&clearTimeout(u.current),u.current=setTimeout((function(){l(!0),c(t)}),e)}),[e]),d=(0,m.useCallback)((function(){u.current&&(clearTimeout(u.current),u.current=null),l(!1),c("")}),[]);return(0,m.useEffect)((function(){return function(){u.current&&clearTimeout(u.current)}}),[]),{isLoading:r,loadingMessage:i,startLoading:p,stopLoading:d}}(),N=H.isLoading,U=H.loadingMessage,V=H.startLoading,W=H.stopLoading,Z=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=(0,m.useState)(new Set),n=(0,s.A)(t,2),r=n[0],l=n[1],a=(0,m.useState)(-1),o=(0,s.A)(a,2),i=o[0],c=o[1],u=(0,m.useCallback)((function(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=e.findIndex((function(e){return e.id===t.id}));l((function(e){var r=new Set(n?e:[]);return r.has(t.id)?r.delete(t.id):r.add(t.id),r})),c(r)}),[e]),p=(0,m.useCallback)((function(t){var n=e.findIndex((function(e){return e.id===t.id}));if(-1!==i){var r=Math.min(i,n),a=Math.max(i,n);l((function(t){for(var n=new Set(t),l=r;l<=a;l++)e[l]&&n.add(e[l].id);return n}))}else u(t)}),[e,i,u]),d=(0,m.useCallback)((function(){l(new Set(e.map((function(e){return e.id}))))}),[e]),f=(0,m.useCallback)((function(){l(new Set),c(-1)}),[]),g=(0,m.useCallback)((function(e){return r.has(e)}),[r]),b=(0,m.useCallback)((function(){return e.filter((function(e){return r.has(e.id)}))}),[e,r]);return{selectedItems:Array.from(r),selectItem:u,selectRange:p,selectAll:d,clearSelection:f,isSelected:g,getSelectedItems:b,selectedCount:r.size}}(a),X=Z.selectedItems,G=Z.selectItem,Y=Z.clearSelection,q=(Z.isSelected,e=(0,m.useState)(null),t=(0,s.A)(e,2),n=t[0],r=t[1],{copy:(0,m.useCallback)((function(e){r(e),navigator.clipboard&&"string"==typeof e&&navigator.clipboard.writeText(e).catch(console.error)}),[]),paste:(0,m.useCallback)((function(){return n}),[n]),clear:(0,m.useCallback)((function(){r(null)}),[]),hasData:null!==n,data:n}),K=q.copy,_=q.paste,Q=q.hasData,ee=(0,m.useRef)(null),te=(0,m.useCallback)((function(e,t,n){if(t){V("Adding component...");try{var r={id:"".concat(t.type,"-").concat(Date.now()),type:t.type,props:eo({x:n.x,y:n.y},ue(t.type)),createdAt:(new Date).toISOString()};l((0,A.X8)(r)),k(!0),setTimeout((function(){return k(!1)}),2e3),Or.Ay.success("".concat(t.label||t.type," component added"))}catch(e){Or.Ay.error("Failed to add component"),console.error("Error adding component:",e)}finally{W()}}}),[l,V,W]),ne=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.onDrop,n=e.onDragStart,r=e.onDragEnd,l=e.onDragOver,a=e.onDragLeave,o=e.snapToGrid,i=void 0!==o&&o,c=e.gridSize,u=void 0===c?20:c,p=(e.showDropZones,e.acceptedTypes),d=void 0===p?["application/json"]:p,f=(0,m.useState)(!1),g=(0,s.A)(f,2),b=g[0],v=g[1],y=(0,m.useState)(!1),h=(0,s.A)(y,2),x=h[0],E=h[1],A=(0,m.useState)(null),w=(0,s.A)(A,2),C=w[0],S=w[1],k=(0,m.useState)({x:0,y:0}),O=(0,s.A)(k,2),z=O[0],P=O[1],j=(0,m.useState)(!0),D=(0,s.A)(j,2),T=D[0],I=D[1],M=(0,m.useState)(null),R=(0,s.A)(M,2),L=(R[0],R[1],(0,m.useRef)(null)),F=(0,m.useRef)(null),B=(0,m.useCallback)((function(e,t){if(v(!0),S(t),t&&e.dataTransfer.setData("application/json",JSON.stringify(t)),e.dataTransfer.effectAllowed="copy",F.current){var r=F.current.cloneNode(!0);r.style.position="absolute",r.style.top="-1000px",r.style.left="-1000px",r.style.opacity="0.8",r.style.transform="rotate(5deg) scale(0.9)",r.style.pointerEvents="none",r.style.zIndex="9999",document.body.appendChild(r),e.dataTransfer.setDragImage(r,50,25),setTimeout((function(){document.body.contains(r)&&document.body.removeChild(r)}),0)}n&&n(e,t)}),[n]),H=(0,m.useCallback)((function(e){v(!1),S(null),P({x:0,y:0}),I(!0),r&&r(e)}),[r]),N=(0,m.useCallback)((function(e){e.preventDefault(),E(!0)}),[]),U=(0,m.useCallback)((function(e){if(e.preventDefault(),L.current){var t=L.current.getBoundingClientRect(),n=e.clientX-t.left,r=e.clientY-t.top;i&&(n=Math.round(n/u)*u,r=Math.round(r/u)*u),P({x:n,y:r})}var a=e.dataTransfer.types[0],o=d.includes(a)||0===d.length;I(o),e.dataTransfer.dropEffect=o?"copy":"none",l&&l(e)}),[i,u,d,l]),V=(0,m.useCallback)((function(e){e.preventDefault(),e.currentTarget.contains(e.relatedTarget)||(E(!1),I(!0),a&&a(e))}),[a]),W=(0,m.useCallback)((function(e){e.preventDefault(),E(!1),I(!0);try{var n=e.dataTransfer.getData("application/json"),r=null;n&&(r=JSON.parse(n));var l=z;i&&(l={x:Math.round(z.x/u)*u,y:Math.round(z.y/u)*u}),t&&t(e,r,l)}catch(e){console.error("Error handling drop:",e)}}),[z,i,u,t]);return(0,m.useEffect)((function(){var e=L.current;if(e)return e.addEventListener("dragenter",N),e.addEventListener("dragover",U),e.addEventListener("dragleave",V),e.addEventListener("drop",W),function(){e.removeEventListener("dragenter",N),e.removeEventListener("dragover",U),e.removeEventListener("dragleave",V),e.removeEventListener("drop",W)}}),[N,U,V,W]),{isDragging:b,isOver:x,dragData:C,dropPosition:z,validDropZone:T,dropZoneRef:L,dragPreviewRef:F,handleDragStart:B,handleDragEnd:H,reset:function(){v(!1),E(!1),S(null),P({x:0,y:0}),I(!0)}}}({onDrop:te,snapToGrid:!0,gridSize:20}),re=ne.isDragging,le=ne.isOver,ae=ne.validDropZone,oe=ne.dropPosition,ie=ne.dropZoneRef,ce=ne.handleDragStart,se=ne.handleDragEnd;(0,m.useEffect)((function(){JSON.stringify(a)!==JSON.stringify(z)&&j(a)}),[a,z,j]);var ue=function(e){return{text:{content:"Sample text",fontSize:14},button:{text:"Button",type:"default"},header:{text:"Header",level:2},card:{title:"Card Title",content:"Card content"},image:{src:"https://via.placeholder.com/150",alt:"Image"},input:{placeholder:"Enter text"},form:{layout:"vertical"}}[e]||{}},pe=(0,m.useCallback)((function(e){e?G(e):Y()}),[G,Y]),de=(0,m.useCallback)((function(e){V("Deleting component...");try{l((0,A.o$)(e)),Y(),Or.Ay.success("Component deleted")}catch(e){Or.Ay.error("Failed to delete component")}finally{W()}}),[l,Y,V,W]),me=(0,m.useCallback)((function(e,t){V("Updating component...");try{l((0,A.ZP)(e,t)),Or.Ay.success("Component updated")}catch(e){Or.Ay.error("Failed to update component")}finally{W()}}),[l,V,W]),fe=(0,m.useCallback)((function(){D()&&Or.Ay.success("Undone")}),[D]),ge=(0,m.useCallback)((function(){T()&&Or.Ay.success("Redone")}),[T]),be=(0,m.useCallback)((function(e){K(e),Or.Ay.success("Component copied")}),[K]),ve=(0,m.useCallback)((function(){var e=_();if(e){var t=eo(eo({},e),{},{id:"".concat(e.type,"-").concat(Date.now()),props:eo(eo({},e.props),{},{x:(e.props.x||0)+20,y:(e.props.y||0)+20})});l((0,A.X8)(t)),Or.Ay.success("Component pasted")}}),[_,l]);return(0,m.useCallback)((function(e,t){e.preventDefault(),F(e,[{key:"edit",label:"Edit",icon:"edit"},{key:"copy",label:"Copy",icon:"copy"},{key:"delete",label:"Delete",icon:"delete",danger:!0}])}),[F]),function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];(0,m.useEffect)((function(){var t=function(t){var n=t.ctrlKey,r=t.metaKey,l=t.shiftKey,a=t.altKey,o=t.key,i=[];(n||r)&&i.push("ctrl"),l&&i.push("shift"),a&&i.push("alt");var c=[].concat(i,[o.toLowerCase()]).join("+");e[c]&&(t.preventDefault(),e[c](t))};return document.addEventListener("keydown",t),function(){document.removeEventListener("keydown",t)}}),t)}({"ctrl+z":fe,"ctrl+y":ge,"ctrl+c":function(){X.length>0&&be(a.find((function(e){return e.id===X[0]})))},"ctrl+v":ve,delete:function(){X.length>0&&X.forEach((function(e){return de(e)}))},escape:function(){Y(),B()},f11:function(e){e.preventDefault(),v(!g)}},[X,a,fe,ge,be,ve,de,Y,B,g]),m.createElement(ro,{ref:ee,className:g?"fullscreen":""},m.createElement(lo,null,m.createElement(ao,null,m.createElement(P.A,{title:"Undo (Ctrl+Z)"},m.createElement(J.Ay,{icon:m.createElement(xn.A,null),disabled:!I,onClick:fe})),m.createElement(P.A,{title:"Redo (Ctrl+Y)"},m.createElement(J.Ay,{icon:m.createElement(Pr.A,null),disabled:!M,onClick:ge})),m.createElement(J.Ay,{icon:m.createElement(b.A,null)},"Save")),m.createElement(ao,null,m.createElement("span",{style:{fontSize:16,fontWeight:600}},"Enhanced Component Builder")),m.createElement(ao,null,m.createElement(P.A,{title:"Toggle Preview Mode"},m.createElement(J.Ay,{icon:m.createElement(Hn.A,null),type:c?"primary":"default",onClick:function(){return u(!c)}},"Preview")),m.createElement(P.A,{title:"Settings"},m.createElement(J.Ay,{icon:m.createElement(fn.A,null)})),m.createElement(P.A,{title:"Fullscreen (F11)"},m.createElement(J.Ay,{icon:g?m.createElement(jr.A,null):m.createElement(Dr.A,null),onClick:function(){return v(!g)}})))),m.createElement(kr.A,null,!c&&m.createElement(oo,{width:300,theme:"light"},m.createElement(il,{onAddComponent:function(e){var t={id:"".concat(e,"-").concat(Date.now()),type:e,props:ue(e),createdAt:(new Date).toISOString()};l((0,A.X8)(t)),Or.Ay.success("".concat(e," component added"))},onDragStart:function(e){E(e),ce(null,e)},onDragEnd:function(){E(null),se()}})),m.createElement(io,{ref:ie},m.createElement(ql,{components:a,onSelectComponent:pe,onDeleteComponent:de,onUpdateComponent:me,previewMode:c,selectedComponentId:X[0],onDrop:te}),N&&m.createElement(co,null,m.createElement($.A,{direction:"vertical",align:"center"},m.createElement(zr.A,{size:"large"}),m.createElement("span",null,U))))),m.createElement(ja,{isDragging:re,isOver:le,isValid:ae,dropPosition:oe,draggedComponent:x,showSuccess:S,successMessage:"Component added successfully!"}),m.createElement(Za,{visible:L.visible,x:L.x,y:L.y,onClose:B,selectedComponent:a.find((function(e){return e.id===X[0]})),selectedComponents:a.filter((function(e){return X.includes(e.id)})),onCopy:be,onPaste:ve,onDelete:de,clipboardHasData:Q}))};var uo,po,mo,fo,go,bo,vo;function yo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ho(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?yo(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):yo(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var xo=w.I4.div(uo||(uo=(0,u.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),C.Ay.spacing[4]),Eo=w.I4.div(po||(po=(0,u.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: ",";\n"])),C.Ay.spacing[4]),Ao=w.I4.div(mo||(mo=(0,u.A)(["\n  padding: ",";\n  border: 1px solid ",";\n  border-radius: ",";\n  background-color: white;\n"])),C.Ay.spacing[4],C.Ay.colors.neutral[200],C.Ay.borderRadius.md),wo=w.I4.div(fo||(fo=(0,u.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),C.Ay.spacing[3]),Co=w.I4.div(go||(go=(0,u.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),C.Ay.spacing[2]),So=w.I4.div(bo||(bo=(0,u.A)(["\n  cursor: pointer;\n  transition: ",";\n  border: 2px solid ",";\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ",";\n  }\n"])),C.Ay.transitions.default,(function(e){return e.isSelected?C.Ay.colors.primary.main:"transparent"}),C.Ay.shadows.md),ko=w.I4.div(vo||(vo=(0,u.A)(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ",";\n  background-color: ",";\n  border-radius: ",";\n  text-align: center;\n"])),C.Ay.spacing[8],C.Ay.colors.neutral[100],C.Ay.borderRadius.md),Oo=[{value:"container",label:"Container"},{value:"text",label:"Text"},{value:"button",label:"Button"},{value:"input",label:"Input Field"},{value:"image",label:"Image"},{value:"card",label:"Card"},{value:"list",label:"List"},{value:"custom",label:"Custom"}];const zo=function(){var e=(0,m.useState)(!0),t=(0,s.A)(e,2),n=t[0];if(t[1],n)return m.createElement(so,null);var r=(0,f.wA)(),l=(0,f.d4)((function(e){var t,n;return(null===(t=e.app)||void 0===t?void 0:t.components)||(null===(n=e.appData)||void 0===n?void 0:n.components)||[]})),a=(0,m.useState)(!0),o=(0,s.A)(a,2),i=o[0],u=o[1],p=(0,m.useState)(""),S=(0,s.A)(p,2),k=S[0],O=S[1],z=(0,m.useState)("container"),P=(0,s.A)(z,2),j=P[0],D=P[1],T=(0,m.useState)("{}"),I=(0,s.A)(T,2),M=I[0],R=I[1],L=(0,m.useState)(null),F=(0,s.A)(L,2),B=F[0],H=F[1],N=(0,m.useState)(!1),U=(0,s.A)(N,2),V=U[0],W=U[1],Z=(0,m.useState)({}),X=(0,s.A)(Z,2),J=X[0],G=X[1];if((0,m.useEffect)((function(){var e=function(){var e=(0,c.A)(d().mark((function e(){return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:try{u(!0),0===l.length&&[{id:"button-1",name:"Primary Button",type:"button",props:{text:"Click Me",variant:"primary",size:"medium",onClick:"handleButtonClick"},createdAt:(new Date).toISOString()},{id:"text-1",name:"Header Text",type:"text",props:{content:"Welcome to App Builder",variant:"h1",color:"#2563EB",align:"center"},createdAt:(new Date).toISOString()},{id:"input-1",name:"Email Input",type:"input",props:{label:"Email Address",placeholder:"Enter your email",type:"email",required:!0,validation:"email"},createdAt:(new Date).toISOString()},{id:"card-1",name:"Feature Card",type:"card",props:{title:"Easy to Use",description:"Build applications with a simple drag-and-drop interface",image:"https://via.placeholder.com/150",elevation:"md"},createdAt:(new Date).toISOString()}].forEach((function(e){r((0,A.X8)(e))})),u(!1)}catch(e){console.error("Failed to initialize ComponentBuilder:",e),u(!1)}case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();e()}),[l.length,r]),(0,m.useEffect)((function(){return console.log("ComponentBuilder mounting..."),function(){console.log("ComponentBuilder unmounting...")}}),[]),(0,m.useEffect)((function(){console.log("Components updated:",l)}),[l]),(0,m.useEffect)((function(){Object.keys(J).length>0&&console.error("ComponentBuilder errors:",J)}),[J]),i)return m.createElement("div",null,"Loading ComponentBuilder...");var Y=function(){var e={};k.trim()||(e.name="Component name is required");try{M&&JSON.parse(M)}catch(t){e.props="Invalid JSON format"}return G(e),0===Object.keys(e).length},$=function(e){H(e),O(e.name),D(e.type),R(JSON.stringify(e.props,null,2)),W(!0),G({})};return m.createElement(xo,null,m.createElement(w.Zp,null,m.createElement(w.Zp.Header,null,m.createElement(w.Zp.Title,null,V?"Edit Component":"Create Component"),V&&m.createElement(w.$n,{variant:"text",size:"small",onClick:function(){O(""),D("container"),R("{}"),H(null),W(!1),G({})},startIcon:m.createElement(g.A,null)},"Cancel")),m.createElement(w.Zp.Content,null,m.createElement(wo,null,m.createElement(Co,null,m.createElement(w.pd,{label:"Component Name",value:k,onChange:function(e){return O(e.target.value)},placeholder:"Enter component name",fullWidth:!0,error:!!J.name,helperText:J.name})),m.createElement(Co,null,m.createElement(w.l6,{label:"Component Type",value:j,onChange:function(e){return D(e.target.value)},options:Oo,fullWidth:!0})),m.createElement(Co,null,m.createElement(w.pd,{label:"Component Props (JSON)",value:M,onChange:function(e){return R(e.target.value)},placeholder:'{"text": "Hello", "color": "blue"}',fullWidth:!0,error:!!J.props,helperText:J.props,as:"textarea",rows:5,style:{fontFamily:C.Ay.typography.fontFamily.code}})))),m.createElement(w.Zp.Footer,null,V?m.createElement(w.$n,{variant:"primary",onClick:function(){if(B&&Y())try{var e=M?JSON.parse(M):{},t=ho(ho({},B),{},{name:k.trim(),type:j,props:e,updatedAt:(new Date).toISOString()});r((0,A.ZP)(t)),O(""),D("container"),R("{}"),H(null),W(!1),G({})}catch(e){G(ho(ho({},J),{},{props:e.message}))}},startIcon:m.createElement(b.A,null)},"Update Component"):m.createElement(w.$n,{variant:"primary",onClick:function(){if(Y())try{var e=M?JSON.parse(M):{},t={id:Date.now().toString(),name:k.trim(),type:j,props:e,createdAt:(new Date).toISOString()};r((0,A.X8)(t)).then((function(){O(""),D("container"),R("{}"),G({})})).catch((function(e){console.error("Failed to add component:",e),G({submit:"Failed to add component"})}))}catch(e){G(ho(ho({},J),{},{props:e.message}))}},startIcon:m.createElement(v.A,null)},"Add Component"))),m.createElement(w.Zp,null,m.createElement(w.Zp.Header,null,m.createElement(w.Zp.Title,null,"Component Library")),m.createElement(w.Zp.Content,null,0===l.length?m.createElement(ko,null,m.createElement("div",{style:{fontSize:"48px",color:C.Ay.colors.neutral[400],marginBottom:C.Ay.spacing[4]}},m.createElement(y.A,null)),m.createElement("h3",null,"No Components Yet"),m.createElement("p",null,"Create your first component to get started")):m.createElement(Eo,null,l.map((function(e){return m.createElement(So,{key:e.id,isSelected:B&&B.id===e.id},m.createElement(w.Zp,{elevation:"sm"},m.createElement(w.Zp.Header,null,m.createElement("div",null,m.createElement("div",{style:{fontWeight:C.Ay.typography.fontWeight.semibold}},e.name),m.createElement("div",{style:{fontSize:C.Ay.typography.fontSize.sm,color:C.Ay.colors.neutral[500]}},e.type)),m.createElement("div",{style:{display:"flex",gap:C.Ay.spacing[1]}},m.createElement(w.$n,{variant:"text",size:"small",onClick:function(t){t.stopPropagation(),function(e){var t=ho(ho({},e),{},{id:Date.now().toString(),name:"".concat(e.name," (Copy)"),createdAt:(new Date).toISOString()});r((0,A.X8)(t))}(e)}},m.createElement(h.A,null)),m.createElement(w.$n,{variant:"text",size:"small",onClick:function(t){t.stopPropagation(),$(e)}},m.createElement(x.A,null)),m.createElement(w.$n,{variant:"text",size:"small",onClick:function(t){var n;t.stopPropagation(),n=e.id,r((0,A.o$)(n)),B&&B.id===n&&(O(""),D("container"),R("{}"),H(null),W(!1))}},m.createElement(E.A,null)))),m.createElement(w.Zp.Content,{onClick:function(){return $(e)}},m.createElement(Ao,null,m.createElement("pre",{style:{margin:0,overflow:"auto",maxHeight:"100px"}},JSON.stringify(e.props,null,2))))))}))))),B&&m.createElement(w.Zp,null,m.createElement(w.Zp.Header,null,m.createElement(w.Zp.Title,null,"Component Properties")),m.createElement(w.Zp.Content,null,m.createElement(gr,{component:B,onUpdate:function(e){r((0,A.ZP)(e))}}))))}}}]);