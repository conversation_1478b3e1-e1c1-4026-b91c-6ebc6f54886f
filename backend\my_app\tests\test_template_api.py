"""
Tests for template API endpoints.
"""
from django.test import TestCase
from django.contrib.auth.models import User
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from my_app.models import LayoutTemplate, AppTemplate
import json


class LayoutTemplateAPITest(TestCase):
    """Test cases for LayoutTemplate API endpoints"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.layout_data = {
            'name': 'Test Layout',
            'description': 'A test layout template',
            'layout_type': 'grid',
            'components': json.dumps({
                'structure': 'grid',
                'columns': 12
            }),
            'default_props': json.dumps({
                'responsive': True
            }),
            'is_public': False
        }
    
    def test_create_layout_template_authenticated(self):
        """Test creating a layout template when authenticated"""
        self.client.force_authenticate(user=self.user)
        
        response = self.client.post('/api/layout-templates/', self.layout_data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'Test Layout')
        self.assertEqual(response.data['user'], self.user.id)
        
        # Verify template was created in database
        template = LayoutTemplate.objects.get(id=response.data['id'])
        self.assertEqual(template.name, 'Test Layout')
        self.assertEqual(template.user, self.user)
    
    def test_create_layout_template_unauthenticated(self):
        """Test creating a layout template when not authenticated"""
        response = self.client.post('/api/layout-templates/', self.layout_data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_list_layout_templates(self):
        """Test listing layout templates"""
        # Create templates for different users
        LayoutTemplate.objects.create(
            name='User Template',
            layout_type='grid',
            user=self.user,
            is_public=False
        )
        LayoutTemplate.objects.create(
            name='Public Template',
            layout_type='flex',
            user=self.other_user,
            is_public=True
        )
        LayoutTemplate.objects.create(
            name='Other Private Template',
            layout_type='sidebar',
            user=self.other_user,
            is_public=False
        )
        
        self.client.force_authenticate(user=self.user)
        response = self.client.get('/api/layout-templates/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Should see own templates + public templates
        self.assertEqual(len(response.data['results']), 2)
        
        template_names = [t['name'] for t in response.data['results']]
        self.assertIn('User Template', template_names)
        self.assertIn('Public Template', template_names)
        self.assertNotIn('Other Private Template', template_names)
    
    def test_retrieve_layout_template(self):
        """Test retrieving a specific layout template"""
        template = LayoutTemplate.objects.create(
            name='Test Template',
            layout_type='grid',
            user=self.user,
            is_public=True
        )
        
        self.client.force_authenticate(user=self.user)
        response = self.client.get(f'/api/layout-templates/{template.id}/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Test Template')
    
    def test_update_layout_template_owner(self):
        """Test updating a layout template as owner"""
        template = LayoutTemplate.objects.create(
            name='Original Name',
            layout_type='grid',
            user=self.user
        )
        
        self.client.force_authenticate(user=self.user)
        update_data = {'name': 'Updated Name'}
        response = self.client.patch(f'/api/layout-templates/{template.id}/', update_data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Updated Name')
        
        # Verify in database
        template.refresh_from_db()
        self.assertEqual(template.name, 'Updated Name')
    
    def test_update_layout_template_non_owner(self):
        """Test updating a layout template as non-owner"""
        template = LayoutTemplate.objects.create(
            name='Original Name',
            layout_type='grid',
            user=self.other_user
        )
        
        self.client.force_authenticate(user=self.user)
        update_data = {'name': 'Updated Name'}
        response = self.client.patch(f'/api/layout-templates/{template.id}/', update_data)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_delete_layout_template_owner(self):
        """Test deleting a layout template as owner"""
        template = LayoutTemplate.objects.create(
            name='To Delete',
            layout_type='grid',
            user=self.user
        )
        
        self.client.force_authenticate(user=self.user)
        response = self.client.delete(f'/api/layout-templates/{template.id}/')
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(LayoutTemplate.objects.filter(id=template.id).exists())
    
    def test_export_layout_template(self):
        """Test exporting a layout template"""
        template = LayoutTemplate.objects.create(
            name='Export Template',
            layout_type='grid',
            components={'test': 'data'},
            default_props={'theme': 'light'},
            user=self.user,
            is_public=True
        )
        
        self.client.force_authenticate(user=self.user)
        response = self.client.get(f'/api/layout-templates/{template.id}/export_template/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Export Template')
        self.assertEqual(response.data['layout_type'], 'grid')
        self.assertIn('export_version', response.data)
    
    def test_import_layout_template(self):
        """Test importing a layout template"""
        import_data = {
            'template_data': {
                'name': 'Imported Template',
                'description': 'Imported from JSON',
                'layout_type': 'flex',
                'components': {'imported': True},
                'default_props': {'theme': 'dark'},
                'is_public': False
            }
        }
        
        self.client.force_authenticate(user=self.user)
        response = self.client.post('/api/layout-templates/import_template/', import_data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'Imported Template')
        
        # Verify in database
        template = LayoutTemplate.objects.get(name='Imported Template')
        self.assertEqual(template.user, self.user)
        self.assertEqual(template.layout_type, 'flex')


class AppTemplateAPITest(TestCase):
    """Test cases for AppTemplate API endpoints"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.app_data = {
            'name': 'Test App',
            'description': 'A test app template',
            'app_category': 'business',
            'components': json.dumps({
                'pages': ['home', 'about']
            }),
            'default_props': json.dumps({
                'theme': 'modern'
            }),
            'required_components': json.dumps(['header', 'footer']),
            'preview_image': 'https://example.com/preview.jpg',
            'is_public': True
        }
    
    def test_create_app_template(self):
        """Test creating an app template"""
        self.client.force_authenticate(user=self.user)
        
        response = self.client.post('/api/app-templates/', self.app_data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'Test App')
        self.assertEqual(response.data['app_category'], 'business')
    
    def test_list_app_templates(self):
        """Test listing app templates"""
        AppTemplate.objects.create(
            name='Business App',
            app_category='business',
            user=self.user,
            is_public=True
        )
        AppTemplate.objects.create(
            name='E-commerce App',
            app_category='ecommerce',
            user=self.user,
            is_public=False
        )
        
        self.client.force_authenticate(user=self.user)
        response = self.client.get('/api/app-templates/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)
    
    def test_get_app_categories(self):
        """Test getting app template categories"""
        self.client.force_authenticate(user=self.user)
        response = self.client.get('/api/app-templates/categories/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsInstance(response.data, list)
        
        # Check that categories have value and label
        for category in response.data:
            self.assertIn('value', category)
            self.assertIn('label', category)
    
    def test_search_app_templates(self):
        """Test searching app templates"""
        AppTemplate.objects.create(
            name='E-commerce Store',
            app_category='ecommerce',
            user=self.user,
            is_public=True
        )
        AppTemplate.objects.create(
            name='Business Dashboard',
            app_category='business',
            user=self.user,
            is_public=True
        )
        
        self.client.force_authenticate(user=self.user)
        response = self.client.get('/api/app-templates/search/?q=ecommerce')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], 'E-commerce Store')


class TemplateServiceAPITest(TestCase):
    """Test cases for template service API endpoints"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_template_categories_endpoint(self):
        """Test template categories endpoint"""
        # Create some templates to generate categories
        LayoutTemplate.objects.create(
            name='Grid Layout',
            layout_type='grid',
            user=self.user
        )
        AppTemplate.objects.create(
            name='Business App',
            app_category='business',
            user=self.user
        )
        
        response = self.client.get('/api/template-categories/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('components', response.data)
        self.assertIn('layouts', response.data)
        self.assertIn('apps', response.data)
    
    def test_template_search_endpoint(self):
        """Test template search endpoint"""
        LayoutTemplate.objects.create(
            name='Responsive Grid',
            layout_type='grid',
            user=self.user,
            is_public=True
        )
        
        response = self.client.get('/api/template-search/?q=grid&public=true')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('layouts', response.data)
        self.assertEqual(len(response.data['layouts']), 1)
    
    def test_clone_template_endpoint(self):
        """Test clone template endpoint"""
        template = LayoutTemplate.objects.create(
            name='Original Template',
            layout_type='grid',
            user=self.user,
            is_public=True
        )
        
        self.client.force_authenticate(user=self.user)
        clone_data = {
            'template_id': template.id,
            'template_type': 'layout',
            'new_name': 'Cloned Template'
        }
        
        response = self.client.post('/api/clone-template/', clone_data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['template']['name'], 'Cloned Template')
        
        # Verify clone was created
        cloned_template = LayoutTemplate.objects.get(name='Cloned Template')
        self.assertEqual(cloned_template.user, self.user)
        self.assertFalse(cloned_template.is_public)  # Clones should be private
