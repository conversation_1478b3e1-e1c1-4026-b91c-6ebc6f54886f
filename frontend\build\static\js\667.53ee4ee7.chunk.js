"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[667],{1667:(e,n,t)=>{t.r(n),t.d(n,{default:()=>J});var r,a,o,l,i,c,s,m,d,u,p=t(4467),y=t(5544),g=t(7528),f=t(6540),E=t(1468),h=t(9740),v=t(5039),b=t(7852),F=t(3903),A=t(9237),x=t(8602),C=t(778),k=t(234),w=t(7046),T=t(261),B=t(3598),S=t(6067),I=t(1616),D=t(4318),P=t(3587),O=t(6020);function W(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function R(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?W(Object(t),!0).forEach((function(n){(0,p.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):W(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var N=P.I4.div(r||(r=(0,g.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),O.Ay.spacing[4]),z=P.I4.div(a||(a=(0,g.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: ",";\n"])),O.Ay.spacing[4]),j=P.I4.div(o||(o=(0,g.A)(["\n  display: flex;\n  align-items: center;\n  gap: ",";\n\n  .color-preview {\n    width: 36px;\n    height: 36px;\n    border-radius: ",";\n    border: 1px solid ",';\n    overflow: hidden;\n    position: relative;\n\n    input[type="color"] {\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      border: none;\n      padding: 0;\n      margin: 0;\n      cursor: pointer;\n    }\n  }\n\n  .color-input {\n    flex: 1;\n  }\n'])),O.Ay.spacing[2],O.Ay.borderRadius.md,O.Ay.colors.neutral[300]),Z=P.I4.div(l||(l=(0,g.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n\n  .font-preview {\n    padding: ",";\n    border: 1px solid ",";\n    border-radius: ",";\n    min-height: 60px;\n  }\n"])),O.Ay.spacing[2],O.Ay.spacing[2],O.Ay.colors.neutral[300],O.Ay.borderRadius.md),H=P.I4.div(i||(i=(0,g.A)(["\n  padding: ",";\n  border-radius: ",";\n  background-color: ",";\n  color: ",";\n  font-family: ",";\n  transition: all 0.3s ease;\n\n  h3 {\n    margin-top: 0;\n    margin-bottom: ",";\n    color: ",";\n  }\n\n  p {\n    margin-bottom: ",";\n  }\n\n  .buttons {\n    display: flex;\n    gap: ",";\n  }\n\n  .primary-button {\n    padding: "," ",";\n    background-color: ",";\n    color: white;\n    border: none;\n    border-radius: ",";\n    cursor: pointer;\n    transition: all 0.2s ease;\n\n    &:hover {\n      opacity: 0.9;\n      transform: translateY(-2px);\n    }\n  }\n\n  .secondary-button {\n    padding: "," ",";\n    background-color: ",";\n    color: white;\n    border: none;\n    border-radius: ",";\n    cursor: pointer;\n    transition: all 0.2s ease;\n\n    &:hover {\n      opacity: 0.9;\n      transform: translateY(-2px);\n    }\n  }\n\n  .card-example {\n    margin-top: ",";\n    padding: ",";\n    border-radius: ",";\n    background-color: ",";\n    border: 1px solid ",";\n  }\n\n  .input-example {\n    margin-top: ",";\n    padding: ",";\n    border-radius: ",";\n    border: 1px solid ",";\n    background-color: ",";\n    color: ",";\n    width: 100%;\n    font-family: ",";\n  }\n"])),O.Ay.spacing[4],O.Ay.borderRadius.md,(function(e){return e.backgroundColor||"white"}),(function(e){return e.textColor||"black"}),(function(e){return e.fontFamily||"inherit"}),O.Ay.spacing[3],(function(e){return e.textColor||"black"}),O.Ay.spacing[3],O.Ay.spacing[2],O.Ay.spacing[2],O.Ay.spacing[3],(function(e){return e.primaryColor||O.Ay.colors.primary.main}),O.Ay.borderRadius.md,O.Ay.spacing[2],O.Ay.spacing[3],(function(e){return e.secondaryColor||O.Ay.colors.secondary.main}),O.Ay.borderRadius.md,O.Ay.spacing[3],O.Ay.spacing[3],O.Ay.borderRadius.md,(function(e){return"#FFFFFF"===e.backgroundColor?"#F9FAFB":"rgba(255, 255, 255, 0.1)"}),(function(e){return"#FFFFFF"===e.backgroundColor?"#E5E7EB":"rgba(255, 255, 255, 0.2)"}),O.Ay.spacing[3],O.Ay.spacing[2],O.Ay.borderRadius.sm,(function(e){return"#FFFFFF"===e.backgroundColor?"#D1D5DB":"rgba(255, 255, 255, 0.2)"}),(function(e){return"#FFFFFF"===e.backgroundColor?"white":"rgba(255, 255, 255, 0.05)"}),(function(e){return e.textColor}),(function(e){return e.fontFamily})),M=(P.I4.div(c||(c=(0,g.A)(["\n  display: flex;\n  gap: ",";\n  margin-top: ",";\n\n  .color-swatch {\n    width: 24px;\n    height: 24px;\n    border-radius: 50%;\n    border: 1px solid ",";\n    cursor: pointer;\n  }\n"])),O.Ay.spacing[2],O.Ay.spacing[2],O.Ay.colors.neutral[300]),P.I4.div(s||(s=(0,g.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),O.Ay.spacing[3])),_=P.I4.div(m||(m=(0,g.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),O.Ay.spacing[2]),U=P.I4.div(d||(d=(0,g.A)(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ",";\n  background-color: ",";\n  border-radius: ",";\n  text-align: center;\n"])),O.Ay.spacing[8],O.Ay.colors.neutral[100],O.Ay.borderRadius.md),$=(0,P.I4)(P.Zp)(u||(u=(0,g.A)(["\n  border: ",";\n  transition: ",";\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ",";\n  }\n"])),(function(e){return e.isActive?"2px solid ".concat(O.Ay.colors.primary.main):"none"}),O.Ay.transitions.default,O.Ay.shadows.md),L=["Inter, sans-serif","Arial, sans-serif","Helvetica, sans-serif","Georgia, serif","Times New Roman, serif","Courier New, monospace","Verdana, sans-serif","Roboto, sans-serif","Open Sans, sans-serif","Lato, sans-serif"],Y=[{name:"Blue",primary:"#2563EB",secondary:"#10B981",background:"#FFFFFF",text:"#111827"},{name:"Purple",primary:"#8B5CF6",secondary:"#EC4899",background:"#FFFFFF",text:"#111827"},{name:"Green",primary:"#10B981",secondary:"#3B82F6",background:"#FFFFFF",text:"#111827"},{name:"Red",primary:"#EF4444",secondary:"#F59E0B",background:"#FFFFFF",text:"#111827"},{name:"Dark",primary:"#3B82F6",secondary:"#10B981",background:"#111827",text:"#F9FAFB"},{name:"Monochrome",primary:"#000000",secondary:"#666666",background:"#FFFFFF",text:"#333333"},{name:"Sunset",primary:"#FF5733",secondary:"#FFC300",background:"#FFFAF0",text:"#333333"},{name:"Ocean",primary:"#1A5276",secondary:"#2E86C1",background:"#EBF5FB",text:"#17202A"},{name:"Forest",primary:"#1E8449",secondary:"#F1C40F",background:"#F4F6F6",text:"#145A32"},{name:"Night Mode",primary:"#BB86FC",secondary:"#03DAC5",background:"#121212",text:"#E1E1E1"}];const J=function(){var e,n,t=(0,E.wA)(),r=(0,E.d4)((function(e){return e&&e.themes?Array.isArray(e.themes.themes)?e.themes.themes:[]:(console.warn("Redux state or themes slice not found, using fallback values"),[])})),a=(0,E.d4)((function(e){return e&&e.themes&&e.themes.activeTheme||"default"})),o=(0,E.d4)((function(e){return e&&e.themes&&e.themes.userPreferences?e.themes.userPreferences:{savedTheme:null,autoApplyTheme:!0}})),l=(0,f.useState)(""),i=(0,y.A)(l,2),c=i[0],s=i[1],m=(0,f.useState)("#2563EB"),d=(0,y.A)(m,2),u=d[0],p=d[1],g=(0,f.useState)("#10B981"),W=(0,y.A)(g,2),J=W[0],q=W[1],G=(0,f.useState)("#FFFFFF"),V=(0,y.A)(G,2),Q=V[0],K=V[1],X=(0,f.useState)("#111827"),ee=(0,y.A)(X,2),ne=ee[0],te=ee[1],re=(0,f.useState)("Inter, sans-serif"),ae=(0,y.A)(re,2),oe=ae[0],le=ae[1],ie=(0,f.useState)(null),ce=(0,y.A)(ie,2),se=ce[0],me=ce[1],de=(0,f.useState)(!1),ue=(0,y.A)(de,2),pe=ue[0],ye=ue[1],ge=(0,f.useState)({}),fe=(0,y.A)(ge,2),Ee=fe[0],he=fe[1];(0,f.useEffect)((function(){if(!Array.isArray(r)||0===r.length)try{t((0,I.zp)({id:"default",name:"Default Theme",primaryColor:"#2563EB",secondaryColor:"#10B981",backgroundColor:"#FFFFFF",textColor:"#111827",fontFamily:"Inter, sans-serif"}))}catch(e){console.error("Error dispatching addTheme action:",e)}if(!a)try{t((0,I.Ic)("default"))}catch(e){console.error("Error dispatching setActiveTheme action:",e)}}),[t]),(0,f.useEffect)((function(){var e=function(e){e.data&&"THEME_CACHE_UPDATED"===e.data.type&&console.log("Theme cache updated at:",e.data.timestamp)};return"serviceWorker"in navigator&&navigator.serviceWorker.addEventListener("message",e),function(){"serviceWorker"in navigator&&navigator.serviceWorker.removeEventListener("message",e)}}),[]);var ve=function(){var e={};return c.trim()||(e.name="Theme name is required"),he(e),0===Object.keys(e).length},be=function(e){me(e),s(e.name),p(e.primaryColor),q(e.secondaryColor),K(e.backgroundColor),te(e.textColor),le(e.fontFamily),ye(!0),he({})};return f.createElement(N,null,f.createElement(P.Zp,null,f.createElement(P.Zp.Header,null,f.createElement(P.Zp.Title,null,pe?"Edit Theme":"Create Theme"),pe&&f.createElement(P.$n,{variant:"text",size:"small",onClick:function(){s(""),p("#2563EB"),q("#10B981"),K("#FFFFFF"),te("#111827"),le("Inter, sans-serif"),me(null),ye(!1),he({})},startIcon:f.createElement(b.A,null)},"Cancel")),f.createElement(P.Zp.Content,null,f.createElement(M,null,f.createElement(_,null,f.createElement(P.pd,{label:"Theme Name",value:c,onChange:function(e){return s(e.target.value)},placeholder:"Enter theme name",fullWidth:!0,error:!!Ee.name,helperText:Ee.name})),f.createElement("div",{style:{marginBottom:O.Ay.spacing[2]}},f.createElement("div",{style:{fontWeight:O.Ay.typography.fontWeight.medium,marginBottom:O.Ay.spacing[2]}},"Color Palettes"),f.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:O.Ay.spacing[2]}},Y.map((function(e,n){return f.createElement(P.$n,{key:n,variant:"outline",size:"small",onClick:function(){return function(e){p(e.primary),q(e.secondary),K(e.background),te(e.text)}(e)}},e.name)})))),f.createElement(_,null,f.createElement("label",null,"Primary Color"),f.createElement(j,null,f.createElement("div",{className:"color-preview"},f.createElement("input",{type:"color",value:u,onChange:function(e){return p(e.target.value)}})),f.createElement(P.pd,{className:"color-input",value:u,onChange:function(e){return p(e.target.value)},fullWidth:!0}))),f.createElement(_,null,f.createElement("label",null,"Secondary Color"),f.createElement(j,null,f.createElement("div",{className:"color-preview"},f.createElement("input",{type:"color",value:J,onChange:function(e){return q(e.target.value)}})),f.createElement(P.pd,{className:"color-input",value:J,onChange:function(e){return q(e.target.value)},fullWidth:!0}))),f.createElement(_,null,f.createElement("label",null,"Background Color"),f.createElement(j,null,f.createElement("div",{className:"color-preview"},f.createElement("input",{type:"color",value:Q,onChange:function(e){return K(e.target.value)}})),f.createElement(P.pd,{className:"color-input",value:Q,onChange:function(e){return K(e.target.value)},fullWidth:!0}))),f.createElement(_,null,f.createElement("label",null,"Text Color"),f.createElement(j,null,f.createElement("div",{className:"color-preview"},f.createElement("input",{type:"color",value:ne,onChange:function(e){return te(e.target.value)}})),f.createElement(P.pd,{className:"color-input",value:ne,onChange:function(e){return te(e.target.value)},fullWidth:!0}))),f.createElement(_,null,f.createElement("label",null,"Font Family"),f.createElement("select",{value:oe,onChange:function(e){return le(e.target.value)},style:{width:"100%",padding:O.Ay.spacing[2],borderRadius:O.Ay.borderRadius.md,border:"1px solid ".concat(O.Ay.colors.neutral[300])}},L.map((function(e){return f.createElement("option",{key:e,value:e},e)}))),f.createElement(Z,null,f.createElement("div",{className:"font-preview",style:{fontFamily:oe}},"The quick brown fox jumps over the lazy dog."))))),f.createElement(P.Zp.Footer,null,pe?f.createElement(P.$n,{variant:"primary",onClick:function(){if(se&&ve()){var e=R(R({},se),{},{name:c.trim(),primaryColor:u,secondaryColor:J,backgroundColor:Q,textColor:ne,fontFamily:oe,updatedAt:(new Date).toISOString()});t((0,I.V_)(e)),"serviceWorker"in navigator&&navigator.serviceWorker.controller&&(navigator.serviceWorker.controller.postMessage({type:"THEME_UPDATED",theme:e}),navigator.serviceWorker.controller.postMessage({type:"UPDATE_THEME_CACHE"})),s(""),p("#2563EB"),q("#10B981"),K("#FFFFFF"),te("#111827"),le("Inter, sans-serif"),me(null),ye(!1),he({})}},startIcon:f.createElement(F.A,null),disabled:"default"===(null==se?void 0:se.id)},"Update Theme"):f.createElement(P.$n,{variant:"primary",onClick:function(){if(ve()){var e={id:Date.now().toString(),name:c.trim(),primaryColor:u,secondaryColor:J,backgroundColor:Q,textColor:ne,fontFamily:oe,createdAt:(new Date).toISOString()};t((0,I.zp)(e)),"serviceWorker"in navigator&&navigator.serviceWorker.controller&&(navigator.serviceWorker.controller.postMessage({type:"THEME_UPDATED",theme:e}),navigator.serviceWorker.controller.postMessage({type:"UPDATE_THEME_CACHE"})),s(""),p("#2563EB"),q("#10B981"),K("#FFFFFF"),te("#111827"),le("Inter, sans-serif"),he({})}},startIcon:f.createElement(A.A,null)},"Add Theme"))),f.createElement(P.Zp,null,f.createElement(P.Zp.Header,null,f.createElement(P.Zp.Title,null,"Theme Preview")),f.createElement(P.Zp.Content,null,f.createElement(H,{primaryColor:u,secondaryColor:J,backgroundColor:Q,textColor:ne,fontFamily:oe},f.createElement("h3",null,"Theme Preview"),f.createElement("p",null,"This is a preview of how your theme will look. The text color, background color, and font family are applied to this preview."),f.createElement("div",{className:"buttons"},f.createElement("button",{className:"primary-button"},"Primary Button"),f.createElement("button",{className:"secondary-button"},"Secondary Button")),f.createElement("div",{className:"card-example"},f.createElement("h4",{style:{margin:"0 0 8px 0",color:ne}},"Card Example"),f.createElement("p",{style:{margin:"0 0 8px 0",fontSize:"14px"}},"This shows how cards will appear with your theme.")),f.createElement("label",{style:{display:"block",marginTop:"16px",marginBottom:"8px"}},"Input Example:"),f.createElement("input",{type:"text",className:"input-example",placeholder:"Enter text here..."}),f.createElement("div",{style:{marginTop:"16px",display:"flex",justifyContent:"space-between"}},f.createElement("div",{style:{width:"24px",height:"24px",borderRadius:"50%",backgroundColor:u,border:"1px solid rgba(0,0,0,0.1)"}}),f.createElement("div",{style:{width:"24px",height:"24px",borderRadius:"50%",backgroundColor:J,border:"1px solid rgba(0,0,0,0.1)"}}),f.createElement("div",{style:{width:"24px",height:"24px",borderRadius:"50%",backgroundColor:Q,border:"1px solid rgba(0,0,0,0.1)"}}),f.createElement("div",{style:{width:"24px",height:"24px",borderRadius:"50%",backgroundColor:ne,border:"1px solid rgba(0,0,0,0.1)"}}))))),f.createElement(P.Zp,null,f.createElement(P.Zp.Header,null,f.createElement(P.Zp.Title,null,"Theme Preferences"),f.createElement(x.A,{style:{fontSize:"18px",color:O.Ay.colors.neutral[500]}})),f.createElement(P.Zp.Content,null,f.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:O.Ay.spacing[2]}},f.createElement("div",null,f.createElement("h4",{style:{margin:0,marginBottom:O.Ay.spacing[1]}},"Auto-apply Theme"),f.createElement("p",{style:{margin:0,color:O.Ay.colors.neutral[500],fontSize:O.Ay.typography.fontSize.sm}},"Automatically save your theme selection as a preference")),f.createElement(v.A,{checked:o.autoApplyTheme,onChange:function(){t({type:D._E}),t((function(e,n){try{var t=n().themes.userPreferences;localStorage.setItem("themePreferences",JSON.stringify(t))}catch(e){console.error("Error saving theme preferences:",e)}}));var e=!o.autoApplyTheme;h.Ay.success("Auto-apply theme ".concat(e?"enabled":"disabled"))},style:{backgroundColor:o.autoApplyTheme?O.Ay.colors.primary.main:void 0}})),f.createElement("div",{style:{marginTop:O.Ay.spacing[3],padding:O.Ay.spacing[3],backgroundColor:O.Ay.colors.neutral[100],borderRadius:O.Ay.borderRadius.md}},f.createElement("h4",{style:{margin:0,marginBottom:O.Ay.spacing[2]}},"Current Preferences"),f.createElement("div",{style:{display:"flex",gap:O.Ay.spacing[2],alignItems:"center"}},f.createElement("div",{style:{width:"20px",height:"20px",borderRadius:"50%",backgroundColor:o.savedTheme&&(null===(e=r.find((function(e){return e.id===o.savedTheme})))||void 0===e?void 0:e.primaryColor)||"#2563EB",border:"1px solid #e5e7eb"}}),f.createElement("span",null,o.savedTheme?(null===(n=r.find((function(e){return e.id===o.savedTheme})))||void 0===n?void 0:n.name)||"Default Theme":"No saved preference"))))),f.createElement(P.Zp,null,f.createElement(P.Zp.Header,null,f.createElement(P.Zp.Title,null,"Available Themes"),f.createElement("div",{style:{display:"flex",gap:O.Ay.spacing[2]}},f.createElement("input",{type:"file",id:"theme-import",accept:".json",style:{display:"none"},onChange:function(e){var n=e.target.files[0];if(n){var r=new FileReader;r.onload=function(n){try{var r=JSON.parse(n.target.result);if(!(r.name&&r.primaryColor&&r.secondaryColor&&r.backgroundColor&&r.textColor&&r.fontFamily))throw new Error("Invalid theme format");var a=R(R({},r),{},{id:Date.now().toString(),name:"".concat(r.name," (Imported)"),createdAt:(new Date).toISOString()});t((0,I.zp)(a)),"serviceWorker"in navigator&&navigator.serviceWorker.controller&&(navigator.serviceWorker.controller.postMessage({type:"THEME_UPDATED",theme:a}),navigator.serviceWorker.controller.postMessage({type:"UPDATE_THEME_CACHE"})),e.target.value=""}catch(n){console.error("Error importing theme:",n),e.target.value=""}},r.readAsText(n)}}}),f.createElement(P.$n,{variant:"outline",size:"small",onClick:function(){return document.getElementById("theme-import").click()}},"Import Theme"))),f.createElement(P.Zp.Content,null,0===r.length?f.createElement(U,null,f.createElement("div",{style:{fontSize:"48px",color:O.Ay.colors.neutral[400],marginBottom:O.Ay.spacing[4]}},f.createElement(C.A,null)),f.createElement("h3",null,"No Themes Yet"),f.createElement("p",null,"Create your first theme to get started")):f.createElement(z,null,(Array.isArray(r)?r:[]).map((function(e){return f.createElement($,{key:e.id,elevation:"sm",isActive:a===e.id},f.createElement(P.Zp.Header,null,f.createElement("div",null,f.createElement("div",{style:{fontWeight:e.typography.fontWeight.semibold}},e.name),f.createElement("div",{style:{fontSize:e.typography.fontSize.sm,color:e.colors.neutral[500]}},e.fontFamily.split(",")[0])),f.createElement("div",{style:{display:"flex",gap:e.spacing[1]}},"default"!==e.id&&f.createElement(f.Fragment,null,f.createElement(P.$n,{variant:"text",size:"small",onClick:function(){return function(e){var n=e||se;if(n){var t=JSON.stringify(n,null,2),r=new Blob([t],{type:"application/json"}),a=URL.createObjectURL(r),o=document.createElement("a");o.href=a,o.download="".concat(n.name.replace(/\s+/g,"-").toLowerCase(),"-theme.json"),document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(a)}}(e)},title:"Export Theme"},f.createElement(k.A,null)),f.createElement(P.$n,{variant:"text",size:"small",onClick:function(){return function(e){var n=R(R({},e),{},{id:Date.now().toString(),name:"".concat(e.name," (Copy)"),createdAt:(new Date).toISOString()});t((0,I.zp)(n))}(e)},title:"Duplicate Theme"},f.createElement(w.A,null)),f.createElement(P.$n,{variant:"text",size:"small",onClick:function(){return be(e)},title:"Edit Theme"},f.createElement(T.A,null)),f.createElement(P.$n,{variant:"text",size:"small",onClick:function(){var n;"default"!==(n=e.id)&&(t((0,I.Qo)(n)),se&&se.id===n&&(s(""),p("#2563EB"),q("#10B981"),K("#FFFFFF"),te("#111827"),le("Inter, sans-serif"),me(null),ye(!1)),a===n&&t((0,I.Ic)("default")))},title:"Delete Theme"},f.createElement(B.A,null))))),f.createElement(P.Zp.Content,{onClick:function(){return be(e)}},f.createElement(H,{primaryColor:e.primaryColor,secondaryColor:e.secondaryColor,backgroundColor:e.backgroundColor,textColor:e.textColor,fontFamily:e.fontFamily,style:{height:"120px",overflow:"hidden"}},f.createElement("h3",{style:{fontSize:"16px"}},"Preview"),f.createElement("p",{style:{fontSize:"14px"}},"Sample text with this theme applied."),f.createElement("div",{className:"buttons"},f.createElement("button",{className:"primary-button",style:{padding:"4px 8px",fontSize:"12px"}},"Button"),f.createElement("button",{className:"secondary-button",style:{padding:"4px 8px",fontSize:"12px"}},"Button")))),f.createElement(P.Zp.Footer,null,f.createElement("div",{style:{display:"flex",gap:e.spacing[2]}},f.createElement("div",{style:{width:"20px",height:"20px",backgroundColor:e.primaryColor,borderRadius:"50%",border:"1px solid #e5e7eb"}}),f.createElement("div",{style:{width:"20px",height:"20px",backgroundColor:e.secondaryColor,borderRadius:"50%",border:"1px solid #e5e7eb"}})),a===e.id?f.createElement(P.$n,{variant:"text",size:"small",startIcon:f.createElement(S.A,null),style:{color:e.colors.success.main}},"Active"):f.createElement(P.$n,{variant:"outline",size:"small",onClick:function(){return function(e){try{t((0,I.Ic)(e)),h.Ay.success("Theme activated successfully");var n=(Array.isArray(r)?r:[]).concat([{id:"default",name:"Default Theme",primaryColor:"#2563EB",secondaryColor:"#10B981",backgroundColor:"#FFFFFF",textColor:"#111827",fontFamily:"Inter, sans-serif"}]).find((function(n){return n.id===e}));"serviceWorker"in navigator&&navigator.serviceWorker.controller&&n&&navigator.serviceWorker.controller.postMessage({type:"THEME_UPDATED",theme:n})}catch(e){console.error("Error setting active theme:",e),h.Ay.error("Failed to activate theme. Please try again.")}}(e.id)}},"Activate")))}))))))}}}]);