"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[67],{4067:(e,t,n)=>{n.r(t),n.d(t,{default:()=>W});var r,a,l,s,i,o,c,m=n(436),u=n(467),d=n(5544),p=n(7528),A=n(4756),g=n.n(A),f=n(6540),y=n(3016),E=n(677),h=n(9467),w=n(9249),b=n(9740),v=n(7197),x=n(2702),I=n(7355),k=n(1196),P=n(6552),C=n(7450),N=n(5763),F=n(7851),S=n(4741),q=n(4976),B=n(7767),L=n(1468),R=(n(1616),n(9391)),j=n(3587),z=n(6020),G=y.A.Title,T=y.A.Text,_=j.I4.div(r||(r=(0,p.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  padding: ",";\n  background-color: ",";\n"])),z.Ay.spacing[4],z.Ay.colors.neutral[100]),H=(0,j.I4)(E.A)(a||(a=(0,p.A)(["\n  width: 100%;\n  max-width: 480px;\n  box-shadow: ",";\n  border-radius: ",";\n"])),z.Ay.shadows.lg,z.Ay.borderRadius.lg),V=(0,j.I4)(h.A)(l||(l=(0,p.A)(["\n  .ant-form-item-label {\n    text-align: left;\n  }\n"]))),D=(0,j.I4)(w.Ay)(s||(s=(0,p.A)(["\n  width: 100%;\n"]))),O=(0,j.I4)(q.N_)(i||(i=(0,p.A)(["\n  float: right;\n"]))),U=((0,j.I4)(T)(o||(o=(0,p.A)(["\n  display: block;\n  text-align: center;\n  margin-top: 16px;\n"]))),(0,j.I4)(w.Ay)(c||(c=(0,p.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n"]))));const W=function(){var e,t=(0,B.Zp)(),n=(0,B.zy)(),r=((0,L.wA)(),(0,R.As)()),a=r.login,l=r.register,s=r.isAuthenticated,i=r.isLoading,o=r.error,c=h.A.useForm(),p=(0,d.A)(c,1)[0],A=(0,f.useState)(!0),y=(0,d.A)(A,2),E=y[0],q=y[1],j=(0,f.useState)(null),W=(0,d.A)(j,2),Z=W[0],J=W[1],K=(null===(e=n.state)||void 0===e||null===(e=e.from)||void 0===e?void 0:e.pathname)||"/dashboard";(0,f.useEffect)((function(){s&&t(K,{replace:!0})}),[s,t,K]),(0,f.useEffect)((function(){J(o)}),[o]);var M=function(){var e=(0,u.A)(g().mark((function e(n){var r,s,i;return g().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(J(null),e.prev=1,!E){e.next=9;break}return e.next=5,a(n.email,n.password);case 5:(r=e.sent).success?(b.Ay.success("Login successful!"),t(K,{replace:!0})):J(r.error||"Login failed"),e.next=14;break;case 9:return s={username:n.email.split("@")[0],email:n.email,password:n.password,first_name:n.firstName,last_name:n.lastName},e.next=12,l(s);case 12:(i=e.sent).success?(b.Ay.success("Registration successful!"),t(K,{replace:!0})):J(i.error||"Registration failed");case 14:e.next=20;break;case 16:e.prev=16,e.t0=e.catch(1),console.error("Authentication error:",e.t0),J(e.t0.message||"An error occurred during authentication");case 20:case"end":return e.stop()}}),e,null,[[1,16]])})));return function(t){return e.apply(this,arguments)}}(),Q=function(e){b.Ay.info("".concat(e," login is not implemented in this demo"))};return f.createElement(_,null,f.createElement(H,null,f.createElement("div",{style:{textAlign:"center",marginBottom:z.Ay.spacing[4]}},f.createElement(G,{level:2,style:{margin:0}},E?"Welcome Back":"Create Account"),f.createElement(T,{type:"secondary"},E?"Sign in to continue to App Builder":"Register to start building amazing applications")),Z&&f.createElement(v.A,{message:"Authentication Error",description:Z,type:"error",showIcon:!0,style:{marginBottom:z.Ay.spacing[4]}}),f.createElement(V,{form:p,name:"auth_form",layout:"vertical",onFinish:M,initialValues:{remember:!0}},!E&&f.createElement(x.A,{style:{display:"flex",gap:z.Ay.spacing[3]}},f.createElement(h.A.Item,{name:"firstName",label:"First Name",rules:[{required:!0,message:"Please enter your first name"}],style:{flex:1}},f.createElement(I.A,{placeholder:"First Name"})),f.createElement(h.A.Item,{name:"lastName",label:"Last Name",rules:[{required:!0,message:"Please enter your last name"}],style:{flex:1}},f.createElement(I.A,{placeholder:"Last Name"}))),f.createElement(h.A.Item,{name:"email",label:E?"Email or Username":"Email",rules:[{required:!0,message:"Please enter your email"}].concat((0,m.A)(E?[]:[{type:"email",message:"Please enter a valid email"}]))},f.createElement(I.A,{prefix:f.createElement(C.A,null),placeholder:"Email"})),f.createElement(h.A.Item,{name:"password",label:"Password",rules:[{required:!0,message:"Please enter your password"}]},f.createElement(I.A.Password,{prefix:f.createElement(N.A,null),placeholder:"Password"})),!E&&f.createElement(h.A.Item,{name:"confirmPassword",label:"Confirm Password",dependencies:["password"],rules:[{required:!0,message:"Please confirm your password"},function(e){var t=e.getFieldValue;return{validator:function(e,n){return n&&t("password")!==n?Promise.reject(new Error("The two passwords do not match")):Promise.resolve()}}}]},f.createElement(I.A.Password,{prefix:f.createElement(N.A,null),placeholder:"Confirm Password"})),E&&f.createElement(h.A.Item,null,f.createElement(h.A.Item,{name:"remember",valuePropName:"checked",noStyle:!0},f.createElement(k.A,null,"Remember me")),f.createElement(O,{to:"/forgot-password"},"Forgot password?")),f.createElement(h.A.Item,null,f.createElement(D,{type:"primary",htmlType:"submit",size:"large",loading:i},E?"Sign In":"Create Account"))),f.createElement(P.A,null,f.createElement(T,{type:"secondary"},"Or continue with")),f.createElement(x.A,{direction:"horizontal",style:{width:"100%",justifyContent:"center",gap:z.Ay.spacing[3],marginBottom:z.Ay.spacing[4]}},f.createElement(U,{icon:f.createElement(F.A,null),onClick:function(){return Q("Google")}},"Google"),f.createElement(U,{icon:f.createElement(S.A,null),onClick:function(){return Q("GitHub")}},"GitHub")),f.createElement("div",{style:{textAlign:"center"}},f.createElement(T,{type:"secondary"},E?"Don't have an account? ":"Already have an account? ",f.createElement(w.Ay,{type:"link",onClick:function(){p.resetFields(),q(!E),J(null)},style:{padding:0}},E?"Sign up now":"Sign in")))))}}}]);