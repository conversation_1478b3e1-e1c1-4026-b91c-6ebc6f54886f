from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from django.core.cache import cache
from django.db import transaction
from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly, AllowAny
from django_filters.rest_framework import DjangoFilterBackend
import json
import logging
import os
from datetime import datetime
from .validators import validate_json_request, validate_app_data
from .error_handling import error_response, handle_exception, not_found_error
from .cache_utils import cache_response, invalidate_app_cache
from .rate_limiting import rate_limit
from .security import sanitize_request, add_security_headers
from .pagination import StandardResultsSetPagination, LargeResultsSetPagination, SmallResultsSetPagination
from .filters import AppFilter, ComponentTemplateFilter, LayoutTemplateFilter, AppTemplateFilter
from .api_docs import document_api, response_example

# Set up logger
logger = logging.getLogger(__name__)

from .models import App, AppVersion, ComponentTemplate, LayoutTemplate, AppTemplate
from .serializers import AppSerializer, AppVersionSerializer, ComponentTemplateSerializer, LayoutTemplateSerializer, AppTemplateSerializer
from .services.template_service import TemplateService

# Custom permissions
class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object to edit it.
    """
    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed to any request
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions are only allowed to the owner
        return obj.user == request.user

# Health check endpoint
@csrf_exempt
@api_view(['GET'])
@permission_classes([permissions.AllowAny])
@handle_exception
@add_security_headers
@rate_limit(requests=100, period=60, scope='ip')  # 100 requests per minute per IP
@document_api(
    description="Health check endpoint",
    version="v1",
    authentication=None,
    permissions=[],
    rate_limiting={
        'limit': 100,
        'period': 60,
        'scope': 'ip'
    },
    response_examples=[
        response_example(200, {
            "status": "ok",
            "timestamp": "2023-04-09T12:34:56Z",
            "service": "app-builder-backend",
            "version": "1.0.0",
            "database": "connected",
            "cache": "connected"
        }, "Successful health check")
    ]
)
def health_check(request):
    """
    Health check endpoint for the API.
    Returns a 200 OK response with basic health information and CSP headers.
    """
    logger.info("Health check requested from " + request.META.get('REMOTE_ADDR', 'unknown'))

    # Get system information
    import psutil
    import socket
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()

    # Get hostname and IP address
    hostname = socket.gethostname()
    try:
        ip_address = socket.gethostbyname(hostname)
    except:
        ip_address = '127.0.0.1'

    response_data = {
        'status': 'ok',
        'timestamp': datetime.now().isoformat(),
        'service': 'app-builder-backend',
        'version': '1.0.0',
        'environment': os.environ.get('DJANGO_ENV', 'development'),
        'system_info': {
            'hostname': hostname,
            'ip_address': ip_address,
            'pid': os.getpid(),
            'memory_usage': memory_info.rss,
            'cpu_percent': process.cpu_percent(),
        },
        'request_info': {
            'remote_addr': request.META.get('REMOTE_ADDR'),
            'http_host': request.META.get('HTTP_HOST'),
            'server_port': request.META.get('SERVER_PORT'),
            'server_protocol': request.META.get('SERVER_PROTOCOL'),
        }
    }

    response = HttpResponse(
        json.dumps(response_data),
        content_type='application/json'
    )

    # Add Content Security Policy headers
    csp_directives = [
        "default-src 'self'",
        "script-src 'self' 'strict-dynamic' 'nonce-{nonce}'",
        "object-src 'none'",
        "base-uri 'self'",
        "require-trusted-types-for 'script'"
    ]
    response['Content-Security-Policy'] = '; '.join(csp_directives)

    # Add CORS headers
    response['Access-Control-Allow-Origin'] = '*'
    response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
    response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'

    return response

# ViewSets for our models
class AppViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows apps to be viewed or edited.

    This viewset automatically provides `list`, `create`, `retrieve`, `update` and `destroy` actions.

    Additionally, it provides the following custom actions:
    * `create_version`: Create a new version of the app
    * `versions`: Get all versions of the app
    * `restore_version`: Restore the app to a specific version

    Filtering:
    * `name`: Filter by name (case-insensitive, contains)
    * `description`: Filter by description (case-insensitive, contains)
    * `is_public`: Filter by public status (exact match)
    * `user`: Filter by user (exact match)
    * `created_after`: Filter by creation date (greater than or equal)
    * `created_before`: Filter by creation date (less than or equal)
    * `updated_after`: Filter by update date (greater than or equal)
    * `updated_before`: Filter by update date (less than or equal)

    Searching:
    * Search by name or description

    Ordering:
    * Order by name, created_at, or updated_at
    * Default ordering is by updated_at (descending)

    Pagination:
    * Default page size: 10
    * Maximum page size: 100
    """
    serializer_class = AppSerializer
    permission_classes = [permissions.IsAuthenticated, IsOwnerOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = AppFilter
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at', 'updated_at']
    ordering = ['-updated_at']
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        """
        This view should return a list of all apps for the currently authenticated user,
        plus any public apps.
        """
        user = self.request.user
        return App.objects.filter(user=user) | App.objects.filter(is_public=True)

    def perform_create(self, serializer):
        """Save the app with the current user"""
        serializer.save(user=self.request.user)

    @action(detail=True, methods=['post'])
    def create_version(self, request, pk=None):
        """Create a new version of the app"""
        app = self.get_object()

        # Get the commit message from the request
        commit_message = request.data.get('commit_message', '')

        # Get the next version number
        version_number = AppVersion.objects.filter(app=app).count() + 1

        # Create the new version
        version = AppVersion.objects.create(
            app=app,
            version_number=version_number,
            app_data=app.app_data,
            created_by=request.user,
            commit_message=commit_message
        )

        # Invalidate cache
        cache_key = f'app_data_{app.id}'
        cache.delete(cache_key)

        # Return the new version
        serializer = AppVersionSerializer(version)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['get'])
    def versions(self, request, pk=None):
        """Get all versions of the app"""
        app = self.get_object()
        versions = AppVersion.objects.filter(app=app)
        serializer = AppVersionSerializer(versions, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def restore_version(self, request, pk=None):
        """Restore the app to a specific version"""
        app = self.get_object()

        # Get the version number from the request
        version_number = request.data.get('version_number')
        if not version_number:
            return Response(
                {'error': 'Version number is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Get the version
            version = AppVersion.objects.get(app=app, version_number=version_number)

            # Update the app
            app.app_data = version.app_data
            app.updated_at = timezone.now()
            app.save()

            # Create a new version
            new_version_number = AppVersion.objects.filter(app=app).count() + 1
            AppVersion.objects.create(
                app=app,
                version_number=new_version_number,
                app_data=app.app_data,
                created_by=request.user,
                commit_message=f"Restored from version {version_number}"
            )

            # Invalidate cache
            cache_key = f'app_data_{app.id}'
            cache.delete(cache_key)

            # Return the updated app
            serializer = AppSerializer(app)
            return Response(serializer.data)

        except AppVersion.DoesNotExist:
            return Response(
                {'error': f'Version {version_number} not found'},
                status=status.HTTP_404_NOT_FOUND
            )

class ComponentTemplateViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows component templates to be viewed or edited.

    This viewset automatically provides `list`, `create`, `retrieve`, `update` and `destroy` actions.

    Filtering:
    * `name`: Filter by name (case-insensitive, contains)
    * `description`: Filter by description (case-insensitive, contains)
    * `component_type`: Filter by component type (case-insensitive, exact match)
    * `is_public`: Filter by public status (exact match)
    * `user`: Filter by user (exact match)
    * `created_after`: Filter by creation date (greater than or equal)
    * `created_before`: Filter by creation date (less than or equal)

    Searching:
    * Search by name, description, or component type

    Ordering:
    * Order by name or created_at
    * Default ordering is by created_at (descending)

    Pagination:
    * Default page size: 5
    * Maximum page size: 20
    """
    serializer_class = ComponentTemplateSerializer
    permission_classes = [permissions.IsAuthenticated, IsOwnerOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ComponentTemplateFilter
    search_fields = ['name', 'description', 'component_type']
    ordering_fields = ['name', 'created_at']
    ordering = ['-created_at']
    pagination_class = SmallResultsSetPagination

    def get_queryset(self):
        """
        This view should return a list of all component templates for the currently authenticated user,
        plus any public component templates.
        """
        user = self.request.user
        return ComponentTemplate.objects.filter(user=user) | ComponentTemplate.objects.filter(is_public=True)

    def perform_create(self, serializer):
        """Save the component template with the current user"""
        serializer.save(user=self.request.user)


class LayoutTemplateViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows layout templates to be viewed or edited.

    This viewset automatically provides `list`, `create`, `retrieve`, `update` and `destroy` actions.

    Filtering:
    * `name`: Filter by name (case-insensitive, contains)
    * `description`: Filter by description (case-insensitive, contains)
    * `layout_type`: Filter by layout type (case-insensitive, exact match)
    * `is_public`: Filter by public status (exact match)
    * `user`: Filter by user (exact match)
    * `created_after`: Filter by creation date (greater than or equal)
    * `created_before`: Filter by creation date (less than or equal)

    Searching:
    * Search by name, description, or layout type

    Ordering:
    * Order by name or created_at
    * Default ordering is by created_at (descending)

    Pagination:
    * Default page size: 5
    * Maximum page size: 20
    """
    serializer_class = LayoutTemplateSerializer
    permission_classes = [permissions.IsAuthenticated, IsOwnerOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = LayoutTemplateFilter
    search_fields = ['name', 'description', 'layout_type']
    ordering_fields = ['name', 'created_at']
    ordering = ['-created_at']
    pagination_class = SmallResultsSetPagination

    def get_queryset(self):
        """
        This view should return a list of all layout templates for the currently authenticated user,
        plus any public layout templates.
        """
        user = self.request.user
        return LayoutTemplate.objects.filter(user=user) | LayoutTemplate.objects.filter(is_public=True)

    def perform_create(self, serializer):
        """Save the layout template with the current user"""
        serializer.save(user=self.request.user)

    @action(detail=False, methods=['post'])
    def import_template(self, request):
        """Import a layout template from JSON data"""
        try:
            template_data = request.data.get('template_data')
            if not template_data:
                return Response(
                    {'error': 'Template data is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Create the template
            template = LayoutTemplate.objects.create(
                name=template_data.get('name', 'Imported Layout'),
                description=template_data.get('description', ''),
                layout_type=template_data.get('layout_type', 'custom'),
                components=template_data.get('components', {}),
                default_props=template_data.get('default_props', {}),
                user=request.user,
                is_public=template_data.get('is_public', False)
            )

            serializer = self.get_serializer(template)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {'error': f'Failed to import template: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['get'])
    def export_template(self, request, pk=None):
        """Export a layout template as JSON data"""
        template = self.get_object()

        export_data = {
            'name': template.name,
            'description': template.description,
            'layout_type': template.layout_type,
            'components': template.get_components_json(),
            'default_props': template.get_default_props_json(),
            'is_public': template.is_public,
            'created_at': template.created_at.isoformat(),
            'export_version': '1.0'
        }

        return Response(export_data)


class AppTemplateViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows app templates to be viewed or edited.

    This viewset automatically provides `list`, `create`, `retrieve`, `update` and `destroy` actions.

    Filtering:
    * `name`: Filter by name (case-insensitive, contains)
    * `description`: Filter by description (case-insensitive, contains)
    * `app_category`: Filter by app category (exact match)
    * `is_public`: Filter by public status (exact match)
    * `user`: Filter by user (exact match)
    * `created_after`: Filter by creation date (greater than or equal)
    * `created_before`: Filter by creation date (less than or equal)

    Searching:
    * Search by name, description, or app category

    Ordering:
    * Order by name or created_at
    * Default ordering is by created_at (descending)

    Pagination:
    * Default page size: 5
    * Maximum page size: 20
    """
    serializer_class = AppTemplateSerializer
    permission_classes = [permissions.IsAuthenticated, IsOwnerOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = AppTemplateFilter
    search_fields = ['name', 'description', 'app_category']
    ordering_fields = ['name', 'created_at']
    ordering = ['-created_at']
    pagination_class = SmallResultsSetPagination

    def get_queryset(self):
        """
        This view should return a list of all app templates for the currently authenticated user,
        plus any public app templates.
        """
        user = self.request.user
        return AppTemplate.objects.filter(user=user) | AppTemplate.objects.filter(is_public=True)

    def perform_create(self, serializer):
        """Save the app template with the current user"""
        serializer.save(user=self.request.user)

    @action(detail=False, methods=['post'])
    def import_template(self, request):
        """Import an app template from JSON data"""
        try:
            template_data = request.data.get('template_data')
            if not template_data:
                return Response(
                    {'error': 'Template data is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Create the template
            template = AppTemplate.objects.create(
                name=template_data.get('name', 'Imported App'),
                description=template_data.get('description', ''),
                app_category=template_data.get('app_category', 'other'),
                components=template_data.get('components', {}),
                default_props=template_data.get('default_props', {}),
                required_components=template_data.get('required_components', []),
                preview_image=template_data.get('preview_image', ''),
                user=request.user,
                is_public=template_data.get('is_public', False)
            )

            serializer = self.get_serializer(template)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {'error': f'Failed to import template: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['get'])
    def export_template(self, request, pk=None):
        """Export an app template as JSON data"""
        template = self.get_object()

        export_data = {
            'name': template.name,
            'description': template.description,
            'app_category': template.app_category,
            'components': template.get_components_json(),
            'default_props': template.get_default_props_json(),
            'required_components': template.get_required_components_list(),
            'preview_image': template.preview_image,
            'is_public': template.is_public,
            'created_at': template.created_at.isoformat(),
            'export_version': '1.0'
        }

        return Response(export_data)

    @action(detail=False, methods=['get'])
    def categories(self, request):
        """Get available app template categories"""
        categories = [
            {'value': choice[0], 'label': choice[1]}
            for choice in AppTemplate.APP_CATEGORIES
        ]
        return Response(categories)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """Search across all app templates"""
        query = request.GET.get('q', '')
        category = request.GET.get('category', '')

        results = TemplateService.search_templates(
            query=query,
            template_type='apps',
            category=category,
            is_public=True
        )

        return Response(results['apps'])


# Template management endpoints
@csrf_exempt
@api_view(['GET'])
@permission_classes([permissions.AllowAny])
@handle_exception
@add_security_headers
@rate_limit(requests=100, period=60, scope='ip')
def template_categories(request):
    """Get all template categories"""
    return JsonResponse({
        'components': TemplateService.get_component_categories(),
        'layouts': TemplateService.get_layout_categories(),
        'apps': TemplateService.get_app_categories()
    })

@csrf_exempt
@api_view(['GET'])
@permission_classes([permissions.AllowAny])
@handle_exception
@add_security_headers
@rate_limit(requests=100, period=60, scope='ip')
def template_search(request):
    """Search templates across all types"""
    query = request.GET.get('q', '')
    template_type = request.GET.get('type', '')
    category = request.GET.get('category', '')
    is_public = request.GET.get('public', '').lower() == 'true' if request.GET.get('public') else None

    results = TemplateService.search_templates(
        query=query,
        template_type=template_type,
        category=category,
        is_public=is_public
    )

    return JsonResponse(results)

@csrf_exempt
@api_view(['GET'])
@permission_classes([permissions.AllowAny])
@handle_exception
@add_security_headers
@rate_limit(requests=100, period=60, scope='ip')
def featured_templates(request):
    """Get featured templates"""
    templates = TemplateService.get_featured_templates()

    # Serialize the templates
    featured = {
        'components': ComponentTemplateSerializer(templates['components'], many=True).data,
        'layouts': LayoutTemplateSerializer(templates['layouts'], many=True).data,
        'apps': AppTemplateSerializer(templates['apps'], many=True).data
    }

    return JsonResponse(featured)

@csrf_exempt
@api_view(['GET'])
@permission_classes([permissions.AllowAny])
@handle_exception
@add_security_headers
@rate_limit(requests=100, period=60, scope='ip')
def template_stats(request):
    """Get template statistics"""
    stats = TemplateService.get_template_stats()
    return JsonResponse(stats)

@csrf_exempt
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@handle_exception
@add_security_headers
@rate_limit(requests=20, period=60, scope='user')
def clone_template(request):
    """Clone a template for the current user"""
    template_id = request.data.get('template_id')
    template_type = request.data.get('template_type')
    new_name = request.data.get('new_name')

    if not template_id or not template_type:
        return JsonResponse(
            {'error': 'template_id and template_type are required'},
            status=400
        )

    try:
        cloned = TemplateService.clone_template(
            template_id=template_id,
            template_type=template_type,
            user=request.user,
            new_name=new_name
        )

        # Serialize the cloned template
        if template_type == 'component':
            serializer = ComponentTemplateSerializer(cloned)
        elif template_type == 'layout':
            serializer = LayoutTemplateSerializer(cloned)
        elif template_type == 'app':
            serializer = AppTemplateSerializer(cloned)

        return JsonResponse({
            'message': 'Template cloned successfully',
            'template': serializer.data
        })

    except Exception as e:
        return JsonResponse(
            {'error': str(e)},
            status=400
        )


# Legacy API endpoints for backward compatibility
@csrf_exempt
@api_view(['GET'])
@permission_classes([permissions.AllowAny])  # Keep this public for backward compatibility
@handle_exception
@add_security_headers
@sanitize_request
@rate_limit(requests=100, period=60, scope='ip')  # 100 requests per minute per IP
@cache_response(timeout=300, key_prefix="get_app_data")
def get_app_data(request):
    """Get app data for the first app"""
    logger.info("App data requested")

    app_id = request.GET.get('app_id')
    if app_id:
        try:
            app = App.objects.get(id=app_id)
        except App.DoesNotExist:
            logger.warning(f"App with ID {app_id} not found")
            return not_found_error('App', app_id)
    else:
        app = App.objects.first()
        if not app:
            logger.warning("No app found, returning empty app data")
            return JsonResponse({"components": [], "layouts": [], "styles": {}, "data": {}}, safe=False)

    logger.debug(f"Returning app data for app {app.id}")
    app_data = app.get_app_data_json()

    # Add metadata to the response
    response_data = {
        **app_data,
        "_metadata": {
            "app_id": app.id,
            "app_name": app.name,
            "updated_at": app.updated_at.isoformat() if app.updated_at else None,
            "version": AppVersion.objects.filter(app=app).count()
        }
    }

    return JsonResponse(response_data, safe=False)

@csrf_exempt
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticatedOrReadOnly])  # Require authentication for write operations
@handle_exception
@add_security_headers
@sanitize_request
@rate_limit(requests=30, period=60, scope='user')  # 30 requests per minute per user
def save_app_data(request):
    """Save app data for the first app"""
    # Validate request
    is_valid, data, error_resp = validate_json_request(request)
    if not is_valid:
        return error_resp

    # Validate app data structure
    is_valid, error_message = validate_app_data(data)
    if not is_valid:
        return error_response('INVALID_FORMAT', error_message, 400)

    logger.info("Saving app data")

    with transaction.atomic():
        app = App.objects.first()
        if app:
            logger.debug(f"Updating existing app with ID {app.id}")
            # Create a new version before updating
            version_number = AppVersion.objects.filter(app=app).count() + 1

            # Get commit message from request if available
            commit_message = "Auto-saved version"
            if isinstance(data, dict) and 'commit_message' in data:
                commit_message = data.pop('commit_message')

            # Create version
            AppVersion.objects.create(
                app=app,
                version_number=version_number,
                app_data=app.app_data,
                created_by=request.user if hasattr(request, 'user') and request.user.is_authenticated else None,
                commit_message=commit_message
            )

            # Update the app
            app.app_data = json.dumps(data)
            app.updated_at = timezone.now()
            app.save()
        else:
            logger.debug("Creating new app")
            # Create a new app
            app_name = "Default App"
            if isinstance(data, dict) and 'app_name' in data:
                app_name = data.pop('app_name')

            app = App.objects.create(
                name=app_name,
                app_data=json.dumps(data),
                user=request.user if hasattr(request, 'user') and request.user.is_authenticated else None
            )

            # Create the first version
            AppVersion.objects.create(
                app=app,
                version_number=1,
                app_data=app.app_data,
                created_by=request.user if hasattr(request, 'user') and request.user.is_authenticated else None,
                commit_message="Initial version"
            )
            version_number = 1

        # Invalidate cache
        invalidate_app_cache(app.id)
        logger.debug(f"Cache invalidated for app {app.id}")

        return JsonResponse({
            "message": "App data updated",
            "app_data": data,
            "app_id": app.id,
            "app_name": app.name,
            "version": version_number,
            "status": "success",
            "timestamp": timezone.now().isoformat()
        })

@csrf_exempt
@api_view(['GET'])
@permission_classes([permissions.AllowAny])  # Keep this public for backward compatibility
@handle_exception
@add_security_headers
@sanitize_request
@rate_limit(requests=50, period=60, scope='ip')  # 50 requests per minute per IP
@cache_response(timeout=600, key_prefix="export_app_data")  # Cache for 10 minutes
def export_app_data(request):
    """Export app data in the specified format"""
    format_type = request.GET.get('format', 'json')
    app_id = request.GET.get('app_id', None)

    logger.info(f"Exporting app data in {format_type} format")

    # Get the app
    if app_id:
        try:
            app = App.objects.get(id=app_id)
        except App.DoesNotExist:
            logger.warning(f"App with ID {app_id} not found")
            return not_found_error('App', app_id)
    else:
        app = App.objects.first()
        if not app:
            logger.warning("No app found")
            return not_found_error('App')

    # Get app data
    app_data = app.get_app_data_json()

    # Export in the requested format
    if format_type == 'json':
        logger.debug(f"Exporting app {app.id} as JSON")
        return JsonResponse(app_data, safe=False)
    elif format_type in ['web', 'mobile']:
        logger.debug(f"Exporting app {app.id} as {format_type} code")
        # Use the existing export functionality
        from core.app_logic import AppBuilder

        app_builder = AppBuilder()
        app_builder.components = app_data.get('components', [])
        app_builder.layouts = app_data.get('layouts', [])
        app_builder.styles = app_data.get('styles', {})
        app_builder.data = app_data.get('data', {})

        generated_code = app_builder.export(format=format_type)
        return JsonResponse({
            "code": generated_code,
            "format": format_type,
            "app_id": app.id,
            "app_name": app.name,
            "status": "success"
        })
    else:
        logger.warning(f"Invalid export format: {format_type}")
        return error_response(
            'INVALID_FORMAT',
            f"Invalid export format: {format_type}",
            400,
            {"supported_formats": ["json", "web", "mobile"]}
        )

def report_errors(request):
    """Handle frontend error reporting"""
    logger.info(f"report_errors called with method: {request.method}")

    if request.method == 'GET':
        return JsonResponse({'message': 'Error reporting endpoint is working', 'method': 'GET'})
    elif request.method == 'POST':
        logger.info("Receiving error report from frontend")

        try:
            # Parse JSON data
            data = json.loads(request.body.decode('utf-8'))
        except json.JSONDecodeError:
            logger.warning("Invalid JSON in error report request")
            return JsonResponse({'error': 'Invalid JSON'}, status=400)

        # Log the errors for monitoring
        errors = data.get('errors', [])
        if isinstance(errors, list):
            for error in errors:
                error_msg = error.get('error', {}).get('message', 'Unknown error') if isinstance(error, dict) else str(error)
                logger.warning(f"Frontend error reported: {error_msg}")
        else:
            logger.warning(f"Frontend error reported: {data}")

        # Return success response
        return JsonResponse({
            'status': 'success',
            'message': 'Errors received and logged',
            'count': len(errors) if isinstance(errors, list) else 1
        })
    else:
        return JsonResponse({'error': 'Method not allowed'}, status=405)

@csrf_exempt
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])  # Require authentication for import
@handle_exception
@add_security_headers
@sanitize_request
@rate_limit(requests=10, period=60, scope='user')  # 10 requests per minute per user
def import_app_data(request):
    """Import app data from a file"""
    logger.info("Importing app data from file")

    if 'file' not in request.FILES:
        logger.warning("No file provided in import request")
        return error_response('INVALID_FILE', "No file provided", 400)

    file = request.FILES['file']
    logger.debug(f"Processing file: {file.name} ({file.size} bytes)")

    # Check file extension
    if not file.name.endswith('.json'):
        logger.warning(f"Unsupported file format: {file.name}")
        return error_response(
            'INVALID_FILE',
            "Unsupported file format",
            400,
            {"supported_formats": [".json"], "provided": file.name}
        )

    # Check file size
    max_size = 5 * 1024 * 1024  # 5MB
    if file.size > max_size:
        logger.warning(f"File too large: {file.size} bytes")
        return error_response(
            'FILE_TOO_LARGE',
            "File too large",
            400,
            {"max_size": max_size, "file_size": file.size}
        )

    # Parse JSON file
    try:
        app_data = json.loads(file.read().decode('utf-8'))
    except json.JSONDecodeError as e:
        logger.warning(f"Invalid JSON file: {str(e)}")
        return error_response('INVALID_JSON', "Invalid JSON file", 400, {"error": str(e)})

    # Validate app data structure
    is_valid, error_message = validate_app_data(app_data)
    if not is_valid:
        logger.warning(f"Invalid app data structure: {error_message}")
        return error_response('INVALID_FORMAT', error_message, 400)

    # Create new app
    app_name = request.POST.get('name', file.name.replace('.json', ''))
    is_public = request.POST.get('is_public', 'false').lower() == 'true'

    logger.info(f"Creating new app '{app_name}' from imported data")

    with transaction.atomic():
        app = App.objects.create(
            name=app_name,
            user=request.user if hasattr(request, 'user') and request.user.is_authenticated else None,
            app_data=json.dumps(app_data),
            is_public=is_public
        )

        # Create the first version
        commit_message = request.POST.get('commit_message', "Imported app")
        AppVersion.objects.create(
            app=app,
            version_number=1,
            app_data=app.app_data,
            created_by=request.user if hasattr(request, 'user') and request.user.is_authenticated else None,
            commit_message=commit_message
        )

    logger.info(f"App imported successfully with ID {app.id}")

    return JsonResponse({
        "message": "App imported successfully",
        "app_id": app.id,
        "app_name": app.name,
        "is_public": app.is_public,
        "status": "success",
        "timestamp": timezone.now().isoformat()
    })

