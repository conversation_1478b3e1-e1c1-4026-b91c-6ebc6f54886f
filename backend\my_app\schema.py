"""
GraphQL schema for the App Builder API.
"""
import graphene
import json
from graphene_django import DjangoObjectType
from graphene_django.filter import DjangoFilterConnectionField
from django.contrib.auth.models import User
from .models import App, AppVersion, ComponentTemplate, LayoutTemplate, AppTemplate
from .error_handling import not_found_error
from .security import sanitize_json

class UserType(DjangoObjectType):
    """
    GraphQL type for the User model.
    """
    class Meta:
        model = User
        fields = ('id', 'username', 'email', 'first_name', 'last_name', 'date_joined')
        filter_fields = {
            'username': ['exact', 'icontains'],
            'email': ['exact', 'icontains'],
            'date_joined': ['exact', 'gt', 'lt'],
        }
        interfaces = (graphene.relay.Node,)

class AppType(DjangoObjectType):
    """
    GraphQL type for the App model.
    """
    app_data_json = graphene.JSONString()
    
    class Meta:
        model = App
        fields = ('id', 'name', 'description', 'user', 'is_public', 'created_at', 'updated_at')
        filter_fields = {
            'name': ['exact', 'icontains'],
            'description': ['exact', 'icontains'],
            'is_public': ['exact'],
            'user': ['exact'],
            'created_at': ['exact', 'gt', 'lt'],
            'updated_at': ['exact', 'gt', 'lt'],
        }
        interfaces = (graphene.relay.Node,)
        
    def resolve_app_data_json(self, info):
        """
        Resolve the app_data_json field.
        """
        return json.loads(self.app_data)

class AppVersionType(DjangoObjectType):
    """
    GraphQL type for the AppVersion model.
    """
    app_data_json = graphene.JSONString()
    
    class Meta:
        model = AppVersion
        fields = ('id', 'app', 'version_number', 'created_by', 'created_at', 'commit_message')
        filter_fields = {
            'app': ['exact'],
            'version_number': ['exact', 'gt', 'lt'],
            'created_by': ['exact'],
            'created_at': ['exact', 'gt', 'lt'],
        }
        interfaces = (graphene.relay.Node,)
        
    def resolve_app_data_json(self, info):
        """
        Resolve the app_data_json field.
        """
        return json.loads(self.app_data)

class ComponentTemplateType(DjangoObjectType):
    """
    GraphQL type for the ComponentTemplate model.
    """
    default_props_json = graphene.JSONString()
    
    class Meta:
        model = ComponentTemplate
        fields = ('id', 'name', 'description', 'component_type', 'user', 'is_public', 'created_at')
        filter_fields = {
            'name': ['exact', 'icontains'],
            'description': ['exact', 'icontains'],
            'component_type': ['exact', 'icontains'],
            'is_public': ['exact'],
            'user': ['exact'],
            'created_at': ['exact', 'gt', 'lt'],
        }
        interfaces = (graphene.relay.Node,)
        
    def resolve_default_props_json(self, info):
        """
        Resolve the default_props_json field.
        """
        return json.loads(self.default_props)


class LayoutTemplateType(DjangoObjectType):
    """
    GraphQL type for the LayoutTemplate model.
    """
    components_json = graphene.JSONString()
    default_props_json = graphene.JSONString()

    class Meta:
        model = LayoutTemplate
        fields = ('id', 'name', 'description', 'layout_type', 'user', 'is_public', 'created_at')
        filter_fields = {
            'name': ['exact', 'icontains'],
            'description': ['exact', 'icontains'],
            'layout_type': ['exact', 'icontains'],
            'is_public': ['exact'],
            'user': ['exact'],
            'created_at': ['exact', 'gt', 'lt'],
        }
        interfaces = (graphene.relay.Node,)

    def resolve_components_json(self, info):
        """
        Resolve the components_json field.
        """
        return self.get_components_json()

    def resolve_default_props_json(self, info):
        """
        Resolve the default_props_json field.
        """
        return self.get_default_props_json()


class AppTemplateType(DjangoObjectType):
    """
    GraphQL type for the AppTemplate model.
    """
    components_json = graphene.JSONString()
    default_props_json = graphene.JSONString()
    required_components_list = graphene.JSONString()

    class Meta:
        model = AppTemplate
        fields = ('id', 'name', 'description', 'app_category', 'preview_image', 'user', 'is_public', 'created_at')
        filter_fields = {
            'name': ['exact', 'icontains'],
            'description': ['exact', 'icontains'],
            'app_category': ['exact', 'icontains'],
            'is_public': ['exact'],
            'user': ['exact'],
            'created_at': ['exact', 'gt', 'lt'],
        }
        interfaces = (graphene.relay.Node,)

    def resolve_components_json(self, info):
        """
        Resolve the components_json field.
        """
        return self.get_components_json()

    def resolve_default_props_json(self, info):
        """
        Resolve the default_props_json field.
        """
        return self.get_default_props_json()

    def resolve_required_components_list(self, info):
        """
        Resolve the required_components_list field.
        """
        return self.get_required_components_list()


class AppInput(graphene.InputObjectType):
    """
    Input type for creating or updating an App.
    """
    name = graphene.String(required=True)
    description = graphene.String()
    is_public = graphene.Boolean()
    app_data = graphene.JSONString()

class AppVersionInput(graphene.InputObjectType):
    """
    Input type for creating an AppVersion.
    """
    app_id = graphene.ID(required=True)
    commit_message = graphene.String(required=True)

class ComponentTemplateInput(graphene.InputObjectType):
    """
    Input type for creating or updating a ComponentTemplate.
    """
    name = graphene.String(required=True)
    description = graphene.String()
    component_type = graphene.String(required=True)
    default_props = graphene.JSONString(required=True)
    is_public = graphene.Boolean()


class LayoutTemplateInput(graphene.InputObjectType):
    """
    Input type for creating or updating a LayoutTemplate.
    """
    name = graphene.String(required=True)
    description = graphene.String()
    layout_type = graphene.String(required=True)
    components = graphene.JSONString(required=True)
    default_props = graphene.JSONString()
    is_public = graphene.Boolean()


class AppTemplateInput(graphene.InputObjectType):
    """
    Input type for creating or updating an AppTemplate.
    """
    name = graphene.String(required=True)
    description = graphene.String()
    app_category = graphene.String(required=True)
    components = graphene.JSONString(required=True)
    default_props = graphene.JSONString()
    required_components = graphene.JSONString()
    preview_image = graphene.String()
    is_public = graphene.Boolean()


class CreateApp(graphene.Mutation):
    """
    Mutation for creating an App.
    """
    class Arguments:
        input = AppInput(required=True)
        
    app = graphene.Field(AppType)
    
    @staticmethod
    def mutate(root, info, input):
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")
            
        app = App.objects.create(
            name=input.name,
            description=input.description or '',
            is_public=input.is_public or False,
            user=user,
            app_data=json.dumps(input.app_data or {})
        )
        
        return CreateApp(app=app)

class UpdateApp(graphene.Mutation):
    """
    Mutation for updating an App.
    """
    class Arguments:
        id = graphene.ID(required=True)
        input = AppInput(required=True)
        
    app = graphene.Field(AppType)
    
    @staticmethod
    def mutate(root, info, id, input):
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")
            
        try:
            app = App.objects.get(pk=id)
        except App.DoesNotExist:
            raise Exception(f"App with ID {id} not found")
            
        if app.user != user and not user.is_staff:
            raise Exception("Permission denied")
            
        app.name = input.name
        if input.description is not None:
            app.description = input.description
        if input.is_public is not None:
            app.is_public = input.is_public
        if input.app_data is not None:
            app.app_data = json.dumps(input.app_data)
            
        app.save()
        
        return UpdateApp(app=app)

class DeleteApp(graphene.Mutation):
    """
    Mutation for deleting an App.
    """
    class Arguments:
        id = graphene.ID(required=True)
        
    success = graphene.Boolean()
    
    @staticmethod
    def mutate(root, info, id):
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")
            
        try:
            app = App.objects.get(pk=id)
        except App.DoesNotExist:
            raise Exception(f"App with ID {id} not found")
            
        if app.user != user and not user.is_staff:
            raise Exception("Permission denied")
            
        app.delete()
        
        return DeleteApp(success=True)

class CreateAppVersion(graphene.Mutation):
    """
    Mutation for creating an AppVersion.
    """
    class Arguments:
        input = AppVersionInput(required=True)
        
    app_version = graphene.Field(AppVersionType)
    
    @staticmethod
    def mutate(root, info, input):
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")
            
        try:
            app = App.objects.get(pk=input.app_id)
        except App.DoesNotExist:
            raise Exception(f"App with ID {input.app_id} not found")
            
        if app.user != user and not user.is_staff:
            raise Exception("Permission denied")
            
        # Get the latest version number
        latest_version = AppVersion.objects.filter(app=app).order_by('-version_number').first()
        version_number = 1 if not latest_version else latest_version.version_number + 1
        
        app_version = AppVersion.objects.create(
            app=app,
            version_number=version_number,
            app_data=app.app_data,
            created_by=user,
            commit_message=input.commit_message
        )
        
        return CreateAppVersion(app_version=app_version)

class RestoreAppVersion(graphene.Mutation):
    """
    Mutation for restoring an App to a specific version.
    """
    class Arguments:
        app_id = graphene.ID(required=True)
        version_number = graphene.Int(required=True)
        
    app = graphene.Field(AppType)
    
    @staticmethod
    def mutate(root, info, app_id, version_number):
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")
            
        try:
            app = App.objects.get(pk=app_id)
        except App.DoesNotExist:
            raise Exception(f"App with ID {app_id} not found")
            
        if app.user != user and not user.is_staff:
            raise Exception("Permission denied")
            
        try:
            app_version = AppVersion.objects.get(app=app, version_number=version_number)
        except AppVersion.DoesNotExist:
            raise Exception(f"Version {version_number} not found")
            
        app.app_data = app_version.app_data
        app.save()
        
        return RestoreAppVersion(app=app)

class CreateComponentTemplate(graphene.Mutation):
    """
    Mutation for creating a ComponentTemplate.
    """
    class Arguments:
        input = ComponentTemplateInput(required=True)
        
    component_template = graphene.Field(ComponentTemplateType)
    
    @staticmethod
    def mutate(root, info, input):
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")
            
        component_template = ComponentTemplate.objects.create(
            name=input.name,
            description=input.description or '',
            component_type=input.component_type,
            default_props=json.dumps(input.default_props),
            is_public=input.is_public or False,
            user=user
        )
        
        return CreateComponentTemplate(component_template=component_template)

class UpdateComponentTemplate(graphene.Mutation):
    """
    Mutation for updating a ComponentTemplate.
    """
    class Arguments:
        id = graphene.ID(required=True)
        input = ComponentTemplateInput(required=True)
        
    component_template = graphene.Field(ComponentTemplateType)
    
    @staticmethod
    def mutate(root, info, id, input):
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")
            
        try:
            component_template = ComponentTemplate.objects.get(pk=id)
        except ComponentTemplate.DoesNotExist:
            raise Exception(f"ComponentTemplate with ID {id} not found")
            
        if component_template.user != user and not user.is_staff:
            raise Exception("Permission denied")
            
        component_template.name = input.name
        if input.description is not None:
            component_template.description = input.description
        component_template.component_type = input.component_type
        component_template.default_props = json.dumps(input.default_props)
        if input.is_public is not None:
            component_template.is_public = input.is_public
            
        component_template.save()
        
        return UpdateComponentTemplate(component_template=component_template)

class DeleteComponentTemplate(graphene.Mutation):
    """
    Mutation for deleting a ComponentTemplate.
    """
    class Arguments:
        id = graphene.ID(required=True)
        
    success = graphene.Boolean()
    
    @staticmethod
    def mutate(root, info, id):
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")
            
        try:
            component_template = ComponentTemplate.objects.get(pk=id)
        except ComponentTemplate.DoesNotExist:
            raise Exception(f"ComponentTemplate with ID {id} not found")
            
        if component_template.user != user and not user.is_staff:
            raise Exception("Permission denied")
            
        component_template.delete()
        
        return DeleteComponentTemplate(success=True)


class CreateLayoutTemplate(graphene.Mutation):
    """
    Mutation for creating a LayoutTemplate.
    """
    class Arguments:
        input = LayoutTemplateInput(required=True)

    layout_template = graphene.Field(LayoutTemplateType)

    @staticmethod
    def mutate(root, info, input):
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")

        layout_template = LayoutTemplate.objects.create(
            name=input.name,
            description=input.description or '',
            layout_type=input.layout_type,
            components=input.components,
            default_props=input.default_props or {},
            is_public=input.is_public or False,
            user=user
        )

        return CreateLayoutTemplate(layout_template=layout_template)


class UpdateLayoutTemplate(graphene.Mutation):
    """
    Mutation for updating a LayoutTemplate.
    """
    class Arguments:
        id = graphene.ID(required=True)
        input = LayoutTemplateInput(required=True)

    layout_template = graphene.Field(LayoutTemplateType)

    @staticmethod
    def mutate(root, info, id, input):
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")

        try:
            layout_template = LayoutTemplate.objects.get(pk=id)
        except LayoutTemplate.DoesNotExist:
            raise Exception(f"LayoutTemplate with ID {id} not found")

        if layout_template.user != user and not user.is_staff:
            raise Exception("Permission denied")

        layout_template.name = input.name
        if input.description is not None:
            layout_template.description = input.description
        layout_template.layout_type = input.layout_type
        layout_template.components = input.components
        if input.default_props is not None:
            layout_template.default_props = input.default_props
        if input.is_public is not None:
            layout_template.is_public = input.is_public

        layout_template.save()

        return UpdateLayoutTemplate(layout_template=layout_template)


class DeleteLayoutTemplate(graphene.Mutation):
    """
    Mutation for deleting a LayoutTemplate.
    """
    class Arguments:
        id = graphene.ID(required=True)

    success = graphene.Boolean()

    @staticmethod
    def mutate(root, info, id):
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")

        try:
            layout_template = LayoutTemplate.objects.get(pk=id)
        except LayoutTemplate.DoesNotExist:
            raise Exception(f"LayoutTemplate with ID {id} not found")

        if layout_template.user != user and not user.is_staff:
            raise Exception("Permission denied")

        layout_template.delete()

        return DeleteLayoutTemplate(success=True)


class CreateAppTemplate(graphene.Mutation):
    """
    Mutation for creating an AppTemplate.
    """
    class Arguments:
        input = AppTemplateInput(required=True)

    app_template = graphene.Field(AppTemplateType)

    @staticmethod
    def mutate(root, info, input):
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")

        app_template = AppTemplate.objects.create(
            name=input.name,
            description=input.description or '',
            app_category=input.app_category,
            components=input.components,
            default_props=input.default_props or {},
            required_components=input.required_components or [],
            preview_image=input.preview_image or '',
            is_public=input.is_public or False,
            user=user
        )

        return CreateAppTemplate(app_template=app_template)


class UpdateAppTemplate(graphene.Mutation):
    """
    Mutation for updating an AppTemplate.
    """
    class Arguments:
        id = graphene.ID(required=True)
        input = AppTemplateInput(required=True)

    app_template = graphene.Field(AppTemplateType)

    @staticmethod
    def mutate(root, info, id, input):
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")

        try:
            app_template = AppTemplate.objects.get(pk=id)
        except AppTemplate.DoesNotExist:
            raise Exception(f"AppTemplate with ID {id} not found")

        if app_template.user != user and not user.is_staff:
            raise Exception("Permission denied")

        app_template.name = input.name
        if input.description is not None:
            app_template.description = input.description
        app_template.app_category = input.app_category
        app_template.components = input.components
        if input.default_props is not None:
            app_template.default_props = input.default_props
        if input.required_components is not None:
            app_template.required_components = input.required_components
        if input.preview_image is not None:
            app_template.preview_image = input.preview_image
        if input.is_public is not None:
            app_template.is_public = input.is_public

        app_template.save()

        return UpdateAppTemplate(app_template=app_template)


class DeleteAppTemplate(graphene.Mutation):
    """
    Mutation for deleting an AppTemplate.
    """
    class Arguments:
        id = graphene.ID(required=True)

    success = graphene.Boolean()

    @staticmethod
    def mutate(root, info, id):
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")

        try:
            app_template = AppTemplate.objects.get(pk=id)
        except AppTemplate.DoesNotExist:
            raise Exception(f"AppTemplate with ID {id} not found")

        if app_template.user != user and not user.is_staff:
            raise Exception("Permission denied")

        app_template.delete()

        return DeleteAppTemplate(success=True)


class SaveAppData(graphene.Mutation):
    """
    Mutation for saving app data.
    """
    class Arguments:
        app_id = graphene.ID(required=True)
        app_data = graphene.JSONString(required=True)
        
    app = graphene.Field(AppType)
    
    @staticmethod
    def mutate(root, info, app_id, app_data):
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")
            
        try:
            app = App.objects.get(pk=app_id)
        except App.DoesNotExist:
            raise Exception(f"App with ID {app_id} not found")
            
        if app.user != user and not user.is_staff:
            raise Exception("Permission denied")
            
        # Sanitize the app data
        app_data = sanitize_json(app_data)
        
        app.app_data = json.dumps(app_data)
        app.save()
        
        return SaveAppData(app=app)

class Mutation(graphene.ObjectType):
    """
    Root mutation type.
    """
    create_app = CreateApp.Field()
    update_app = UpdateApp.Field()
    delete_app = DeleteApp.Field()
    create_app_version = CreateAppVersion.Field()
    restore_app_version = RestoreAppVersion.Field()
    create_component_template = CreateComponentTemplate.Field()
    update_component_template = UpdateComponentTemplate.Field()
    delete_component_template = DeleteComponentTemplate.Field()
    create_layout_template = CreateLayoutTemplate.Field()
    update_layout_template = UpdateLayoutTemplate.Field()
    delete_layout_template = DeleteLayoutTemplate.Field()
    create_app_template = CreateAppTemplate.Field()
    update_app_template = UpdateAppTemplate.Field()
    delete_app_template = DeleteAppTemplate.Field()
    save_app_data = SaveAppData.Field()

class Query(graphene.ObjectType):
    """
    Root query type.
    """
    node = graphene.relay.Node.Field()
    
    # User queries
    users = DjangoFilterConnectionField(UserType)
    user = graphene.Field(UserType, id=graphene.ID())
    me = graphene.Field(UserType)
    
    # App queries
    apps = DjangoFilterConnectionField(AppType)
    app = graphene.Field(AppType, id=graphene.ID())
    
    # AppVersion queries
    app_versions = DjangoFilterConnectionField(AppVersionType)
    app_version = graphene.Field(AppVersionType, id=graphene.ID())
    
    # ComponentTemplate queries
    component_templates = DjangoFilterConnectionField(ComponentTemplateType)
    component_template = graphene.Field(ComponentTemplateType, id=graphene.ID())

    # LayoutTemplate queries
    layout_templates = DjangoFilterConnectionField(LayoutTemplateType)
    layout_template = graphene.Field(LayoutTemplateType, id=graphene.ID())

    # AppTemplate queries
    app_templates = DjangoFilterConnectionField(AppTemplateType)
    app_template = graphene.Field(AppTemplateType, id=graphene.ID())

    def resolve_user(self, info, id):
        """
        Resolve a user by ID.
        """
        if not info.context.user.is_authenticated:
            raise Exception("Authentication required")
            
        try:
            return User.objects.get(pk=id)
        except User.DoesNotExist:
            return None
            
    def resolve_me(self, info):
        """
        Resolve the current user.
        """
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")
            
        return user
        
    def resolve_app(self, info, id):
        """
        Resolve an app by ID.
        """
        try:
            app = App.objects.get(pk=id)
        except App.DoesNotExist:
            return None
            
        user = info.context.user
        if not app.is_public and (not user.is_authenticated or (app.user != user and not user.is_staff)):
            raise Exception("Permission denied")
            
        return app
        
    def resolve_app_version(self, info, id):
        """
        Resolve an app version by ID.
        """
        try:
            app_version = AppVersion.objects.get(pk=id)
        except AppVersion.DoesNotExist:
            return None
            
        user = info.context.user
        if not app_version.app.is_public and (not user.is_authenticated or (app_version.app.user != user and not user.is_staff)):
            raise Exception("Permission denied")
            
        return app_version
        
    def resolve_component_template(self, info, id):
        """
        Resolve a component template by ID.
        """
        try:
            component_template = ComponentTemplate.objects.get(pk=id)
        except ComponentTemplate.DoesNotExist:
            return None
            
        user = info.context.user
        if not component_template.is_public and (not user.is_authenticated or (component_template.user != user and not user.is_staff)):
            raise Exception("Permission denied")
            
        return component_template

    def resolve_layout_template(self, info, id):
        """
        Resolve a layout template by ID.
        """
        try:
            layout_template = LayoutTemplate.objects.get(pk=id)
        except LayoutTemplate.DoesNotExist:
            return None

        user = info.context.user
        if not layout_template.is_public and (not user.is_authenticated or (layout_template.user != user and not user.is_staff)):
            raise Exception("Permission denied")

        return layout_template

    def resolve_app_template(self, info, id):
        """
        Resolve an app template by ID.
        """
        try:
            app_template = AppTemplate.objects.get(pk=id)
        except AppTemplate.DoesNotExist:
            return None

        user = info.context.user
        if not app_template.is_public and (not user.is_authenticated or (app_template.user != user and not user.is_staff)):
            raise Exception("Permission denied")

        return app_template

schema = graphene.Schema(query=Query, mutation=Mutation)
