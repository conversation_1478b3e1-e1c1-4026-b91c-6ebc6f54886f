import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  SaveOutlined,
  CloseOutlined,
  AppstoreOutlined
} from '@ant-design/icons';

import { addComponent, updateComponent, removeComponent } from '../../redux/minimal-store';
import { styled } from '../../design-system';
import { Button, Card, Input, Select } from '../../design-system';
import theme from '../../design-system/theme';
import { EnhancedComponentProperties } from './property-editor';

// Import enhanced component builder
import EnhancedComponentBuilder from '../builder/EnhancedComponentBuilder';

const ComponentBuilderContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[4]};
`;

const ComponentGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: ${theme.spacing[4]};
`;

const ComponentPreview = styled.div`
  padding: ${theme.spacing[4]};
  border: 1px solid ${theme.colors.neutral[200]};
  border-radius: ${theme.borderRadius.md};
  background-color: white;
`;

const PropertyEditor = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[3]};
`;

const PropertyGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[2]};
`;

const ComponentItem = styled.div`
  cursor: pointer;
  transition: ${theme.transitions.default};
  border: 2px solid ${props => props.isSelected ? theme.colors.primary.main : 'transparent'};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${theme.shadows.md};
  }
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${theme.spacing[8]};
  background-color: ${theme.colors.neutral[100]};
  border-radius: ${theme.borderRadius.md};
  text-align: center;
`;

const componentTypes = [
  { value: 'container', label: 'Container' },
  { value: 'text', label: 'Text' },
  { value: 'button', label: 'Button' },
  { value: 'input', label: 'Input Field' },
  { value: 'image', label: 'Image' },
  { value: 'card', label: 'Card' },
  { value: 'list', label: 'List' },
  { value: 'custom', label: 'Custom' }
];

const ComponentBuilder = () => {
  // Check if enhanced mode is available
  const [useEnhancedMode, setUseEnhancedMode] = useState(true);

  // If enhanced mode is enabled, use the enhanced component builder
  if (useEnhancedMode) {
    return <EnhancedComponentBuilder />;
  }

  // Fallback to original implementation
  const dispatch = useDispatch();

  // Fix selector path to match store structure
  const components = useSelector(state =>
    state.app?.components ||
    state.appData?.components ||
    []
  );

  // Add loading state
  const [isLoading, setIsLoading] = useState(true);

  // All useState hooks must be called before any conditional returns
  const [componentName, setComponentName] = useState('');
  const [componentType, setComponentType] = useState('container');
  const [componentProps, setComponentProps] = useState('{}');
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [errors, setErrors] = useState({});

  // Function to create sample components
  const createSampleComponents = () => {
    const sampleComponents = [
      {
        id: 'button-1',
        name: 'Primary Button',
        type: 'button',
        props: {
          text: 'Click Me',
          variant: 'primary',
          size: 'medium',
          onClick: 'handleButtonClick'
        },
        createdAt: new Date().toISOString()
      },
      {
        id: 'text-1',
        name: 'Header Text',
        type: 'text',
        props: {
          content: 'Welcome to App Builder',
          variant: 'h1',
          color: '#2563EB',
          align: 'center'
        },
        createdAt: new Date().toISOString()
      },
      {
        id: 'input-1',
        name: 'Email Input',
        type: 'input',
        props: {
          label: 'Email Address',
          placeholder: 'Enter your email',
          type: 'email',
          required: true,
          validation: 'email'
        },
        createdAt: new Date().toISOString()
      },
      {
        id: 'card-1',
        name: 'Feature Card',
        type: 'card',
        props: {
          title: 'Easy to Use',
          description: 'Build applications with a simple drag-and-drop interface',
          image: 'https://via.placeholder.com/150',
          elevation: 'md'
        },
        createdAt: new Date().toISOString()
      }
    ];

    // Add each sample component to the store
    sampleComponents.forEach(component => {
      dispatch(addComponent(component));
    });
  };

  useEffect(() => {
    // Initialize component
    const init = async () => {
      try {
        setIsLoading(true);

        // Check if we already have components
        if (components.length === 0) {
          // Add sample components if none exist
          createSampleComponents();
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Failed to initialize ComponentBuilder:', error);
        setIsLoading(false);
      }
    };

    init();
  }, [components.length, dispatch]);

  // All useEffect hooks must be called before any conditional returns
  useEffect(() => {
    console.log('ComponentBuilder mounting...');
    return () => {
      console.log('ComponentBuilder unmounting...');
    };
  }, []);

  useEffect(() => {
    console.log('Components updated:', components);
  }, [components]);

  useEffect(() => {
    if (Object.keys(errors).length > 0) {
      console.error('ComponentBuilder errors:', errors);
    }
  }, [errors]);

  if (isLoading) {
    return <div>Loading ComponentBuilder...</div>;
  }

  const validateForm = () => {
    const newErrors = {};

    if (!componentName.trim()) {
      newErrors.name = 'Component name is required';
    }

    try {
      if (componentProps) {
        JSON.parse(componentProps);
      }
    } catch (error) {
      newErrors.props = 'Invalid JSON format';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddComponent = () => {
    if (!validateForm()) return;

    try {
      const propsObject = componentProps ? JSON.parse(componentProps) : {};

      const newComponent = {
        id: Date.now().toString(),
        name: componentName.trim(),
        type: componentType,
        props: propsObject,
        createdAt: new Date().toISOString()
      };

      dispatch(addComponent(newComponent))
        .then(() => {
          // Reset form
          setComponentName('');
          setComponentType('container');
          setComponentProps('{}');
          setErrors({});
        })
        .catch(error => {
          console.error('Failed to add component:', error);
          setErrors({ submit: 'Failed to add component' });
        });
    } catch (error) {
      setErrors({ ...errors, props: error.message });
    }
  };

  const handleUpdateComponent = () => {
    if (!selectedComponent || !validateForm()) return;

    try {
      const propsObject = componentProps ? JSON.parse(componentProps) : {};

      const updatedComponent = {
        ...selectedComponent,
        name: componentName.trim(),
        type: componentType,
        props: propsObject,
        updatedAt: new Date().toISOString()
      };

      dispatch(updateComponent(updatedComponent));

      // Reset form and exit edit mode
      setComponentName('');
      setComponentType('container');
      setComponentProps('{}');
      setSelectedComponent(null);
      setEditMode(false);
      setErrors({});
    } catch (error) {
      setErrors({ ...errors, props: error.message });
    }
  };

  const handleRemoveComponent = (id) => {
    dispatch(removeComponent(id));

    // If the removed component was selected, reset the form
    if (selectedComponent && selectedComponent.id === id) {
      setComponentName('');
      setComponentType('container');
      setComponentProps('{}');
      setSelectedComponent(null);
      setEditMode(false);
    }
  };

  const handleSelectComponent = (component) => {
    setSelectedComponent(component);
    setComponentName(component.name);
    setComponentType(component.type);
    setComponentProps(JSON.stringify(component.props, null, 2));
    setEditMode(true);
    setErrors({});
  };

  const handleCancelEdit = () => {
    setComponentName('');
    setComponentType('container');
    setComponentProps('{}');
    setSelectedComponent(null);
    setEditMode(false);
    setErrors({});
  };

  const handleDuplicateComponent = (component) => {
    const duplicatedComponent = {
      ...component,
      id: Date.now().toString(),
      name: `${component.name} (Copy)`,
      createdAt: new Date().toISOString()
    };

    dispatch(addComponent(duplicatedComponent));
  };

  return (
    <ComponentBuilderContainer>
      <Card>
        <Card.Header>
          <Card.Title>{editMode ? 'Edit Component' : 'Create Component'}</Card.Title>
          {editMode && (
            <Button
              variant="text"
              size="small"
              onClick={handleCancelEdit}
              startIcon={<CloseOutlined />}
            >
              Cancel
            </Button>
          )}
        </Card.Header>
        <Card.Content>
          <PropertyEditor>
            <PropertyGroup>
              <Input
                label="Component Name"
                value={componentName}
                onChange={(e) => setComponentName(e.target.value)}
                placeholder="Enter component name"
                fullWidth
                error={!!errors.name}
                helperText={errors.name}
              />
            </PropertyGroup>

            <PropertyGroup>
              <Select
                label="Component Type"
                value={componentType}
                onChange={(e) => setComponentType(e.target.value)}
                options={componentTypes}
                fullWidth
              />
            </PropertyGroup>

            <PropertyGroup>
              <Input
                label="Component Props (JSON)"
                value={componentProps}
                onChange={(e) => setComponentProps(e.target.value)}
                placeholder='{"text": "Hello", "color": "blue"}'
                fullWidth
                error={!!errors.props}
                helperText={errors.props}
                as="textarea"
                rows={5}
                style={{ fontFamily: theme.typography.fontFamily.code }}
              />
            </PropertyGroup>
          </PropertyEditor>
        </Card.Content>
        <Card.Footer>
          {editMode ? (
            <Button
              variant="primary"
              onClick={handleUpdateComponent}
              startIcon={<SaveOutlined />}
            >
              Update Component
            </Button>
          ) : (
            <Button
              variant="primary"
              onClick={handleAddComponent}
              startIcon={<PlusOutlined />}
            >
              Add Component
            </Button>
          )}
        </Card.Footer>
      </Card>

      <Card>
        <Card.Header>
          <Card.Title>Component Library</Card.Title>
        </Card.Header>
        <Card.Content>
          {components.length === 0 ? (
            <EmptyState>
              <div style={{ fontSize: '48px', color: theme.colors.neutral[400], marginBottom: theme.spacing[4] }}>
                <AppstoreOutlined />
              </div>
              <h3>No Components Yet</h3>
              <p>Create your first component to get started</p>
            </EmptyState>
          ) : (
            <ComponentGrid>
              {components.map(component => (
                <ComponentItem
                  key={component.id}
                  isSelected={selectedComponent && selectedComponent.id === component.id}
                >
                  <Card elevation="sm">
                    <Card.Header>
                      <div>
                        <div style={{ fontWeight: theme.typography.fontWeight.semibold }}>{component.name}</div>
                        <div style={{ fontSize: theme.typography.fontSize.sm, color: theme.colors.neutral[500] }}>
                          {component.type}
                        </div>
                      </div>
                      <div style={{ display: 'flex', gap: theme.spacing[1] }}>
                        <Button
                          variant="text"
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDuplicateComponent(component);
                          }}
                        >
                          <CopyOutlined />
                        </Button>
                        <Button
                          variant="text"
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSelectComponent(component);
                          }}
                        >
                          <EditOutlined />
                        </Button>
                        <Button
                          variant="text"
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveComponent(component.id);
                          }}
                        >
                          <DeleteOutlined />
                        </Button>
                      </div>
                    </Card.Header>
                    <Card.Content onClick={() => handleSelectComponent(component)}>
                      <ComponentPreview>
                        <pre style={{ margin: 0, overflow: 'auto', maxHeight: '100px' }}>
                          {JSON.stringify(component.props, null, 2)}
                        </pre>
                      </ComponentPreview>
                    </Card.Content>
                  </Card>
                </ComponentItem>
              ))}
            </ComponentGrid>
          )}
        </Card.Content>
      </Card>

      {selectedComponent && (
        <Card>
          <Card.Header>
            <Card.Title>Component Properties</Card.Title>
          </Card.Header>
          <Card.Content>
            <EnhancedComponentProperties
              component={selectedComponent}
              onUpdate={(updatedComponent) => {
                dispatch(updateComponent(updatedComponent));
              }}
            />
          </Card.Content>
        </Card>
      )}
    </ComponentBuilderContainer>
  );
};

export default ComponentBuilder;





