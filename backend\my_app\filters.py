"""
Filtering utilities for the API.
"""
import logging
import django_filters
from django.db.models import Q
from .models import App, ComponentTemplate, LayoutTemplate, AppTemplate

# Set up logger
logger = logging.getLogger(__name__)

class AppFilter(django_filters.FilterSet):
    """
    Filter for App model.
    
    This filter allows filtering apps by name, description, and creation date.
    """
    name = django_filters.CharFilter(lookup_expr='icontains')
    description = django_filters.CharFilter(lookup_expr='icontains')
    created_after = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='gte')
    created_before = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='lte')
    updated_after = django_filters.DateTimeFilter(field_name='updated_at', lookup_expr='gte')
    updated_before = django_filters.DateTimeFilter(field_name='updated_at', lookup_expr='lte')
    
    class Meta:
        model = App
        fields = ['name', 'description', 'is_public', 'user']

class ComponentTemplateFilter(django_filters.FilterSet):
    """
    Filter for ComponentTemplate model.
    
    This filter allows filtering component templates by name, description, component type, and creation date.
    """
    name = django_filters.CharFilter(lookup_expr='icontains')
    description = django_filters.CharFilter(lookup_expr='icontains')
    component_type = django_filters.CharFilter(lookup_expr='iexact')
    created_after = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='gte')
    created_before = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='lte')
    
    class Meta:
        model = ComponentTemplate
        fields = ['name', 'description', 'component_type', 'is_public', 'user']


class LayoutTemplateFilter(django_filters.FilterSet):
    """
    Filter for LayoutTemplate model.

    This filter allows filtering layout templates by name, description, layout type, and creation date.
    """
    name = django_filters.CharFilter(lookup_expr='icontains')
    description = django_filters.CharFilter(lookup_expr='icontains')
    layout_type = django_filters.CharFilter(lookup_expr='iexact')
    created_after = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='gte')
    created_before = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='lte')

    class Meta:
        model = LayoutTemplate
        fields = ['name', 'description', 'layout_type', 'is_public', 'user']


class AppTemplateFilter(django_filters.FilterSet):
    """
    Filter for AppTemplate model.

    This filter allows filtering app templates by name, description, app category, and creation date.
    """
    name = django_filters.CharFilter(lookup_expr='icontains')
    description = django_filters.CharFilter(lookup_expr='icontains')
    app_category = django_filters.CharFilter(lookup_expr='iexact')
    created_after = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='gte')
    created_before = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='lte')

    class Meta:
        model = AppTemplate
        fields = ['name', 'description', 'app_category', 'is_public', 'user']
