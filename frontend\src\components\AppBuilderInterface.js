import React, { useState, useEffect } from 'react';
import { Layout, <PERSON>u, <PERSON><PERSON>, Card, Typography, Tabs, Input, Select, Switch, Collapse, message, Spin, Tooltip, Divider } from 'antd';
import {
  AppstoreOutlined,
  LayoutOutlined,
  FormOutlined,
  DatabaseOutlined,
  ApiOutlined,
  SettingOutlined,
  SaveOutlined,
  PlayCircleOutlined,
  CodeOutlined,
  PlusOutlined,
  DeleteOutlined,
  CopyOutlined,
  EyeOutlined,
  RocketOutlined,
  BulbOutlined
} from '@ant-design/icons';
import ComponentPalette from './builder/ComponentPalette';
import PreviewArea from './builder/PreviewArea';
import { EnhancedComponentProperties } from './enhanced/property-editor';
import DataSourcePanel from './builder/DataSourcePanel';
import CodePanel from './builder/CodePanel';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Option } = Select;

const AppBuilderInterface = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [currentApp, setCurrentApp] = useState(null);
  const [loading, setLoading] = useState(false);
  const [components, setComponents] = useState([]);
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [previewMode, setPreviewMode] = useState(false);
  const [appName, setAppName] = useState('My New App');
  const [dataSources, setDataSources] = useState([]);
  const [activeTab, setActiveTab] = useState('components');

  // Mock data for demonstration
  const mockApps = [
    { id: 1, name: 'Customer Portal', description: 'Customer management portal' },
    { id: 2, name: 'Inventory System', description: 'Inventory tracking system' },
    { id: 3, name: 'Analytics Dashboard', description: 'Business analytics dashboard' }
  ];

  useEffect(() => {
    // Simulate loading an app
    setLoading(true);
    setTimeout(() => {
      setCurrentApp(mockApps[0]);
      setComponents([
        { id: 'header-1', type: 'header', props: { title: 'Customer Portal', subtitle: 'Manage your customers' } },
        { id: 'table-1', type: 'table', props: { columns: ['Name', 'Email', 'Status'], dataSource: 'customers' } },
        { id: 'form-1', type: 'form', props: { fields: ['name', 'email', 'phone'], submitAction: 'createCustomer' } }
      ]);
      setDataSources([
        { id: 'customers', name: 'Customers', type: 'api', endpoint: '/api/customers' },
        { id: 'products', name: 'Products', type: 'api', endpoint: '/api/products' }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const handleComponentSelect = (component) => {
    setSelectedComponent(component);
  };

  const handleAddComponent = (componentType) => {
    const newComponent = {
      id: `${componentType}-${Date.now()}`,
      type: componentType,
      props: getDefaultPropsForType(componentType)
    };

    setComponents([...components, newComponent]);
    setSelectedComponent(newComponent);
    message.success(`Added ${componentType} component`);
  };

  const handleUpdateComponent = (id, updatedProps) => {
    const updatedComponents = components.map(comp =>
      comp.id === id ? { ...comp, props: { ...comp.props, ...updatedProps } } : comp
    );

    setComponents(updatedComponents);

    // Update selected component if it's the one being edited
    if (selectedComponent && selectedComponent.id === id) {
      setSelectedComponent({ ...selectedComponent, props: { ...selectedComponent.props, ...updatedProps } });
    }

    message.success('Component updated');
  };

  const handleDeleteComponent = (id) => {
    const updatedComponents = components.filter(comp => comp.id !== id);
    setComponents(updatedComponents);

    if (selectedComponent && selectedComponent.id === id) {
      setSelectedComponent(null);
    }

    message.success('Component deleted');
  };

  const handleSaveApp = () => {
    setLoading(true);

    // Simulate saving to backend
    setTimeout(() => {
      setLoading(false);
      message.success('Application saved successfully');
    }, 1000);
  };

  const handlePreviewToggle = () => {
    setPreviewMode(!previewMode);
  };

  const handleAddDataSource = () => {
    const newDataSource = {
      id: `datasource-${Date.now()}`,
      name: 'New Data Source',
      type: 'api',
      endpoint: '/api/data'
    };

    setDataSources([...dataSources, newDataSource]);
    message.success('Data source added');
  };

  // Helper function to get default props for a component type
  const getDefaultPropsForType = (type) => {
    switch (type) {
      case 'header':
        return { title: 'New Header', subtitle: 'Subtitle text' };
      case 'table':
        return { columns: ['Column 1', 'Column 2'], dataSource: '' };
      case 'form':
        return { fields: ['field1', 'field2'], submitAction: '' };
      case 'chart':
        return { type: 'bar', dataSource: '', xField: '', yField: '' };
      case 'card':
        return { title: 'Card Title', content: 'Card content goes here' };
      case 'button':
        return { text: 'Button', action: '', type: 'primary' };
      default:
        return {};
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider
        width={250}
        collapsible
        collapsed={collapsed}
        onCollapse={setCollapsed}
        theme="light"
        style={{ boxShadow: '2px 0 8px rgba(0,0,0,0.1)' }}
      >
        <div style={{ padding: '16px', textAlign: 'center' }}>
          <Title level={4} style={{ margin: '0 0 8px 0' }}>
            {collapsed ? 'AB' : 'App Builder'}
          </Title>
          {!collapsed && (
            <Text type="secondary">Build your app with ease</Text>
          )}
        </div>

        <Menu
          mode="inline"
          defaultSelectedKeys={['components']}
          selectedKeys={[activeTab]}
          onClick={({ key }) => setActiveTab(key)}
        >
          <Menu.Item key="components" icon={<AppstoreOutlined />}>
            Components
          </Menu.Item>
          <Menu.Item key="layout" icon={<LayoutOutlined />}>
            Layout
          </Menu.Item>
          <Menu.Item key="data" icon={<DatabaseOutlined />}>
            Data Sources
          </Menu.Item>
          <Menu.Item key="api" icon={<ApiOutlined />}>
            API Integration
          </Menu.Item>
          <Menu.Item key="code" icon={<CodeOutlined />}>
            Custom Code
          </Menu.Item>
          <Menu.Item key="settings" icon={<SettingOutlined />}>
            Settings
          </Menu.Item>
        </Menu>

        <div style={{ padding: '16px', position: 'absolute', bottom: 0, width: '100%' }}>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSaveApp}
            loading={loading}
            block
          >
            {collapsed ? '' : 'Save App'}
          </Button>

          <Button
            style={{ marginTop: '8px' }}
            icon={<PlayCircleOutlined />}
            onClick={handlePreviewToggle}
            type={previewMode ? 'default' : 'text'}
            block
          >
            {collapsed ? '' : (previewMode ? 'Exit Preview' : 'Preview')}
          </Button>
        </div>
      </Sider>

      <Layout>
        <Header style={{
          background: '#fff',
          padding: '0 16px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Input
              value={appName}
              onChange={(e) => setAppName(e.target.value)}
              bordered={false}
              style={{ fontSize: '18px', fontWeight: 'bold', width: '300px' }}
            />
            <Tooltip title="App Settings">
              <Button type="text" icon={<SettingOutlined />} />
            </Tooltip>
          </div>

          <div>
            <Button
              type="primary"
              icon={<RocketOutlined />}
              style={{ marginRight: '8px' }}
            >
              Deploy
            </Button>

            <Select
              defaultValue="1"
              style={{ width: 200 }}
              onChange={(value) => {
                const app = mockApps.find(a => a.id.toString() === value);
                setCurrentApp(app);
              }}
            >
              {mockApps.map(app => (
                <Option key={app.id} value={app.id.toString()}>{app.name}</Option>
              ))}
            </Select>
          </div>
        </Header>

        <Content style={{ margin: '16px', display: 'flex', flexDirection: 'column' }}>
          {loading ? (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <Spin size="large" tip="Loading App Builder..." />
            </div>
          ) : (
            <div style={{ display: 'flex', height: '100%' }}>
              {/* Left panel - Component palette or data sources depending on active tab */}
              {!previewMode && (
                <Card style={{ width: 250, marginRight: '16px', height: '100%', overflow: 'auto' }}>
                  {activeTab === 'components' && (
                    <ComponentPalette onAddComponent={handleAddComponent} />
                  )}

                  {activeTab === 'data' && (
                    <DataSourcePanel
                      dataSources={dataSources}
                      onAddDataSource={handleAddDataSource}
                    />
                  )}

                  {activeTab === 'code' && (
                    <CodePanel />
                  )}

                  {activeTab === 'layout' && (
                    <div>
                      <Title level={5}>Layout Options</Title>
                      <Collapse defaultActiveKey={['1']}>
                        <Panel header="Page Layout" key="1">
                          <div style={{ marginBottom: '8px' }}>
                            <Text>Page Width</Text>
                            <Select defaultValue="responsive" style={{ width: '100%', marginTop: '4px' }}>
                              <Option value="responsive">Responsive</Option>
                              <Option value="fixed">Fixed Width</Option>
                              <Option value="fullWidth">Full Width</Option>
                            </Select>
                          </div>

                          <div style={{ marginBottom: '8px' }}>
                            <Text>Header Position</Text>
                            <Select defaultValue="top" style={{ width: '100%', marginTop: '4px' }}>
                              <Option value="top">Top</Option>
                              <Option value="sticky">Sticky Top</Option>
                              <Option value="none">No Header</Option>
                            </Select>
                          </div>

                          <div>
                            <Text>Show Footer</Text>
                            <div>
                              <Switch defaultChecked />
                            </div>
                          </div>
                        </Panel>

                        <Panel header="Grid Settings" key="2">
                          <div style={{ marginBottom: '8px' }}>
                            <Text>Grid Columns</Text>
                            <Select defaultValue="12" style={{ width: '100%', marginTop: '4px' }}>
                              <Option value="12">12 Columns</Option>
                              <Option value="16">16 Columns</Option>
                              <Option value="24">24 Columns</Option>
                            </Select>
                          </div>

                          <div>
                            <Text>Grid Gutter</Text>
                            <Select defaultValue="16" style={{ width: '100%', marginTop: '4px' }}>
                              <Option value="8">Small (8px)</Option>
                              <Option value="16">Medium (16px)</Option>
                              <Option value="24">Large (24px)</Option>
                            </Select>
                          </div>
                        </Panel>
                      </Collapse>
                    </div>
                  )}
                </Card>
              )}

              {/* Center panel - Preview area */}
              <Card
                style={{
                  flex: 1,
                  height: '100%',
                  overflow: 'auto',
                  padding: previewMode ? '0' : '16px'
                }}
                bodyStyle={{
                  height: '100%',
                  padding: previewMode ? '0' : '16px',
                  background: previewMode ? '#fff' : '#f5f5f5'
                }}
              >
                <PreviewArea
                  components={components}
                  onSelectComponent={handleComponentSelect}
                  onDeleteComponent={handleDeleteComponent}
                  previewMode={previewMode}
                  selectedComponentId={selectedComponent?.id}
                />
              </Card>

              {/* Right panel - Properties panel */}
              {!previewMode && selectedComponent && (
                <Card style={{ width: 300, marginLeft: '16px', height: '100%', overflow: 'auto' }}>
                  <EnhancedComponentProperties
                    component={selectedComponent}
                    onUpdate={handleUpdateComponent}
                  />
                </Card>
              )}
            </div>
          )}
        </Content>
      </Layout>
    </Layout>
  );
};

export default AppBuilderInterface;
