/**
 * Integration tests for template system functionality
 */

import axios from 'axios';

// Mock axios for testing
jest.mock('axios');
const mockedAxios = axios;

// Test data
const mockComponentTemplates = [
  {
    id: 1,
    name: 'Primary Button',
    description: 'A primary action button',
    component_type: 'button',
    default_props: '{"type": "primary", "size": "medium"}',
    is_public: true,
    created_at: '2025-06-20T00:00:00Z'
  },
  {
    id: 2,
    name: 'Search Input',
    description: 'An input field with search icon',
    component_type: 'input',
    default_props: '{"placeholder": "Search...", "prefix": "search"}',
    is_public: true,
    created_at: '2025-06-20T00:00:00Z'
  }
];

const mockLayoutTemplates = [
  {
    id: 1,
    name: 'Dashboard Grid',
    description: 'A grid layout for dashboards',
    layout_type: 'grid',
    components: '{"columns": 12, "gutter": [16, 16]}',
    default_props: '{"responsive": true}',
    is_public: true,
    created_at: '2025-06-20T00:00:00Z'
  }
];

const mockAppTemplates = [
  {
    id: 1,
    name: 'E-commerce Store',
    description: 'A complete e-commerce application',
    app_category: 'ecommerce',
    components: '{"pages": ["home", "products", "cart"]}',
    default_props: '{"theme": "modern"}',
    required_components: '["product-card", "cart", "navigation"]',
    preview_image: 'https://example.com/preview.jpg',
    is_public: true,
    created_at: '2025-06-20T00:00:00Z'
  }
];

const mockCategories = {
  components: [
    { value: 'button', label: 'Button', count: 2 },
    { value: 'input', label: 'Input', count: 1 }
  ],
  layouts: [
    { value: 'grid', label: 'Grid', count: 1 },
    { value: 'flex', label: 'Flex', count: 1 }
  ],
  apps: [
    { value: 'ecommerce', label: 'E-commerce', count: 1 },
    { value: 'business', label: 'Business Apps', count: 1 }
  ]
};

describe('Template System Integration Tests', () => {
  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
  });

  describe('API Integration', () => {
    test('should fetch template categories successfully', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        data: mockCategories
      });

      const response = await axios.get('/api/template-categories/');
      
      expect(response.data).toEqual(mockCategories);
      expect(response.data.components).toHaveLength(2);
      expect(response.data.layouts).toHaveLength(2);
      expect(response.data.apps).toHaveLength(2);
    });

    test('should fetch component templates successfully', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        data: { results: mockComponentTemplates }
      });

      const response = await axios.get('/api/component-templates/');
      
      expect(response.data.results).toEqual(mockComponentTemplates);
      expect(response.data.results).toHaveLength(2);
      expect(response.data.results[0].component_type).toBe('button');
    });

    test('should fetch layout templates successfully', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        data: { results: mockLayoutTemplates }
      });

      const response = await axios.get('/api/layout-templates/');
      
      expect(response.data.results).toEqual(mockLayoutTemplates);
      expect(response.data.results).toHaveLength(1);
      expect(response.data.results[0].layout_type).toBe('grid');
    });

    test('should fetch app templates successfully', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        data: { results: mockAppTemplates }
      });

      const response = await axios.get('/api/app-templates/');
      
      expect(response.data.results).toEqual(mockAppTemplates);
      expect(response.data.results).toHaveLength(1);
      expect(response.data.results[0].app_category).toBe('ecommerce');
    });

    test('should create a new layout template', async () => {
      const newTemplate = {
        name: 'New Layout',
        description: 'A new layout template',
        layout_type: 'flex',
        components: JSON.stringify({ direction: 'column' }),
        default_props: JSON.stringify({ gap: 16 }),
        is_public: false
      };

      const createdTemplate = {
        id: 2,
        ...newTemplate,
        created_at: '2025-06-20T00:00:00Z'
      };

      mockedAxios.post.mockResolvedValueOnce({
        data: createdTemplate
      });

      const response = await axios.post('/api/layout-templates/', newTemplate);
      
      expect(response.data).toEqual(createdTemplate);
      expect(response.data.name).toBe('New Layout');
      expect(response.data.layout_type).toBe('flex');
    });

    test('should export a template', async () => {
      const exportData = {
        name: 'Dashboard Grid',
        layout_type: 'grid',
        components: { columns: 12, gutter: [16, 16] },
        default_props: { responsive: true },
        is_public: true,
        created_at: '2025-06-20T00:00:00Z',
        export_version: '1.0'
      };

      mockedAxios.get.mockResolvedValueOnce({
        data: exportData
      });

      const response = await axios.get('/api/layout-templates/1/export_template/');
      
      expect(response.data).toEqual(exportData);
      expect(response.data.export_version).toBe('1.0');
    });

    test('should import a template', async () => {
      const importData = {
        template_data: {
          name: 'Imported Layout',
          description: 'Imported from JSON',
          layout_type: 'sidebar',
          components: { width: '250px' },
          default_props: { collapsible: true },
          is_public: false
        }
      };

      const importedTemplate = {
        id: 3,
        ...importData.template_data,
        created_at: '2025-06-20T00:00:00Z'
      };

      mockedAxios.post.mockResolvedValueOnce({
        data: importedTemplate
      });

      const response = await axios.post('/api/layout-templates/import_template/', importData);
      
      expect(response.data.name).toBe('Imported Layout');
      expect(response.data.layout_type).toBe('sidebar');
    });

    test('should search templates', async () => {
      const searchResults = {
        components: [mockComponentTemplates[0]],
        layouts: [mockLayoutTemplates[0]],
        apps: []
      };

      mockedAxios.get.mockResolvedValueOnce({
        data: searchResults
      });

      const response = await axios.get('/api/template-search/?q=button');
      
      expect(response.data).toEqual(searchResults);
      expect(response.data.components).toHaveLength(1);
      expect(response.data.components[0].name).toBe('Primary Button');
    });

    test('should clone a template', async () => {
      const cloneData = {
        template_id: 1,
        template_type: 'layout',
        new_name: 'Cloned Layout'
      };

      const clonedTemplate = {
        id: 4,
        name: 'Cloned Layout',
        description: 'A grid layout for dashboards',
        layout_type: 'grid',
        components: '{"columns": 12, "gutter": [16, 16]}',
        default_props: '{"responsive": true}',
        is_public: false,
        created_at: '2025-06-20T00:00:00Z'
      };

      mockedAxios.post.mockResolvedValueOnce({
        data: {
          message: 'Template cloned successfully',
          template: clonedTemplate
        }
      });

      const response = await axios.post('/api/clone-template/', cloneData);
      
      expect(response.data.message).toBe('Template cloned successfully');
      expect(response.data.template.name).toBe('Cloned Layout');
      expect(response.data.template.is_public).toBe(false);
    });
  });

  describe('Data Processing', () => {
    test('should parse JSON fields correctly', () => {
      const template = mockComponentTemplates[0];
      const defaultProps = JSON.parse(template.default_props);
      
      expect(defaultProps).toEqual({
        type: 'primary',
        size: 'medium'
      });
    });

    test('should handle empty JSON fields', () => {
      const template = {
        ...mockLayoutTemplates[0],
        components: '{}',
        default_props: '{}'
      };
      
      const components = JSON.parse(template.components);
      const defaultProps = JSON.parse(template.default_props);
      
      expect(components).toEqual({});
      expect(defaultProps).toEqual({});
    });

    test('should validate required fields', () => {
      const template = mockAppTemplates[0];
      
      expect(template.name).toBeDefined();
      expect(template.app_category).toBeDefined();
      expect(template.components).toBeDefined();
      expect(template.created_at).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    test('should handle API errors gracefully', async () => {
      const errorResponse = {
        response: {
          status: 404,
          data: { error: 'Template not found' }
        }
      };

      mockedAxios.get.mockRejectedValueOnce(errorResponse);

      try {
        await axios.get('/api/layout-templates/999/');
      } catch (error) {
        expect(error.response.status).toBe(404);
        expect(error.response.data.error).toBe('Template not found');
      }
    });

    test('should handle authentication errors', async () => {
      const authError = {
        response: {
          status: 401,
          data: { error: 'Authentication required' }
        }
      };

      mockedAxios.post.mockRejectedValueOnce(authError);

      try {
        await axios.post('/api/layout-templates/', {});
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data.error).toBe('Authentication required');
      }
    });

    test('should handle invalid JSON data', () => {
      const invalidTemplate = {
        ...mockComponentTemplates[0],
        default_props: 'invalid json'
      };

      expect(() => {
        JSON.parse(invalidTemplate.default_props);
      }).toThrow();
    });
  });
});

// Integration test helper functions
export const testHelpers = {
  /**
   * Create a mock template for testing
   */
  createMockTemplate: (type = 'component', overrides = {}) => {
    const baseTemplate = {
      id: Math.floor(Math.random() * 1000),
      name: 'Test Template',
      description: 'A test template',
      is_public: true,
      created_at: new Date().toISOString(),
      ...overrides
    };

    switch (type) {
      case 'component':
        return {
          ...baseTemplate,
          component_type: 'button',
          default_props: '{"type": "primary"}',
          ...overrides
        };
      case 'layout':
        return {
          ...baseTemplate,
          layout_type: 'grid',
          components: '{"columns": 12}',
          default_props: '{"responsive": true}',
          ...overrides
        };
      case 'app':
        return {
          ...baseTemplate,
          app_category: 'business',
          components: '{"pages": ["home"]}',
          default_props: '{"theme": "modern"}',
          required_components: '["header"]',
          preview_image: '',
          ...overrides
        };
      default:
        return baseTemplate;
    }
  },

  /**
   * Simulate API response delay
   */
  delay: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),

  /**
   * Validate template structure
   */
  validateTemplate: (template, type) => {
    const requiredFields = ['id', 'name', 'created_at'];
    
    for (const field of requiredFields) {
      if (!template[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    switch (type) {
      case 'component':
        if (!template.component_type) {
          throw new Error('Missing component_type for component template');
        }
        break;
      case 'layout':
        if (!template.layout_type) {
          throw new Error('Missing layout_type for layout template');
        }
        break;
      case 'app':
        if (!template.app_category) {
          throw new Error('Missing app_category for app template');
        }
        break;
    }

    return true;
  }
};
