import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Typography,
  Input,
  Select,
  List,
  Tag,
  Space,
  Modal,
  Form,
  Switch,
  Tooltip,
  message,
  Empty,
  Spin,
  Tabs,
  Badge,
  Upload,
  Image
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  EyeOutlined,
  LockOutlined,
  AppstoreOutlined,
  UserOutlined,
  GlobalOutlined,
  LayoutOutlined,
  MobileOutlined,
  DownloadOutlined,
  UploadOutlined,
  StarOutlined,
  BulbOutlined
} from '@ant-design/icons';
import axios from 'axios';
import MainLayout from '../components/layout/MainLayout';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

/**
 * TemplatesPage component
 * Displays and manages component templates
 */
const TemplatesPage = () => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [componentType, setComponentType] = useState('');
  const [visibility, setVisibility] = useState('all');
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentTemplate, setCurrentTemplate] = useState(null);
  const [form] = Form.useForm();
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('my');

  // Fetch templates when component mounts
  useEffect(() => {
    fetchTemplates();
  }, [activeTab]);

  // Fetch templates from API
  const fetchTemplates = async () => {
    setLoading(true);
    try {
      let url = '/api/component-templates/';

      // Add filters
      const params = new URLSearchParams();
      if (activeTab === 'my') {
        params.append('user', 'current');
      } else if (activeTab === 'public') {
        params.append('is_public', 'true');
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await axios.get(url);
      setTemplates(response.data);
    } catch (error) {
      console.error('Error fetching templates:', error);
      message.error('Failed to load templates');
    } finally {
      setLoading(false);
    }
  };

  // Create a new template
  const createTemplate = async (values) => {
    setSaving(true);
    try {
      // Convert props to JSON string
      const data = {
        ...values,
        default_props: JSON.stringify(values.default_props || {})
      };

      await axios.post('/api/component-templates/', data);

      message.success('Template created successfully');
      setCreateModalVisible(false);
      form.resetFields();

      // Refresh templates
      fetchTemplates();
    } catch (error) {
      console.error('Error creating template:', error);
      message.error('Failed to create template');
    } finally {
      setSaving(false);
    }
  };

  // Update a template
  const updateTemplate = async (values) => {
    setSaving(true);
    try {
      // Convert props to JSON string if it's an object
      const data = {
        ...values,
        default_props: typeof values.default_props === 'object'
          ? JSON.stringify(values.default_props)
          : values.default_props
      };

      await axios.patch(`/api/component-templates/${currentTemplate.id}/`, data);

      message.success('Template updated successfully');
      setEditModalVisible(false);

      // Refresh templates
      fetchTemplates();
    } catch (error) {
      console.error('Error updating template:', error);
      message.error('Failed to update template');
    } finally {
      setSaving(false);
    }
  };

  // Delete a template
  const deleteTemplate = async (template) => {
    try {
      await axios.delete(`/api/component-templates/${template.id}/`);

      message.success('Template deleted successfully');

      // Refresh templates
      fetchTemplates();
    } catch (error) {
      console.error('Error deleting template:', error);
      message.error('Failed to delete template');
    }
  };

  // Edit a template
  const editTemplate = (template) => {
    setCurrentTemplate(template);

    // Parse default_props if it's a string
    const defaultProps = typeof template.default_props === 'string'
      ? JSON.parse(template.default_props)
      : template.default_props;

    form.setFieldsValue({
      name: template.name,
      description: template.description,
      component_type: template.component_type,
      default_props: defaultProps,
      is_public: template.is_public
    });

    setEditModalVisible(true);
  };

  // Filter templates
  const filteredTemplates = templates.filter(template => {
    // Filter by search text
    const matchesSearch = searchText === '' ||
      template.name.toLowerCase().includes(searchText.toLowerCase()) ||
      template.description.toLowerCase().includes(searchText.toLowerCase());

    // Filter by component type
    const matchesType = componentType === '' ||
      template.component_type === componentType;

    // Filter by visibility
    const matchesVisibility = visibility === 'all' ||
      (visibility === 'public' && template.is_public) ||
      (visibility === 'private' && !template.is_public);

    return matchesSearch && matchesType && matchesVisibility;
  });

  // Get unique component types
  const componentTypes = [...new Set(templates.map(t => t.component_type))];

  // Render template item
  const renderTemplateItem = (template) => {
    return (
      <List.Item>
        <Card
          title={
            <Space>
              <Text strong>{template.name}</Text>
              {template.is_public ? (
                <Tag icon={<EyeOutlined />} color="green">Public</Tag>
              ) : (
                <Tag icon={<LockOutlined />} color="default">Private</Tag>
              )}
            </Space>
          }
          extra={
            <Space>
              <Tooltip title="Use Template">
                <Button icon={<CopyOutlined />} />
              </Tooltip>
              <Tooltip title="Edit Template">
                <Button
                  icon={<EditOutlined />}
                  onClick={() => editTemplate(template)}
                />
              </Tooltip>
              <Tooltip title="Delete Template">
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => Modal.confirm({
                    title: 'Delete Template',
                    content: `Are you sure you want to delete "${template.name}"?`,
                    okText: 'Delete',
                    okType: 'danger',
                    onOk: () => deleteTemplate(template)
                  })}
                />
              </Tooltip>
            </Space>
          }
          style={{ width: '100%' }}
        >
          <Paragraph ellipsis={{ rows: 2 }}>
            {template.description || 'No description provided'}
          </Paragraph>
          <Space>
            <Tag color="blue">{template.component_type}</Tag>
            <Text type="secondary">
              Created by: {template.user ? template.user.username : 'Anonymous'}
            </Text>
            <Text type="secondary">
              Created: {new Date(template.created_at).toLocaleDateString()}
            </Text>
          </Space>
        </Card>
      </List.Item>
    );
  };

  // Render template form
  const renderTemplateForm = () => {
    return (
      <Form
        form={form}
        layout="vertical"
        onFinish={currentTemplate ? updateTemplate : createTemplate}
      >
        <Form.Item
          name="name"
          label="Template Name"
          rules={[{ required: true, message: 'Please enter template name' }]}
        >
          <Input placeholder="Enter template name" />
        </Form.Item>

        <Form.Item
          name="description"
          label="Description"
        >
          <TextArea
            placeholder="Enter template description"
            rows={4}
            maxLength={500}
            showCount
          />
        </Form.Item>

        <Form.Item
          name="component_type"
          label="Component Type"
          rules={[{ required: true, message: 'Please select component type' }]}
        >
          <Select placeholder="Select component type">
            <Option value="button">Button</Option>
            <Option value="input">Input</Option>
            <Option value="select">Select</Option>
            <Option value="checkbox">Checkbox</Option>
            <Option value="radio">Radio</Option>
            <Option value="switch">Switch</Option>
            <Option value="slider">Slider</Option>
            <Option value="date-picker">Date Picker</Option>
            <Option value="time-picker">Time Picker</Option>
            <Option value="upload">Upload</Option>
            <Option value="form">Form</Option>
            <Option value="table">Table</Option>
            <Option value="list">List</Option>
            <Option value="card">Card</Option>
            <Option value="tabs">Tabs</Option>
            <Option value="modal">Modal</Option>
            <Option value="drawer">Drawer</Option>
            <Option value="menu">Menu</Option>
            <Option value="layout">Layout</Option>
            <Option value="custom">Custom</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="default_props"
          label="Default Properties"
          rules={[
            {
              validator: (_, value) => {
                try {
                  if (typeof value === 'string') {
                    JSON.parse(value);
                  }
                  return Promise.resolve();
                } catch (error) {
                  return Promise.reject('Please enter valid JSON');
                }
              }
            }
          ]}
        >
          <TextArea
            placeholder="Enter default properties in JSON format"
            rows={6}
            style={{ fontFamily: 'monospace' }}
          />
        </Form.Item>

        <Form.Item
          name="is_public"
          label="Visibility"
          valuePropName="checked"
        >
          <Switch
            checkedChildren={<EyeOutlined />}
            unCheckedChildren={<LockOutlined />}
          />
          <Text type="secondary" style={{ marginLeft: 8 }}>
            Make this template public
          </Text>
        </Form.Item>
      </Form>
    );
  };

  return (
    <MainLayout>
      <div className="templates-page">
        <div className="templates-header">
          <Title level={2}>
            <AppstoreOutlined /> Component Templates
          </Title>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              form.resetFields();
              setCreateModalVisible(true);
            }}
          >
            Create Template
          </Button>
        </div>

        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          className="templates-tabs"
        >
          <TabPane
            tab={<span><UserOutlined /> My Templates</span>}
            key="my"
          >
            <div className="templates-filters">
              <Input
                placeholder="Search templates"
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: 250 }}
              />

              <Select
                placeholder="Filter by component type"
                value={componentType}
                onChange={setComponentType}
                style={{ width: 200 }}
                allowClear
                suffixIcon={<FilterOutlined />}
              >
                {componentTypes.map(type => (
                  <Option key={type} value={type}>{type}</Option>
                ))}
              </Select>

              <Select
                placeholder="Filter by visibility"
                value={visibility}
                onChange={setVisibility}
                style={{ width: 150 }}
                suffixIcon={<FilterOutlined />}
              >
                <Option value="all">All</Option>
                <Option value="public">Public</Option>
                <Option value="private">Private</Option>
              </Select>
            </div>

            <div className="templates-list">
              <Spin spinning={loading}>
                {filteredTemplates.length > 0 ? (
                  <List
                    grid={{
                      gutter: 16,
                      xs: 1,
                      sm: 1,
                      md: 2,
                      lg: 2,
                      xl: 3,
                      xxl: 3
                    }}
                    dataSource={filteredTemplates}
                    renderItem={renderTemplateItem}
                    pagination={{
                      pageSize: 9,
                      hideOnSinglePage: true
                    }}
                  />
                ) : (
                  <Empty
                    description={
                      <span>
                        {loading ? 'Loading templates...' : 'No templates found'}
                      </span>
                    }
                  />
                )}
              </Spin>
            </div>
          </TabPane>

          <TabPane
            tab={<span><GlobalOutlined /> Public Templates</span>}
            key="public"
          >
            <div className="templates-filters">
              <Input
                placeholder="Search templates"
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: 250 }}
              />

              <Select
                placeholder="Filter by component type"
                value={componentType}
                onChange={setComponentType}
                style={{ width: 200 }}
                allowClear
                suffixIcon={<FilterOutlined />}
              >
                {componentTypes.map(type => (
                  <Option key={type} value={type}>{type}</Option>
                ))}
              </Select>
            </div>

            <div className="templates-list">
              <Spin spinning={loading}>
                {filteredTemplates.length > 0 ? (
                  <List
                    grid={{
                      gutter: 16,
                      xs: 1,
                      sm: 1,
                      md: 2,
                      lg: 2,
                      xl: 3,
                      xxl: 3
                    }}
                    dataSource={filteredTemplates}
                    renderItem={renderTemplateItem}
                    pagination={{
                      pageSize: 9,
                      hideOnSinglePage: true
                    }}
                  />
                ) : (
                  <Empty
                    description={
                      <span>
                        {loading ? 'Loading templates...' : 'No templates found'}
                      </span>
                    }
                  />
                )}
              </Spin>
            </div>
          </TabPane>
        </Tabs>

        {/* Create Template Modal */}
        <Modal
          title="Create Component Template"
          open={createModalVisible}
          onCancel={() => setCreateModalVisible(false)}
          onOk={() => form.submit()}
          okText="Create"
          confirmLoading={saving}
          width={600}
        >
          {renderTemplateForm()}
        </Modal>

        {/* Edit Template Modal */}
        <Modal
          title="Edit Component Template"
          open={editModalVisible}
          onCancel={() => setEditModalVisible(false)}
          onOk={() => form.submit()}
          okText="Save Changes"
          confirmLoading={saving}
          width={600}
        >
          {renderTemplateForm()}
        </Modal>
      </div>

      <style jsx="true">{`
        .templates-page {
          max-width: 1200px;
          margin: 0 auto;
        }
        
        .templates-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 24px;
        }
        
        .templates-tabs {
          background-color: #fff;
          padding: 16px;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }
        
        .templates-filters {
          display: flex;
          gap: 16px;
          margin-bottom: 24px;
          flex-wrap: wrap;
        }
        
        .templates-list {
          min-height: 400px;
        }
      `}</style>
    </MainLayout>
  );
};

export default TemplatesPage;
