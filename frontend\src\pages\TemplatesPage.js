import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Typography,
  Input,
  Select,
  List,
  Tag,
  Space,
  Modal,
  Form,
  Switch,
  Tooltip,
  message,
  Empty,
  Spin,
  Tabs,
  Badge,
  Upload,
  Image
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  EyeOutlined,
  LockOutlined,
  AppstoreOutlined,
  UserOutlined,
  GlobalOutlined,
  LayoutOutlined,
  MobileOutlined,
  DownloadOutlined,
  UploadOutlined,
  StarOutlined,
  BulbOutlined
} from '@ant-design/icons';
import axios from 'axios';
import MainLayout from '../components/layout/MainLayout';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

/**
 * TemplatesPage component
 * Displays and manages component, layout, and app templates
 */
const TemplatesPage = () => {
  // Template data states
  const [componentTemplates, setComponentTemplates] = useState([]);
  const [layoutTemplates, setLayoutTemplates] = useState([]);
  const [appTemplates, setAppTemplates] = useState([]);

  // UI states
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [filterType, setFilterType] = useState('');
  const [filterCategory, setFilterCategory] = useState('');
  const [visibility, setVisibility] = useState('all');
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [currentTemplate, setCurrentTemplate] = useState(null);
  const [form] = Form.useForm();
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('my');
  const [templateType, setTemplateType] = useState('components'); // components, layouts, apps

  // Categories for different template types
  const componentTypes = [
    'button', 'input', 'select', 'checkbox', 'radio', 'switch', 'slider',
    'date-picker', 'time-picker', 'upload', 'form', 'table', 'list', 'card',
    'tabs', 'modal', 'drawer', 'menu', 'layout', 'custom'
  ];

  const layoutTypes = [
    'grid', 'flex', 'sidebar', 'header-footer', 'dashboard', 'landing',
    'blog', 'portfolio', 'ecommerce', 'admin', 'custom'
  ];

  const appCategories = [
    { value: 'business', label: 'Business Apps' },
    { value: 'ecommerce', label: 'E-commerce' },
    { value: 'portfolio', label: 'Portfolio' },
    { value: 'dashboard', label: 'Dashboard' },
    { value: 'landing', label: 'Landing Page' },
    { value: 'blog', label: 'Blog' },
    { value: 'social', label: 'Social Media' },
    { value: 'education', label: 'Education' },
    { value: 'healthcare', label: 'Healthcare' },
    { value: 'finance', label: 'Finance' },
    { value: 'other', label: 'Other' }
  ];

  // Fetch templates when component mounts or tab changes
  useEffect(() => {
    fetchAllTemplates();
  }, [activeTab, templateType]);

  // Fetch all template types from API
  const fetchAllTemplates = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchComponentTemplates(),
        fetchLayoutTemplates(),
        fetchAppTemplates()
      ]);
    } catch (error) {
      console.error('Error fetching templates:', error);
      message.error('Failed to load templates');
    } finally {
      setLoading(false);
    }
  };

  // Fetch component templates
  const fetchComponentTemplates = async () => {
    try {
      let url = '/api/component-templates/';
      const params = new URLSearchParams();

      if (activeTab === 'my') {
        params.append('user', 'current');
      } else if (activeTab === 'public') {
        params.append('is_public', 'true');
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await axios.get(url);
      setComponentTemplates(response.data.results || response.data);
    } catch (error) {
      console.error('Error fetching component templates:', error);
    }
  };

  // Fetch layout templates
  const fetchLayoutTemplates = async () => {
    try {
      let url = '/api/layout-templates/';
      const params = new URLSearchParams();

      if (activeTab === 'my') {
        params.append('user', 'current');
      } else if (activeTab === 'public') {
        params.append('is_public', 'true');
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await axios.get(url);
      setLayoutTemplates(response.data.results || response.data);
    } catch (error) {
      console.error('Error fetching layout templates:', error);
    }
  };

  // Fetch app templates
  const fetchAppTemplates = async () => {
    try {
      let url = '/api/app-templates/';
      const params = new URLSearchParams();

      if (activeTab === 'my') {
        params.append('user', 'current');
      } else if (activeTab === 'public') {
        params.append('is_public', 'true');
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await axios.get(url);
      setAppTemplates(response.data.results || response.data);
    } catch (error) {
      console.error('Error fetching app templates:', error);
    }
  };

  // Create a new template
  const createTemplate = async (values) => {
    setSaving(true);
    try {
      let url, data;

      if (templateType === 'components') {
        url = '/api/component-templates/';
        data = {
          ...values,
          default_props: JSON.stringify(values.default_props || {})
        };
      } else if (templateType === 'layouts') {
        url = '/api/layout-templates/';
        data = {
          ...values,
          components: JSON.stringify(values.components || {}),
          default_props: JSON.stringify(values.default_props || {})
        };
      } else if (templateType === 'apps') {
        url = '/api/app-templates/';
        data = {
          ...values,
          components: JSON.stringify(values.components || {}),
          default_props: JSON.stringify(values.default_props || {}),
          required_components: JSON.stringify(values.required_components || [])
        };
      }

      await axios.post(url, data);

      message.success('Template created successfully');
      setCreateModalVisible(false);
      form.resetFields();

      // Refresh templates
      fetchAllTemplates();
    } catch (error) {
      console.error('Error creating template:', error);
      message.error('Failed to create template');
    } finally {
      setSaving(false);
    }
  };

  // Update a template
  const updateTemplate = async (values) => {
    setSaving(true);
    try {
      let url, data;

      if (currentTemplate.template_type === 'component') {
        url = `/api/component-templates/${currentTemplate.id}/`;
        data = {
          ...values,
          default_props: typeof values.default_props === 'object'
            ? JSON.stringify(values.default_props)
            : values.default_props
        };
      } else if (currentTemplate.template_type === 'layout') {
        url = `/api/layout-templates/${currentTemplate.id}/`;
        data = {
          ...values,
          components: typeof values.components === 'object'
            ? JSON.stringify(values.components)
            : values.components,
          default_props: typeof values.default_props === 'object'
            ? JSON.stringify(values.default_props)
            : values.default_props
        };
      } else if (currentTemplate.template_type === 'app') {
        url = `/api/app-templates/${currentTemplate.id}/`;
        data = {
          ...values,
          components: typeof values.components === 'object'
            ? JSON.stringify(values.components)
            : values.components,
          default_props: typeof values.default_props === 'object'
            ? JSON.stringify(values.default_props)
            : values.default_props,
          required_components: typeof values.required_components === 'object'
            ? JSON.stringify(values.required_components)
            : values.required_components
        };
      }

      await axios.patch(url, data);

      message.success('Template updated successfully');
      setEditModalVisible(false);

      // Refresh templates
      fetchAllTemplates();
    } catch (error) {
      console.error('Error updating template:', error);
      message.error('Failed to update template');
    } finally {
      setSaving(false);
    }
  };

  // Delete a template
  const deleteTemplate = async (template) => {
    try {
      let url;

      if (template.template_type === 'component') {
        url = `/api/component-templates/${template.id}/`;
      } else if (template.template_type === 'layout') {
        url = `/api/layout-templates/${template.id}/`;
      } else if (template.template_type === 'app') {
        url = `/api/app-templates/${template.id}/`;
      }

      await axios.delete(url);

      message.success('Template deleted successfully');

      // Refresh templates
      fetchAllTemplates();
    } catch (error) {
      console.error('Error deleting template:', error);
      message.error('Failed to delete template');
    }
  };

  // Edit a template
  const editTemplate = (template) => {
    setCurrentTemplate(template);

    // Parse JSON fields if they're strings
    const defaultProps = typeof template.default_props === 'string'
      ? JSON.parse(template.default_props)
      : template.default_props;

    const components = template.components && typeof template.components === 'string'
      ? JSON.parse(template.components)
      : template.components;

    const requiredComponents = template.required_components && typeof template.required_components === 'string'
      ? JSON.parse(template.required_components)
      : template.required_components;

    const formValues = {
      name: template.name,
      description: template.description,
      default_props: defaultProps,
      is_public: template.is_public
    };

    // Add type-specific fields
    if (template.template_type === 'component') {
      formValues.component_type = template.component_type;
    } else if (template.template_type === 'layout') {
      formValues.layout_type = template.layout_type;
      formValues.components = components;
    } else if (template.template_type === 'app') {
      formValues.app_category = template.app_category;
      formValues.components = components;
      formValues.required_components = requiredComponents;
      formValues.preview_image = template.preview_image;
    }

    form.setFieldsValue(formValues);
    setEditModalVisible(true);
  };

  // Get current templates based on active template type
  const getCurrentTemplates = () => {
    switch (templateType) {
      case 'layouts':
        return layoutTemplates.map(t => ({ ...t, template_type: 'layout' }));
      case 'apps':
        return appTemplates.map(t => ({ ...t, template_type: 'app' }));
      default:
        return componentTemplates.map(t => ({ ...t, template_type: 'component' }));
    }
  };

  // Get template type display name
  const getTemplateTypeDisplayName = () => {
    switch (templateType) {
      case 'layouts':
        return 'Layout Template';
      case 'apps':
        return 'App Template';
      default:
        return 'Component Template';
    }
  };

  // Filter templates
  const filteredTemplates = getCurrentTemplates().filter(template => {
    // Filter by search text
    const matchesSearch = searchText === '' ||
      template.name.toLowerCase().includes(searchText.toLowerCase()) ||
      template.description.toLowerCase().includes(searchText.toLowerCase());

    // Filter by type/category
    let matchesType = true;
    if (templateType === 'components' && filterType) {
      matchesType = template.component_type === filterType;
    } else if (templateType === 'layouts' && filterType) {
      matchesType = template.layout_type === filterType;
    } else if (templateType === 'apps' && filterCategory) {
      matchesType = template.app_category === filterCategory;
    }

    // Filter by visibility
    const matchesVisibility = visibility === 'all' ||
      (visibility === 'public' && template.is_public) ||
      (visibility === 'private' && !template.is_public);

    return matchesSearch && matchesType && matchesVisibility;
  });

  // Get unique types for current template type
  const getUniqueTypes = () => {
    const templates = getCurrentTemplates();
    switch (templateType) {
      case 'layouts':
        return [...new Set(templates.map(t => t.layout_type))];
      case 'apps':
        return [...new Set(templates.map(t => t.app_category))];
      default:
        return [...new Set(templates.map(t => t.component_type))];
    }
  };

  // Render template item
  const renderTemplateItem = (template) => {
    const getTemplateIcon = () => {
      switch (template.template_type) {
        case 'layout':
          return <LayoutOutlined />;
        case 'app':
          return <MobileOutlined />;
        default:
          return <AppstoreOutlined />;
      }
    };

    const getTemplateTypeTag = () => {
      switch (template.template_type) {
        case 'layout':
          return <Tag color="purple">{template.layout_type}</Tag>;
        case 'app':
          return <Tag color="orange">{template.app_category}</Tag>;
        default:
          return <Tag color="blue">{template.component_type}</Tag>;
      }
    };

    return (
      <List.Item>
        <Card
          title={
            <Space>
              {getTemplateIcon()}
              <Text strong>{template.name}</Text>
              {template.is_public ? (
                <Tag icon={<EyeOutlined />} color="green">Public</Tag>
              ) : (
                <Tag icon={<LockOutlined />} color="default">Private</Tag>
              )}
              {template.template_type === 'app' && template.preview_image && (
                <Tag icon={<StarOutlined />} color="gold">Featured</Tag>
              )}
            </Space>
          }
          extra={
            <Space>
              <Tooltip title="Use Template">
                <Button icon={<CopyOutlined />} />
              </Tooltip>
              <Tooltip title="Export Template">
                <Button icon={<DownloadOutlined />} onClick={() => exportTemplate(template)} />
              </Tooltip>
              <Tooltip title="Edit Template">
                <Button
                  icon={<EditOutlined />}
                  onClick={() => editTemplate(template)}
                />
              </Tooltip>
              <Tooltip title="Delete Template">
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => Modal.confirm({
                    title: 'Delete Template',
                    content: `Are you sure you want to delete "${template.name}"?`,
                    okText: 'Delete',
                    okType: 'danger',
                    onOk: () => deleteTemplate(template)
                  })}
                />
              </Tooltip>
            </Space>
          }
          style={{ width: '100%' }}
          cover={
            template.template_type === 'app' && template.preview_image ? (
              <Image
                alt={template.name}
                src={template.preview_image}
                height={120}
                style={{ objectFit: 'cover' }}
                fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
              />
            ) : null
          }
        >
          <Paragraph ellipsis={{ rows: 2 }}>
            {template.description || 'No description provided'}
          </Paragraph>
          <Space wrap>
            {getTemplateTypeTag()}
            {template.template_type === 'app' && template.required_components &&
              template.required_components.length > 0 && (
                <Tag color="cyan">
                  <BulbOutlined /> {template.required_components.length} dependencies
                </Tag>
              )}
            <Text type="secondary">
              Created by: {template.user ? template.user.username : 'Anonymous'}
            </Text>
            <Text type="secondary">
              Created: {new Date(template.created_at).toLocaleDateString()}
            </Text>
          </Space>
        </Card>
      </List.Item>
    );
  };

  // Export template
  const exportTemplate = async (template) => {
    try {
      let url;
      if (template.template_type === 'component') {
        url = `/api/component-templates/${template.id}/export_template/`;
      } else if (template.template_type === 'layout') {
        url = `/api/layout-templates/${template.id}/export_template/`;
      } else if (template.template_type === 'app') {
        url = `/api/app-templates/${template.id}/export_template/`;
      }

      const response = await axios.get(url);
      const dataStr = JSON.stringify(response.data, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });

      const link = document.createElement('a');
      link.href = URL.createObjectURL(dataBlob);
      link.download = `${template.name.replace(/\s+/g, '_')}_template.json`;
      link.click();

      message.success('Template exported successfully');
    } catch (error) {
      console.error('Error exporting template:', error);
      message.error('Failed to export template');
    }
  };

  // Render template form
  const renderTemplateForm = () => {
    return (
      <Form
        form={form}
        layout="vertical"
        onFinish={currentTemplate ? updateTemplate : createTemplate}
      >
        <Form.Item
          name="name"
          label="Template Name"
          rules={[{ required: true, message: 'Please enter template name' }]}
        >
          <Input placeholder="Enter template name" />
        </Form.Item>

        <Form.Item
          name="description"
          label="Description"
        >
          <TextArea
            placeholder="Enter template description"
            rows={4}
            maxLength={500}
            showCount
          />
        </Form.Item>

        {/* Component Type Field - only for component templates */}
        {templateType === 'components' && (
          <Form.Item
            name="component_type"
            label="Component Type"
            rules={[{ required: true, message: 'Please select component type' }]}
          >
            <Select placeholder="Select component type">
              {componentTypes.map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
          </Form.Item>
        )}

        {/* Layout Type Field - only for layout templates */}
        {templateType === 'layouts' && (
          <Form.Item
            name="layout_type"
            label="Layout Type"
            rules={[{ required: true, message: 'Please select layout type' }]}
          >
            <Select placeholder="Select layout type">
              {layoutTypes.map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
          </Form.Item>
        )}

        {/* App Category Field - only for app templates */}
        {templateType === 'apps' && (
          <Form.Item
            name="app_category"
            label="App Category"
            rules={[{ required: true, message: 'Please select app category' }]}
          >
            <Select placeholder="Select app category">
              {appCategories.map(category => (
                <Option key={category.value} value={category.value}>
                  {category.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        )}

        {/* Components Field - for layout and app templates */}
        {(templateType === 'layouts' || templateType === 'apps') && (
          <Form.Item
            name="components"
            label="Components Configuration"
            rules={[
              { required: true, message: 'Please enter components configuration' },
              {
                validator: (_, value) => {
                  try {
                    if (typeof value === 'string') {
                      JSON.parse(value);
                    }
                    return Promise.resolve();
                  } catch (error) {
                    return Promise.reject('Please enter valid JSON');
                  }
                }
              }
            ]}
          >
            <TextArea
              placeholder="Enter components configuration in JSON format"
              rows={8}
              style={{ fontFamily: 'monospace' }}
            />
          </Form.Item>
        )}

        {/* Required Components Field - only for app templates */}
        {templateType === 'apps' && (
          <Form.Item
            name="required_components"
            label="Required Components"
            rules={[
              {
                validator: (_, value) => {
                  try {
                    if (value && typeof value === 'string') {
                      const parsed = JSON.parse(value);
                      if (!Array.isArray(parsed)) {
                        return Promise.reject('Required components must be an array');
                      }
                    }
                    return Promise.resolve();
                  } catch (error) {
                    return Promise.reject('Please enter valid JSON array');
                  }
                }
              }
            ]}
          >
            <TextArea
              placeholder='Enter required components as JSON array, e.g., ["button", "input"]'
              rows={3}
              style={{ fontFamily: 'monospace' }}
            />
          </Form.Item>
        )}

        {/* Preview Image Field - only for app templates */}
        {templateType === 'apps' && (
          <Form.Item
            name="preview_image"
            label="Preview Image URL"
          >
            <Input placeholder="Enter preview image URL (optional)" />
          </Form.Item>
        )}

        <Form.Item
          name="default_props"
          label="Default Properties"
          rules={[
            {
              validator: (_, value) => {
                try {
                  if (value && typeof value === 'string') {
                    JSON.parse(value);
                  }
                  return Promise.resolve();
                } catch (error) {
                  return Promise.reject('Please enter valid JSON');
                }
              }
            }
          ]}
        >
          <TextArea
            placeholder="Enter default properties in JSON format (optional)"
            rows={4}
            style={{ fontFamily: 'monospace' }}
          />
        </Form.Item>

        <Form.Item
          name="is_public"
          label="Visibility"
          valuePropName="checked"
        >
          <Switch
            checkedChildren={<EyeOutlined />}
            unCheckedChildren={<LockOutlined />}
          />
          <Text type="secondary" style={{ marginLeft: 8 }}>
            Make this template public
          </Text>
        </Form.Item>
      </Form>
    );
  };

  // Import template
  const importTemplate = async (templateData) => {
    try {
      let url;
      if (templateType === 'components') {
        url = '/api/component-templates/import_template/';
      } else if (templateType === 'layouts') {
        url = '/api/layout-templates/import_template/';
      } else if (templateType === 'apps') {
        url = '/api/app-templates/import_template/';
      }

      await axios.post(url, { template_data: templateData });

      message.success('Template imported successfully');
      setImportModalVisible(false);

      // Refresh templates
      fetchAllTemplates();
    } catch (error) {
      console.error('Error importing template:', error);
      message.error('Failed to import template');
    }
  };

  return (
    <MainLayout>
      <div className="templates-page">
        <div className="templates-header">
          <Title level={2}>
            <AppstoreOutlined /> Template Library
          </Title>
          <Space>
            <Button
              icon={<UploadOutlined />}
              onClick={() => setImportModalVisible(true)}
            >
              Import Template
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                form.resetFields();
                setCreateModalVisible(true);
              }}
            >
              Create {getTemplateTypeDisplayName()}
            </Button>
          </Space>
        </div>

        {/* Template Type Tabs */}
        <div className="template-type-tabs">
          <Tabs
            activeKey={templateType}
            onChange={setTemplateType}
            type="card"
            size="large"
          >
            <TabPane
              tab={
                <span>
                  <AppstoreOutlined />
                  Components
                  <Badge count={componentTemplates.length} style={{ marginLeft: 8 }} />
                </span>
              }
              key="components"
            />
            <TabPane
              tab={
                <span>
                  <LayoutOutlined />
                  Layouts
                  <Badge count={layoutTemplates.length} style={{ marginLeft: 8 }} />
                </span>
              }
              key="layouts"
            />
            <TabPane
              tab={
                <span>
                  <MobileOutlined />
                  App Starters
                  <Badge count={appTemplates.length} style={{ marginLeft: 8 }} />
                </span>
              }
              key="apps"
            />
          </Tabs>
        </div>

        {/* User/Public Tabs */}
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          className="templates-tabs"
        >
          <TabPane
            tab={<span><UserOutlined /> My Templates</span>}
            key="my"
          >
            <div className="templates-filters">
              <Input
                placeholder="Search templates"
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: 250 }}
              />

              <Select
                placeholder={`Filter by ${templateType === 'components' ? 'component type' :
                  templateType === 'layouts' ? 'layout type' : 'category'}`}
                value={templateType === 'apps' ? filterCategory : filterType}
                onChange={templateType === 'apps' ? setFilterCategory : setFilterType}
                style={{ width: 200 }}
                allowClear
                suffixIcon={<FilterOutlined />}
              >
                {templateType === 'apps' ?
                  appCategories.map(category => (
                    <Option key={category.value} value={category.value}>
                      {category.label}
                    </Option>
                  )) :
                  getUniqueTypes().map(type => (
                    <Option key={type} value={type}>{type}</Option>
                  ))
                }
              </Select>

              <Select
                placeholder="Filter by visibility"
                value={visibility}
                onChange={setVisibility}
                style={{ width: 150 }}
                suffixIcon={<FilterOutlined />}
              >
                <Option value="all">All</Option>
                <Option value="public">Public</Option>
                <Option value="private">Private</Option>
              </Select>
            </div>

            <div className="templates-list">
              <Spin spinning={loading}>
                {filteredTemplates.length > 0 ? (
                  <List
                    grid={{
                      gutter: 16,
                      xs: 1,
                      sm: 1,
                      md: 2,
                      lg: 2,
                      xl: 3,
                      xxl: 3
                    }}
                    dataSource={filteredTemplates}
                    renderItem={renderTemplateItem}
                    pagination={{
                      pageSize: 9,
                      hideOnSinglePage: true
                    }}
                  />
                ) : (
                  <Empty
                    description={
                      <span>
                        {loading ? 'Loading templates...' : `No ${templateType} templates found`}
                      </span>
                    }
                  />
                )}
              </Spin>
            </div>
          </TabPane>

          <TabPane
            tab={<span><GlobalOutlined /> Public Templates</span>}
            key="public"
          >
            <div className="templates-filters">
              <Input
                placeholder="Search templates"
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: 250 }}
              />

              <Select
                placeholder={`Filter by ${templateType === 'components' ? 'component type' :
                  templateType === 'layouts' ? 'layout type' : 'category'}`}
                value={templateType === 'apps' ? filterCategory : filterType}
                onChange={templateType === 'apps' ? setFilterCategory : setFilterType}
                style={{ width: 200 }}
                allowClear
                suffixIcon={<FilterOutlined />}
              >
                {templateType === 'apps' ?
                  appCategories.map(category => (
                    <Option key={category.value} value={category.value}>
                      {category.label}
                    </Option>
                  )) :
                  getUniqueTypes().map(type => (
                    <Option key={type} value={type}>{type}</Option>
                  ))
                }
              </Select>
            </div>

            <div className="templates-list">
              <Spin spinning={loading}>
                {filteredTemplates.length > 0 ? (
                  <List
                    grid={{
                      gutter: 16,
                      xs: 1,
                      sm: 1,
                      md: 2,
                      lg: 2,
                      xl: 3,
                      xxl: 3
                    }}
                    dataSource={filteredTemplates}
                    renderItem={renderTemplateItem}
                    pagination={{
                      pageSize: 9,
                      hideOnSinglePage: true
                    }}
                  />
                ) : (
                  <Empty
                    description={
                      <span>
                        {loading ? 'Loading templates...' : `No public ${templateType} templates found`}
                      </span>
                    }
                  />
                )}
              </Spin>
            </div>
          </TabPane>
        </Tabs>

        {/* Create Template Modal */}
        <Modal
          title={`Create ${getTemplateTypeDisplayName()}`}
          open={createModalVisible}
          onCancel={() => setCreateModalVisible(false)}
          onOk={() => form.submit()}
          okText="Create"
          confirmLoading={saving}
          width={800}
        >
          {renderTemplateForm()}
        </Modal>

        {/* Edit Template Modal */}
        <Modal
          title={`Edit ${currentTemplate?.template_type === 'component' ? 'Component' :
            currentTemplate?.template_type === 'layout' ? 'Layout' : 'App'} Template`}
          open={editModalVisible}
          onCancel={() => setEditModalVisible(false)}
          onOk={() => form.submit()}
          okText="Save Changes"
          confirmLoading={saving}
          width={800}
        >
          {renderTemplateForm()}
        </Modal>

        {/* Import Template Modal */}
        <Modal
          title="Import Template"
          open={importModalVisible}
          onCancel={() => setImportModalVisible(false)}
          footer={null}
          width={600}
        >
          <Upload.Dragger
            name="file"
            multiple={false}
            accept=".json"
            beforeUpload={(file) => {
              const reader = new FileReader();
              reader.onload = async (e) => {
                try {
                  const templateData = JSON.parse(e.target.result);
                  await importTemplate(templateData);
                } catch (error) {
                  message.error('Invalid JSON file');
                }
              };
              reader.readAsText(file);
              return false; // Prevent automatic upload
            }}
          >
            <p className="ant-upload-drag-icon">
              <UploadOutlined />
            </p>
            <p className="ant-upload-text">Click or drag file to this area to upload</p>
            <p className="ant-upload-hint">
              Support for JSON template files only. The template will be imported to the current template type ({templateType}).
            </p>
          </Upload.Dragger>
        </Modal>
      </div>

      <style jsx="true">{`
        .templates-page {
          max-width: 1400px;
          margin: 0 auto;
          padding: 0 16px;
        }

        .templates-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 24px;
        }

        .template-type-tabs {
          margin-bottom: 24px;
        }

        .template-type-tabs .ant-tabs-card > .ant-tabs-content {
          margin-top: 0;
        }

        .template-type-tabs .ant-tabs-card > .ant-tabs-content > .ant-tabs-tabpane {
          background: transparent;
          border: none;
        }

        .templates-tabs {
          background-color: #fff;
          padding: 16px;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .templates-filters {
          display: flex;
          gap: 16px;
          margin-bottom: 24px;
          flex-wrap: wrap;
        }

        .templates-list {
          min-height: 400px;
        }

        .ant-card-cover img {
          border-radius: 8px 8px 0 0;
        }

        .ant-upload-drag {
          border: 2px dashed #d9d9d9;
          border-radius: 8px;
          background: #fafafa;
          text-align: center;
          padding: 40px 20px;
        }

        .ant-upload-drag:hover {
          border-color: #1890ff;
        }

        .ant-upload-drag-icon {
          font-size: 48px;
          color: #d9d9d9;
          margin-bottom: 16px;
        }

        .ant-upload-text {
          font-size: 16px;
          color: #666;
          margin-bottom: 8px;
        }

        .ant-upload-hint {
          font-size: 14px;
          color: #999;
        }
      `}</style>
    </MainLayout>
  );
};

export default TemplatesPage;
