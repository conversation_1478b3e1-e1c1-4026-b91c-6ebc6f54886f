from django.contrib import admin
from .models import App, AppVersion, ComponentTemplate, LayoutTemplate, AppTemplate

class AppVersionInline(admin.TabularInline):
    model = AppVersion
    extra = 0
    readonly_fields = ('version_number', 'created_at', 'created_by')

@admin.register(App)
class AppAdmin(admin.ModelAdmin):
    list_display = ('name', 'user', 'is_public', 'created_at', 'updated_at')
    list_filter = ('is_public', 'created_at', 'updated_at')
    search_fields = ('name', 'description')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [AppVersionInline]

@admin.register(AppVersion)
class AppVersionAdmin(admin.ModelAdmin):
    list_display = ('app', 'version_number', 'created_at', 'created_by')
    list_filter = ('created_at',)
    search_fields = ('app__name', 'commit_message')
    readonly_fields = ('version_number', 'created_at')

@admin.register(ComponentTemplate)
class ComponentTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'component_type', 'user', 'is_public', 'created_at')
    list_filter = ('is_public', 'component_type', 'created_at')
    search_fields = ('name', 'description', 'component_type')
    readonly_fields = ('created_at',)

@admin.register(LayoutTemplate)
class LayoutTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'layout_type', 'user', 'is_public', 'created_at')
    list_filter = ('is_public', 'layout_type', 'created_at')
    search_fields = ('name', 'description', 'layout_type')
    readonly_fields = ('created_at',)

@admin.register(AppTemplate)
class AppTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'app_category', 'user', 'is_public', 'created_at')
    list_filter = ('is_public', 'app_category', 'created_at')
    search_fields = ('name', 'description', 'app_category')
    readonly_fields = ('created_at',)
