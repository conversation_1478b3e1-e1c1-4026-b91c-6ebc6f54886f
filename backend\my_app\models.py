from django.db import models
from django.contrib.auth.models import User
import json

class App(models.Model):
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='apps', null=True, blank=True)
    app_data = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_public = models.BooleanField(default=False)

    class Meta:
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['is_public']),
            models.Index(fields=['created_at']),
            models.Index(fields=['updated_at']),
        ]

    def __str__(self):
        return f"{self.name} ({self.user.username if self.user else 'Anonymous'})"

    def get_app_data_json(self):
        """Return app_data as a Python dictionary"""
        try:
            return json.loads(self.app_data)
        except (json.JSONDecodeError, TypeError):
            return {"components": [], "layouts": [], "styles": {}, "data": {}}

    def create_version(self, commit_message, created_by):
        version_number = self.versions.count() + 1
        return AppVersion.objects.create(
            app=self,
            version_number=version_number,
            app_data=self.app_data,
            created_by=created_by,
            commit_message=commit_message
        )

class AppVersion(models.Model):
    app = models.ForeignKey(App, on_delete=models.CASCADE, related_name='versions')
    version_number = models.PositiveIntegerField()
    app_data = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    commit_message = models.TextField(blank=True)

    class Meta:
        unique_together = ('app', 'version_number')
        ordering = ['-version_number']
        indexes = [
            models.Index(fields=['app', 'version_number']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.app.name} v{self.version_number}"

    def get_app_data_json(self):
        """Return app_data as a Python dictionary"""
        try:
            return json.loads(self.app_data)
        except (json.JSONDecodeError, TypeError):
            return {"components": [], "layouts": [], "styles": {}, "data": {}}

class ComponentTemplate(models.Model):
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    component_type = models.CharField(max_length=100)
    default_props = models.TextField()
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='component_templates', null=True, blank=True)
    is_public = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['is_public']),
            models.Index(fields=['component_type']),
        ]

    def __str__(self):
        return self.name

    def get_default_props_json(self):
        """Return default_props as a Python dictionary"""
        try:
            return json.loads(self.default_props)
        except (json.JSONDecodeError, TypeError):
            return {}

class LayoutTemplate(models.Model):
    """
    Model for layout templates that define reusable layout structures.
    """
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    layout_type = models.CharField(max_length=100, help_text="Type of layout (grid, flex, sidebar, etc.)")
    components = models.JSONField(default=dict, help_text="Component references and configurations")
    default_props = models.JSONField(default=dict, help_text="Default properties for the layout")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='layout_templates', null=True, blank=True)
    is_public = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['is_public']),
            models.Index(fields=['layout_type']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def get_components_json(self):
        """Return components as a Python dictionary"""
        return self.components if isinstance(self.components, dict) else {}

    def get_default_props_json(self):
        """Return default_props as a Python dictionary"""
        return self.default_props if isinstance(self.default_props, dict) else {}


class AppTemplate(models.Model):
    """
    Model for complete application templates that serve as starting points.
    """
    APP_CATEGORIES = [
        ('business', 'Business Apps'),
        ('ecommerce', 'E-commerce'),
        ('portfolio', 'Portfolio'),
        ('dashboard', 'Dashboard'),
        ('landing', 'Landing Page'),
        ('blog', 'Blog'),
        ('social', 'Social Media'),
        ('education', 'Education'),
        ('healthcare', 'Healthcare'),
        ('finance', 'Finance'),
        ('other', 'Other'),
    ]

    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    app_category = models.CharField(max_length=100, choices=APP_CATEGORIES, default='other')
    components = models.JSONField(default=dict, help_text="Complete app structure with components")
    default_props = models.JSONField(default=dict, help_text="Default properties for the app")
    required_components = models.JSONField(default=list, help_text="List of required component dependencies")
    preview_image = models.URLField(blank=True, help_text="URL to template preview image")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='app_templates', null=True, blank=True)
    is_public = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['is_public']),
            models.Index(fields=['app_category']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def get_components_json(self):
        """Return components as a Python dictionary"""
        return self.components if isinstance(self.components, dict) else {}

    def get_default_props_json(self):
        """Return default_props as a Python dictionary"""
        return self.default_props if isinstance(self.default_props, dict) else {}

    def get_required_components_list(self):
        """Return required_components as a Python list"""
        return self.required_components if isinstance(self.required_components, list) else []
