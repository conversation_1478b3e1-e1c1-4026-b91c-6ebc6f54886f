import React, { useState } from 'react';
import { Button, Dropdown, Tooltip } from 'antd';
import {
  SunOutlined,
  MoonOutlined,
  DesktopOutlined,
  CheckOutlined
} from '@ant-design/icons';
import styled, { keyframes } from 'styled-components';
import { useEnhancedTheme } from '../../contexts/EnhancedThemeContext';

// Animations
const rotate = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
`;

// Styled components
const ToggleContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const ToggleButton = styled(Button)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid var(--color-border);
  background-color: var(--color-surface);
  color: var(--color-text);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  /* Ensure WCAG AA contrast compliance */
  &:hover, &:focus {
    border-color: var(--color-primary);
    color: var(--color-primary);
    background-color: var(--color-background-secondary);
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
  }

  &:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }

  &:active {
    transform: scale(0.95);
  }

  .anticon {
    font-size: 18px;
    animation: ${fadeIn} 0.3s ease;

    /* Ensure icon has sufficient contrast */
    filter: contrast(1.1);
  }

  &.rotating .anticon {
    animation: ${rotate} 0.5s ease;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    border-width: 2px;
    font-weight: 600;

    &:hover, &:focus {
      border-width: 3px;
    }

    .anticon {
      filter: contrast(1.3);
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    transition: none;

    &:hover {
      transform: none;
    }

    &:active {
      transform: none;
    }

    .anticon {
      animation: none;
    }

    &.rotating .anticon {
      animation: none;
    }
  }
`;

const DropdownContent = styled.div`
  min-width: 180px;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border-light);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  padding: 4px;
`;

const ThemeOption = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
  margin: 2px 0;
  color: var(--color-text);

  &:hover {
    background-color: var(--color-background-secondary);
    transform: translateX(2px);
  }

  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 1px;
  }

  &.active {
    background-color: var(--color-primary);
    color: white;
    box-shadow: var(--shadow-sm);
  }

  .option-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .anticon {
    font-size: 16px;

    /* Ensure icon contrast in active state */
    filter: ${props => props.className?.includes('active') ? 'none' : 'contrast(1.1)'};
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    border: 1px solid var(--color-border);

    &:hover {
      border-color: var(--color-primary);
    }

    &.active {
      border: 2px solid white;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    transition: background-color 0.2s ease;

    &:hover {
      transform: none;
    }
  }
`;

const ThemeLabel = styled.span`
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
`;

const ThemeDescription = styled.div`
  font-size: 12px;
  color: ${props => props.active ? 'rgba(255, 255, 255, 0.8)' : 'var(--color-text-secondary)'};
  margin-top: 2px;
  line-height: 1.2;

  /* Ensure description text meets contrast requirements */
  opacity: ${props => props.active ? 0.9 : 0.8};
`;

const DarkModeToggle = ({ showDropdown = true, size = 'default' }) => {
  const { isDarkMode, themeMode, toggleDarkMode, setThemeMode, systemPrefersDark } = useEnhancedTheme();
  const [isRotating, setIsRotating] = useState(false);

  const handleToggle = () => {
    setIsRotating(true);
    toggleDarkMode();
    setTimeout(() => setIsRotating(false), 500);
  };

  const handleThemeChange = (mode) => {
    setIsRotating(true);
    setThemeMode(mode);
    setTimeout(() => setIsRotating(false), 500);
  };

  const getIcon = () => {
    if (themeMode === 'system') {
      return <DesktopOutlined />;
    }
    return isDarkMode ? <MoonOutlined /> : <SunOutlined />;
  };

  const getTooltipTitle = () => {
    switch (themeMode) {
      case 'light':
        return 'Light mode';
      case 'dark':
        return 'Dark mode';
      case 'system':
        return `System mode (${systemPrefersDark ? 'dark' : 'light'})`;
      default:
        return 'Toggle theme';
    }
  };

  const themeOptions = [
    {
      key: 'light',
      icon: <SunOutlined />,
      label: 'Light',
      description: 'Light theme',
    },
    {
      key: 'dark',
      icon: <MoonOutlined />,
      label: 'Dark',
      description: 'Dark theme',
    },
    {
      key: 'system',
      icon: <DesktopOutlined />,
      label: 'System',
      description: 'Follow system preference',
    },
  ];

  const dropdownMenu = {
    items: themeOptions.map(option => ({
      key: option.key,
      label: (
        <ThemeOption
          className={themeMode === option.key ? 'active' : ''}
          onClick={() => handleThemeChange(option.key)}
          role="menuitem"
          tabIndex={0}
          aria-selected={themeMode === option.key}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleThemeChange(option.key);
            }
          }}
        >
          <div className="option-content">
            {option.icon}
            <div>
              <ThemeLabel>{option.label}</ThemeLabel>
              <ThemeDescription active={themeMode === option.key}>
                {option.description}
              </ThemeDescription>
            </div>
          </div>
          {themeMode === option.key && (
            <CheckOutlined aria-label="Selected" />
          )}
        </ThemeOption>
      ),
    })),
  };

  if (!showDropdown) {
    return (
      <ToggleContainer>
        <Tooltip title={getTooltipTitle()} placement="bottom">
          <ToggleButton
            type="text"
            size={size}
            className={isRotating ? 'rotating' : ''}
            onClick={handleToggle}
            aria-label={`Switch to ${isDarkMode ? 'light' : 'dark'} mode. Current theme: ${getTooltipTitle()}`}
            aria-pressed={isDarkMode}
            role="switch"
          >
            {getIcon()}
          </ToggleButton>
        </Tooltip>
      </ToggleContainer>
    );
  }

  return (
    <ToggleContainer>
      <Dropdown
        menu={dropdownMenu}
        trigger={['click']}
        placement="bottomRight"
        arrow
        dropdownRender={(menu) => (
          <DropdownContent role="menu" aria-label="Theme selection menu">
            {menu}
          </DropdownContent>
        )}
        onOpenChange={(open) => {
          // Announce to screen readers when menu opens/closes
          if (open) {
            // Focus management for accessibility
            setTimeout(() => {
              const firstMenuItem = document.querySelector('[role="menuitem"]');
              if (firstMenuItem) {
                firstMenuItem.focus();
              }
            }, 100);
          }
        }}
      >
        <Tooltip title={getTooltipTitle()} placement="bottom">
          <ToggleButton
            type="text"
            size={size}
            className={isRotating ? 'rotating' : ''}
            aria-label={`Theme options menu. Current theme: ${getTooltipTitle()}`}
            aria-haspopup="menu"
            aria-expanded="false"
            role="button"
          >
            {getIcon()}
          </ToggleButton>
        </Tooltip>
      </Dropdown>
    </ToggleContainer>
  );
};

export default DarkModeToggle;
