from rest_framework import serializers
from .models import App, AppVers<PERSON>, ComponentTemplate, LayoutTemplate, AppTemplate
import json

class AppSerializer(serializers.ModelSerializer):
    """Serializer for the App model"""
    app_data_json = serializers.SerializerMethodField()
    
    class Meta:
        model = App
        fields = ['id', 'name', 'description', 'user', 'app_data', 'app_data_json', 
                  'created_at', 'updated_at', 'is_public']
        read_only_fields = ['created_at', 'updated_at']
    
    def get_app_data_json(self, obj):
        """Return app_data as a Python dictionary"""
        return obj.get_app_data_json()
    
    def validate_app_data(self, value):
        """Validate that app_data is valid JSON"""
        try:
            if isinstance(value, str):
                json.loads(value)
            return value
        except json.JSONDecodeError:
            raise serializers.ValidationError("Invalid JSON data")

class AppVersionSerializer(serializers.ModelSerializer):
    """Serializer for the AppVersion model"""
    app_data_json = serializers.SerializerMethodField()
    
    class Meta:
        model = AppVersion
        fields = ['id', 'app', 'version_number', 'app_data', 'app_data_json', 
                  'created_at', 'created_by', 'commit_message']
        read_only_fields = ['created_at', 'version_number']
    
    def get_app_data_json(self, obj):
        """Return app_data as a Python dictionary"""
        return obj.get_app_data_json()

class ComponentTemplateSerializer(serializers.ModelSerializer):
    """Serializer for the ComponentTemplate model"""
    default_props_json = serializers.SerializerMethodField()
    
    class Meta:
        model = ComponentTemplate
        fields = ['id', 'name', 'description', 'component_type', 'default_props', 
                  'default_props_json', 'user', 'is_public', 'created_at']
        read_only_fields = ['created_at']
    
    def get_default_props_json(self, obj):
        """Return default_props as a Python dictionary"""
        return obj.get_default_props_json()
    
    def validate_default_props(self, value):
        """Validate that default_props is valid JSON"""
        try:
            if isinstance(value, str):
                json.loads(value)
            return value
        except json.JSONDecodeError:
            raise serializers.ValidationError("Invalid JSON data")


class LayoutTemplateSerializer(serializers.ModelSerializer):
    """Serializer for the LayoutTemplate model"""
    components_json = serializers.SerializerMethodField()
    default_props_json = serializers.SerializerMethodField()

    class Meta:
        model = LayoutTemplate
        fields = ['id', 'name', 'description', 'layout_type', 'components',
                  'components_json', 'default_props', 'default_props_json',
                  'user', 'is_public', 'created_at']
        read_only_fields = ['created_at']

    def get_components_json(self, obj):
        """Return components as a Python dictionary"""
        return obj.get_components_json()

    def get_default_props_json(self, obj):
        """Return default_props as a Python dictionary"""
        return obj.get_default_props_json()

    def validate_components(self, value):
        """Validate that components is valid JSON if provided as string"""
        if isinstance(value, str):
            try:
                json.loads(value)
            except json.JSONDecodeError:
                raise serializers.ValidationError("Invalid JSON data for components")
        return value

    def validate_default_props(self, value):
        """Validate that default_props is valid JSON if provided as string"""
        if isinstance(value, str):
            try:
                json.loads(value)
            except json.JSONDecodeError:
                raise serializers.ValidationError("Invalid JSON data for default_props")
        return value


class AppTemplateSerializer(serializers.ModelSerializer):
    """Serializer for the AppTemplate model"""
    components_json = serializers.SerializerMethodField()
    default_props_json = serializers.SerializerMethodField()
    required_components_list = serializers.SerializerMethodField()

    class Meta:
        model = AppTemplate
        fields = ['id', 'name', 'description', 'app_category', 'components',
                  'components_json', 'default_props', 'default_props_json',
                  'required_components', 'required_components_list', 'preview_image',
                  'user', 'is_public', 'created_at']
        read_only_fields = ['created_at']

    def get_components_json(self, obj):
        """Return components as a Python dictionary"""
        return obj.get_components_json()

    def get_default_props_json(self, obj):
        """Return default_props as a Python dictionary"""
        return obj.get_default_props_json()

    def get_required_components_list(self, obj):
        """Return required_components as a Python list"""
        return obj.get_required_components_list()

    def validate_components(self, value):
        """Validate that components is valid JSON if provided as string"""
        if isinstance(value, str):
            try:
                json.loads(value)
            except json.JSONDecodeError:
                raise serializers.ValidationError("Invalid JSON data for components")
        return value

    def validate_default_props(self, value):
        """Validate that default_props is valid JSON if provided as string"""
        if isinstance(value, str):
            try:
                json.loads(value)
            except json.JSONDecodeError:
                raise serializers.ValidationError("Invalid JSON data for default_props")
        return value

    def validate_required_components(self, value):
        """Validate that required_components is a valid list if provided as string"""
        if isinstance(value, str):
            try:
                parsed = json.loads(value)
                if not isinstance(parsed, list):
                    raise serializers.ValidationError("Required components must be a list")
            except json.JSONDecodeError:
                raise serializers.ValidationError("Invalid JSON data for required_components")
        return value
