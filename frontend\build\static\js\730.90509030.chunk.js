"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[730],{3730:(e,t,r)=>{r.r(t),r.d(t,{default:()=>Ee});var n=r(4467),a=r(436),c=r(467),o=r(5544),l=r(7528),i=r(4756),u=r.n(i),s=r(6540),m=r(3016),p=r(4358),d=r(2395),f=r(9467),E=r(9740),g=r(6914),v=r(2702),w=r(7977),A=r(9249),y=r(6044),h=r(7355),b=r(677),k=r(7072),j=r(6955),S=r(7308),O=r(5597),P=r(1849),T=r(6552),C=r(234),x=r(261),D=r(1952),I=r(7046),L=r(3598),R=r(2877),M=r(9237),z=r(751),J=r(6325),N=r(8602),Z=r(7767),B=r(1250);function F(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function H(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?F(Object(r),!0).forEach((function(t){(0,n.A)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):F(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var U={marks:{},measures:{},resources:[],errors:[]},W=function(){if(window.PerformanceObserver)try{new PerformanceObserver((function(e){var t=e.getEntries();U.resources=[].concat((0,a.A)(U.resources),(0,a.A)(t))})).observe({entryTypes:["resource"]}),new PerformanceObserver((function(e){e.getEntries().forEach((function(e){console.warn("Long task detected:",e),U.errors.push({type:"long-task",message:"Long task detected: ".concat(e.name," (").concat(e.duration,"ms)"),timestamp:(new Date).toISOString(),details:e})}))})).observe({entryTypes:["longtask"]}),new PerformanceObserver((function(e){e.getEntries().forEach((function(e){q("paint-".concat(e.name),e.startTime)}))})).observe({entryTypes:["paint"]}),new PerformanceObserver((function(e){e.getEntries().forEach((function(e){q("fid-".concat(e.name),e.startTime),e.duration>100&&U.errors.push({type:"fid",message:"High First Input Delay: ".concat(e.duration,"ms"),timestamp:(new Date).toISOString(),details:e})}))})).observe({entryTypes:["first-input"]}),new PerformanceObserver((function(e){e.getEntries().forEach((function(e){e.value>.1&&U.errors.push({type:"layout-shift",message:"High Layout Shift: ".concat(e.value),timestamp:(new Date).toISOString(),details:e})}))})).observe({entryTypes:["layout-shift"]})}catch(e){console.warn("Error setting up PerformanceObserver:",e)}},q=function(e,t){try{void 0!==t?window.performance.mark(e,{startTime:t}):window.performance.mark(e);var r=window.performance.getEntriesByName(e,"mark");r.length>0&&(U.marks[e]=r[r.length-1])}catch(t){console.warn('Error creating mark "'.concat(e,'":'),t)}},K=function(e,t,r){try{window.performance.measure(e,t,r);var n=window.performance.getEntriesByName(e,"measure");n.length>0&&(U.measures[e]=n[n.length-1])}catch(t){console.warn('Error creating measure "'.concat(e,'":'),t)}},_=function(){return H({},U.marks)},G=function(){return H({},U.measures)},V=function(){return(0,a.A)(U.resources)},Y=function(){return(0,a.A)(U.errors)},Q=function(){try{window.performance.clearMarks(),U.marks={}}catch(e){console.warn("Error clearing marks:",e)}},X=function(){try{window.performance.clearMeasures(),U.measures={}}catch(e){console.warn("Error clearing measures:",e)}},$=function(){try{window.performance.clearResourceTimings(),U.resources=[]}catch(e){console.warn("Error clearing resource timings:",e)}},ee=function(){U.errors=[]},te=function(){return{marks:_(),measures:G(),resources:V(),errors:Y(),navigation:window.performance.timing?{navigationStart:window.performance.timing.navigationStart,loadEventEnd:window.performance.timing.loadEventEnd,domComplete:window.performance.timing.domComplete,domInteractive:window.performance.timing.domInteractive,domContentLoadedEventEnd:window.performance.timing.domContentLoadedEventEnd}:null,memory:window.performance.memory?{jsHeapSizeLimit:window.performance.memory.jsHeapSizeLimit,totalJSHeapSize:window.performance.memory.totalJSHeapSize,usedJSHeapSize:window.performance.memory.usedJSHeapSize}:null}};const re=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.enabled,r=void 0!==t&&t,n=e.autoMarkRenders,a=void 0===n||n,c=e.autoMarkEffects,l=void 0===c||c,i=e.autoMarkEvents,u=void 0===i||i,m=e.reportInterval,p=void 0===m?1e4:m,d=e.onReport,f=void 0===d?null:d,E=(0,s.useState)(r),g=(0,o.A)(E,2),v=g[0],w=g[1],A=(0,s.useState)(!1),y=(0,o.A)(A,2),h=y[0],b=y[1],k=(0,s.useRef)(null),j=(0,s.useRef)("Component-".concat(Math.random().toString(36).substr(2,9))),S=(0,s.useRef)(0),O=(0,s.useRef)(0),P=(0,s.useRef)(0),T=(0,s.useRef)(null);(0,s.useEffect)((function(){return v&&!h&&(k.current=window.performance?(U.marks={},U.measures={},U.resources=[],U.errors=[],window.performance.setResourceTimingBufferSize&&window.performance.setResourceTimingBufferSize(500),W(),{mark:q,measure:K,getMarks:_,getMeasures:G,getResourceTimings:V,getErrors:Y,clearMarks:Q,clearMeasures:X,clearResourceTimings:$,clearErrors:ee,getPerformanceReport:te}):(console.warn("Performance API is not available in this browser."),null),b(!0),k.current&&k.current.mark("".concat(j.current,"-mount"))),function(){k.current&&(k.current.mark("".concat(j.current,"-unmount")),k.current.measure("".concat(j.current,"-lifetime"),"".concat(j.current,"-mount"),"".concat(j.current,"-unmount"))),T.current&&clearInterval(T.current)}}),[v,h]),(0,s.useEffect)((function(){if(v&&h&&f&&p>0)return T.current=setInterval((function(){if(k.current){var e=k.current.getPerformanceReport();f(e)}}),p),function(){clearInterval(T.current)}}),[v,h,f,p]),(0,s.useEffect)((function(){v&&h&&a&&k.current&&(S.current+=1,k.current.mark("".concat(j.current,"-render-").concat(S.current)),S.current>1&&k.current.measure("".concat(j.current,"-render-time-").concat(S.current),"".concat(j.current,"-render-").concat(S.current-1),"".concat(j.current,"-render-").concat(S.current)))}));var C=(0,s.useCallback)((function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"effect";return v&&h&&l&&k.current?function(){O.current+=1;var r="".concat(j.current,"-").concat(t,"-").concat(O.current);k.current.mark("".concat(r,"-start"));try{var n=e.apply(void 0,arguments);return n&&"function"==typeof n.then?n.then((function(e){return k.current.mark("".concat(r,"-end")),k.current.measure("".concat(r,"-time"),"".concat(r,"-start"),"".concat(r,"-end")),e})).catch((function(e){throw k.current.mark("".concat(r,"-error")),k.current.measure("".concat(r,"-error-time"),"".concat(r,"-start"),"".concat(r,"-error")),e})):(k.current.mark("".concat(r,"-end")),k.current.measure("".concat(r,"-time"),"".concat(r,"-start"),"".concat(r,"-end")),n)}catch(e){throw k.current.mark("".concat(r,"-error")),k.current.measure("".concat(r,"-error-time"),"".concat(r,"-start"),"".concat(r,"-error")),e}}:e}),[v,h,l]),x=(0,s.useCallback)((function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"event";return v&&h&&u&&k.current?function(){P.current+=1;var r="".concat(j.current,"-").concat(t,"-").concat(P.current);k.current.mark("".concat(r,"-start"));try{var n=e.apply(void 0,arguments);return n&&"function"==typeof n.then?n.then((function(e){return k.current.mark("".concat(r,"-end")),k.current.measure("".concat(r,"-time"),"".concat(r,"-start"),"".concat(r,"-end")),e})).catch((function(e){throw k.current.mark("".concat(r,"-error")),k.current.measure("".concat(r,"-error-time"),"".concat(r,"-start"),"".concat(r,"-error")),e})):(k.current.mark("".concat(r,"-end")),k.current.measure("".concat(r,"-time"),"".concat(r,"-start"),"".concat(r,"-end")),n)}catch(e){throw k.current.mark("".concat(r,"-error")),k.current.measure("".concat(r,"-error-time"),"".concat(r,"-start"),"".concat(r,"-error")),e}}:e}),[v,h,u]),D=(0,s.useCallback)((function(e){j.current=e}),[]),I=(0,s.useCallback)((function(e){w(e)}),[]),L=(0,s.useCallback)((function(){return v&&h&&k.current?k.current.getPerformanceReport():null}),[v,h]),R=(0,s.useCallback)((function(){v&&h&&k.current&&(k.current.clearMarks(),k.current.clearMeasures(),k.current.clearResourceTimings(),k.current.clearErrors())}),[v,h]);return{isEnabled:v,isInitialized:h,setEnabled:I,setComponentName:D,wrapEffect:C,wrapEvent:x,getReport:L,clearData:R,mark:(0,s.useCallback)((function(e){v&&h&&k.current&&k.current.mark("".concat(j.current,"-").concat(e))}),[v,h]),measure:(0,s.useCallback)((function(e,t,r){v&&h&&k.current&&k.current.measure("".concat(j.current,"-").concat(e),"".concat(j.current,"-").concat(t),"".concat(j.current,"-").concat(r))}),[v,h])}};var ne,ae,ce;function oe(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function le(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?oe(Object(r),!0).forEach((function(t){(0,n.A)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):oe(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var ie=m.A.Title,ue=m.A.Text,se=p.A.Option,me=d.A.TabPane,pe=B.Ay.div(ne||(ne=(0,l.A)(["\n  padding: 24px;\n"]))),de=B.Ay.div(ae||(ae=(0,l.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  flex-wrap: wrap;\n  gap: 16px;\n"]))),fe=B.Ay.div(ce||(ce=(0,l.A)(["\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n"])));const Ee=function(){var e=(0,s.useState)([]),t=(0,o.A)(e,2),r=t[0],n=t[1],l=(0,s.useState)(!0),i=(0,o.A)(l,2),m=i[0],B=i[1],F=(0,s.useState)(""),H=(0,o.A)(F,2),U=H[0],W=H[1],q=(0,s.useState)(!1),K=(0,o.A)(q,2),_=K[0],G=K[1],V=(0,s.useState)(!1),Y=(0,o.A)(V,2),Q=Y[0],X=Y[1],$=(0,s.useState)(null),ee=(0,o.A)($,2),te=ee[0],ne=ee[1],ae=f.A.useForm(),ce=(0,o.A)(ae,1)[0],oe=(0,Z.Zp)(),Ee=re({enabled:!1});Ee.setComponentName("ProjectsPage"),(0,s.useEffect)((function(){Ee.mark("fetch-projects-start");var e=function(){var e=(0,c.A)(u().mark((function e(){return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,new Promise((function(e){return setTimeout(e,1e3)}));case 3:n([{id:"1",name:"E-commerce Dashboard",description:"A dashboard for an e-commerce website",status:"active",createdAt:"2023-05-15T10:30:00Z",updatedAt:"2023-05-20T14:45:00Z",owner:"John Doe",team:["John Doe","Jane Smith"],tags:["dashboard","e-commerce"],template:"dashboard"},{id:"2",name:"Blog Template",description:"A template for a blog website",status:"completed",createdAt:"2023-04-10T09:15:00Z",updatedAt:"2023-04-25T16:20:00Z",owner:"Jane Smith",team:["Jane Smith","Bob Johnson"],tags:["blog","template"],template:"blog"},{id:"3",name:"Mobile App UI",description:"UI design for a mobile app",status:"archived",createdAt:"2023-03-05T11:45:00Z",updatedAt:"2023-03-15T13:10:00Z",owner:"Bob Johnson",team:["Bob Johnson"],tags:["mobile","ui"],template:"mobile"},{id:"4",name:"Landing Page",description:"A landing page for a product",status:"active",createdAt:"2023-05-01T08:30:00Z",updatedAt:"2023-05-10T15:45:00Z",owner:"John Doe",team:["John Doe","Alice Williams"],tags:["landing","marketing"],template:"landing"},{id:"5",name:"Admin Panel",description:"An admin panel for a web application",status:"active",createdAt:"2023-04-20T13:15:00Z",updatedAt:"2023-05-05T10:30:00Z",owner:"Alice Williams",team:["Alice Williams","John Doe"],tags:["admin","dashboard"],template:"admin"}]),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("Error fetching projects:",e.t0),E.Ay.error("Failed to load projects");case 11:return e.prev=11,B(!1),Ee.mark("fetch-projects-end"),Ee.measure("fetch-projects","fetch-projects-start","fetch-projects-end"),e.finish(11);case 16:case"end":return e.stop()}}),e,null,[[0,7,11,16]])})));return function(){return e.apply(this,arguments)}}();e()}),[Ee]);var ge=function(){var e=(0,c.A)(u().mark((function e(t){var c;return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return Ee.mark("create-project-start"),e.prev=1,e.next=4,new Promise((function(e){return setTimeout(e,1e3)}));case 4:c={id:String(r.length+1),name:t.name,description:t.description,status:"active",createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),owner:"Current User",team:["Current User"],tags:t.tags||[],template:t.template},n([].concat((0,a.A)(r),[c])),G(!1),ce.resetFields(),E.Ay.success("Project created successfully"),oe("/app-builder?project=".concat(c.id)),e.next=16;break;case 12:e.prev=12,e.t0=e.catch(1),console.error("Error creating project:",e.t0),E.Ay.error("Failed to create project");case 16:return e.prev=16,Ee.mark("create-project-end"),Ee.measure("create-project","create-project-start","create-project-end"),e.finish(16);case 20:case"end":return e.stop()}}),e,null,[[1,12,16,20]])})));return function(t){return e.apply(this,arguments)}}(),ve=function(){var e=(0,c.A)(u().mark((function e(t){return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return Ee.mark("delete-project-start"),e.prev=1,e.next=4,new Promise((function(e){return setTimeout(e,1e3)}));case 4:n(r.filter((function(e){return e.id!==t}))),E.Ay.success("Project deleted successfully"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(1),console.error("Error deleting project:",e.t0),E.Ay.error("Failed to delete project");case 12:return e.prev=12,Ee.mark("delete-project-end"),Ee.measure("delete-project","delete-project-start","delete-project-end"),e.finish(12);case 16:case"end":return e.stop()}}),e,null,[[1,8,12,16]])})));return function(t){return e.apply(this,arguments)}}(),we=function(){var e=(0,c.A)(u().mark((function e(t){var c;return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return Ee.mark("duplicate-project-start"),e.prev=1,e.next=4,new Promise((function(e){return setTimeout(e,1e3)}));case 4:c=le(le({},t),{},{id:String(r.length+1),name:"".concat(t.name," (Copy)"),createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()}),n([].concat((0,a.A)(r),[c])),E.Ay.success("Project duplicated successfully"),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(1),console.error("Error duplicating project:",e.t0),E.Ay.error("Failed to duplicate project");case 13:return e.prev=13,Ee.mark("duplicate-project-end"),Ee.measure("duplicate-project","duplicate-project-start","duplicate-project-end"),e.finish(13);case 17:case"end":return e.stop()}}),e,null,[[1,9,13,17]])})));return function(t){return e.apply(this,arguments)}}(),Ae=r.filter((function(e){var t=U.toLowerCase();return e.name.toLowerCase().includes(t)||e.description.toLowerCase().includes(t)||e.owner.toLowerCase().includes(t)||e.tags.some((function(e){return e.toLowerCase().includes(t)}))})),ye=[{title:"Name",dataIndex:"name",key:"name",sorter:function(e,t){return e.name.localeCompare(t.name)}},{title:"Description",dataIndex:"description",key:"description",ellipsis:!0},{title:"Status",dataIndex:"status",key:"status",render:function(e){return s.createElement(g.A,{color:"active"===e?"green":"completed"===e?"blue":"default"},e.toUpperCase())},filters:[{text:"Active",value:"active"},{text:"Completed",value:"completed"},{text:"Archived",value:"archived"}],onFilter:function(e,t){return t.status===e}},{title:"Created",dataIndex:"createdAt",key:"createdAt",render:function(e){return new Date(e).toLocaleDateString()},sorter:function(e,t){return new Date(e.createdAt).getTime()-new Date(t.createdAt).getTime()}},{title:"Owner",dataIndex:"owner",key:"owner"},{title:"Tags",dataIndex:"tags",key:"tags",render:function(e){return s.createElement(s.Fragment,null,e.map((function(e){return s.createElement(g.A,{key:e},e)})))}},{title:"Actions",key:"actions",render:function(e,t){return s.createElement(v.A,{size:"small"},s.createElement(w.A,{title:"View"},s.createElement(A.Ay,{icon:s.createElement(C.A,null),size:"small",onClick:function(){return ne(t),void X(!0)}})),s.createElement(w.A,{title:"Edit"},s.createElement(A.Ay,{icon:s.createElement(x.A,null),size:"small",onClick:function(){oe("/app-builder?project=".concat(t.id))}})),s.createElement(w.A,{title:"Export"},s.createElement(A.Ay,{icon:s.createElement(D.A,null),size:"small",onClick:function(){return e=t,void E.Ay.info("Exporting project: ".concat(e.name));var e}})),s.createElement(w.A,{title:"Duplicate"},s.createElement(A.Ay,{icon:s.createElement(I.A,null),size:"small",onClick:function(){return we(t)}})),s.createElement(w.A,{title:"Delete"},s.createElement(y.A,{title:"Are you sure you want to delete this project?",onConfirm:function(){return ve(t.id)},okText:"Yes",cancelText:"No"},s.createElement(A.Ay,{icon:s.createElement(L.A,null),size:"small",danger:!0}))))}}];return s.createElement(pe,null,s.createElement(de,null,s.createElement(ie,{level:2},"Projects"),s.createElement(fe,null,s.createElement(h.A,{placeholder:"Search projects",prefix:s.createElement(R.A,null),value:U,onChange:function(e){return W(e.target.value)},style:{width:250}}),s.createElement(A.Ay,{type:"primary",icon:s.createElement(M.A,null),onClick:function(){return G(!0)}},"New Project"))),s.createElement(b.A,null,m?s.createElement(k.A,{active:!0,paragraph:{rows:10}}):s.createElement(j.A,{dataSource:Ae,columns:ye,rowKey:"id",pagination:{pageSize:10},locale:{emptyText:s.createElement(S.A,{description:"No projects found",image:S.A.PRESENTED_IMAGE_SIMPLE})}})),s.createElement(O.A,{title:"Create New Project",visible:_,onCancel:function(){return G(!1)},footer:null},s.createElement(f.A,{form:ce,layout:"vertical",onFinish:ge},s.createElement(f.A.Item,{name:"name",label:"Project Name",rules:[{required:!0,message:"Please enter a project name"}]},s.createElement(h.A,{placeholder:"Enter project name"})),s.createElement(f.A.Item,{name:"description",label:"Description"},s.createElement(h.A.TextArea,{placeholder:"Enter project description",rows:4})),s.createElement(f.A.Item,{name:"template",label:"Template",rules:[{required:!0,message:"Please select a template"}]},s.createElement(p.A,{placeholder:"Select a template"},s.createElement(se,{value:"blank"},"Blank"),s.createElement(se,{value:"dashboard"},"Dashboard"),s.createElement(se,{value:"blog"},"Blog"),s.createElement(se,{value:"ecommerce"},"E-commerce"),s.createElement(se,{value:"landing"},"Landing Page"),s.createElement(se,{value:"admin"},"Admin Panel"),s.createElement(se,{value:"mobile"},"Mobile App"))),s.createElement(f.A.Item,{name:"tags",label:"Tags"},s.createElement(p.A,{mode:"tags",placeholder:"Add tags",style:{width:"100%"}})),s.createElement(f.A.Item,null,s.createElement(A.Ay,{type:"primary",htmlType:"submit"},"Create Project")))),s.createElement(P.A,{title:null==te?void 0:te.name,width:600,placement:"right",onClose:function(){return X(!1)},visible:Q},te&&s.createElement(d.A,{defaultActiveKey:"details"},s.createElement(me,{tab:s.createElement("span",null,s.createElement(C.A,null)," Details"),key:"details"},s.createElement("div",null,s.createElement(ie,{level:4},"Description"),s.createElement(ue,null,te.description),s.createElement(T.A,null),s.createElement(ie,{level:4},"Status"),s.createElement(g.A,{color:"active"===te.status?"green":"completed"===te.status?"blue":"default"},te.status.toUpperCase()),s.createElement(T.A,null),s.createElement(ie,{level:4},"Created"),s.createElement(ue,null,new Date(te.createdAt).toLocaleString()),s.createElement(T.A,null),s.createElement(ie,{level:4},"Last Updated"),s.createElement(ue,null,new Date(te.updatedAt).toLocaleString()),s.createElement(T.A,null),s.createElement(ie,{level:4},"Tags"),te.tags.map((function(e){return s.createElement(g.A,{key:e},e)})),s.createElement(T.A,null),s.createElement(ie,{level:4},"Template"),s.createElement(ue,null,te.template))),s.createElement(me,{tab:s.createElement("span",null,s.createElement(z.A,null)," Team"),key:"team"},s.createElement("div",null,s.createElement(ie,{level:4},"Owner"),s.createElement(ue,null,te.owner),s.createElement(T.A,null),s.createElement(ie,{level:4},"Team Members"),s.createElement("ul",null,te.team.map((function(e){return s.createElement("li",{key:e},e)}))))),s.createElement(me,{tab:s.createElement("span",null,s.createElement(J.A,null)," History"),key:"history"},s.createElement(S.A,{description:"No history available"})),s.createElement(me,{tab:s.createElement("span",null,s.createElement(N.A,null)," Settings"),key:"settings"},s.createElement(S.A,{description:"No settings available"})))))}}}]);