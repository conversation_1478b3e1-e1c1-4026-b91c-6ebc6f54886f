/**
 * Real-time Collaboration Service
 * 
 * Handles real-time collaborative features for the App Builder
 */

import EnhancedWebSocketClient from './EnhancedWebSocketClient';
import { getWebSocketUrl } from '../utils/websocket';

class CollaborationService {
  constructor() {
    this.wsClient = null;
    this.isConnected = false;
    this.currentDocument = null;
    this.collaborators = new Map();
    this.eventListeners = new Map();
    this.operationQueue = [];
    this.isProcessingOperations = false;
    this.debug = process.env.NODE_ENV === 'development';
  }

  /**
   * Initialize collaboration for a document
   * @param {string} documentId - The document/app ID
   * @param {Object} user - Current user information
   */
  async initializeCollaboration(documentId, user) {
    try {
      this.currentDocument = documentId;
      
      // Create WebSocket connection for collaboration
      this.wsClient = new EnhancedWebSocketClient({
        url: getWebSocketUrl('collaboration'),
        autoConnect: true,
        autoReconnect: true,
        debug: this.debug,
        heartbeatInterval: 30000
      });

      // Set up event listeners
      this._setupEventListeners();

      // Join the document collaboration room
      await this._joinDocument(documentId, user);

      this.isConnected = true;
      this._log('Collaboration initialized for document:', documentId);

    } catch (error) {
      this._error('Failed to initialize collaboration:', error);
      throw error;
    }
  }

  /**
   * Join a document collaboration room
   * @param {string} documentId - Document ID
   * @param {Object} user - User information
   */
  async _joinDocument(documentId, user) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Join document timeout'));
      }, 5000);

      // Listen for join confirmation
      const handleJoinResponse = (data) => {
        if (data.type === 'document_joined' && data.documentId === documentId) {
          clearTimeout(timeout);
          this.removeEventListener('message', handleJoinResponse);
          this.collaborators = new Map(data.collaborators || []);
          resolve(data);
        }
      };

      this.addEventListener('message', handleJoinResponse);

      // Send join request
      this.wsClient.send({
        type: 'join_document',
        documentId,
        user: {
          id: user.id,
          username: user.username,
          avatar: user.avatar || null
        }
      });
    });
  }

  /**
   * Leave the current document collaboration
   */
  async leaveDocument() {
    if (this.currentDocument && this.wsClient) {
      this.wsClient.send({
        type: 'leave_document',
        documentId: this.currentDocument
      });
    }

    this.currentDocument = null;
    this.collaborators.clear();
    this.operationQueue = [];
  }

  /**
   * Send a real-time operation (like text changes, component updates)
   * @param {Object} operation - The operation to send
   */
  sendOperation(operation) {
    if (!this.isConnected || !this.currentDocument) {
      this._log('Cannot send operation: not connected or no document');
      return;
    }

    const operationData = {
      type: 'operation',
      documentId: this.currentDocument,
      operation: {
        ...operation,
        timestamp: Date.now(),
        id: this._generateOperationId()
      }
    };

    this.wsClient.send(operationData);
    this._log('Operation sent:', operationData);
  }

  /**
   * Send cursor position update
   * @param {Object} cursor - Cursor position data
   */
  sendCursorUpdate(cursor) {
    if (!this.isConnected || !this.currentDocument) return;

    this.wsClient.send({
      type: 'cursor_update',
      documentId: this.currentDocument,
      cursor: {
        ...cursor,
        timestamp: Date.now()
      }
    });
  }

  /**
   * Send user presence update
   * @param {Object} presence - Presence data
   */
  sendPresenceUpdate(presence) {
    if (!this.isConnected || !this.currentDocument) return;

    this.wsClient.send({
      type: 'presence_update',
      documentId: this.currentDocument,
      presence: {
        ...presence,
        timestamp: Date.now()
      }
    });
  }

  /**
   * Get list of current collaborators
   * @returns {Array} List of collaborators
   */
  getCollaborators() {
    return Array.from(this.collaborators.values());
  }

  /**
   * Add event listener
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  addEventListener(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event).add(callback);
  }

  /**
   * Remove event listener
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  removeEventListener(event, callback) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).delete(callback);
    }
  }

  /**
   * Emit event to listeners
   * @param {string} event - Event name
   * @param {*} data - Event data
   */
  _emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          this._error('Error in event listener:', error);
        }
      });
    }
  }

  /**
   * Set up WebSocket event listeners
   */
  _setupEventListeners() {
    this.wsClient.addEventListener('message', (data) => {
      this._handleMessage(data);
    });

    this.wsClient.addEventListener('open', () => {
      this._log('Collaboration WebSocket connected');
      this._emit('connected');
    });

    this.wsClient.addEventListener('close', () => {
      this._log('Collaboration WebSocket disconnected');
      this.isConnected = false;
      this._emit('disconnected');
    });

    this.wsClient.addEventListener('error', (error) => {
      this._error('Collaboration WebSocket error:', error);
      this._emit('error', error);
    });
  }

  /**
   * Handle incoming WebSocket messages
   * @param {Object} data - Message data
   */
  _handleMessage(data) {
    this._log('Received message:', data);

    switch (data.type) {
      case 'operation':
        this._handleOperation(data.operation);
        break;
      
      case 'cursor_update':
        this._handleCursorUpdate(data.cursor, data.userId);
        break;
      
      case 'presence_update':
        this._handlePresenceUpdate(data.presence, data.userId);
        break;
      
      case 'collaborator_joined':
        this._handleCollaboratorJoined(data.collaborator);
        break;
      
      case 'collaborator_left':
        this._handleCollaboratorLeft(data.userId);
        break;
      
      case 'document_joined':
        this._handleDocumentJoined(data);
        break;
      
      default:
        this._log('Unknown message type:', data.type);
    }

    // Emit generic message event
    this._emit('message', data);
  }

  /**
   * Handle incoming operations
   * @param {Object} operation - Operation data
   */
  _handleOperation(operation) {
    this.operationQueue.push(operation);
    this._processOperationQueue();
    this._emit('operation', operation);
  }

  /**
   * Handle cursor updates
   * @param {Object} cursor - Cursor data
   * @param {string} userId - User ID
   */
  _handleCursorUpdate(cursor, userId) {
    this._emit('cursor_update', { cursor, userId });
  }

  /**
   * Handle presence updates
   * @param {Object} presence - Presence data
   * @param {string} userId - User ID
   */
  _handlePresenceUpdate(presence, userId) {
    if (this.collaborators.has(userId)) {
      const collaborator = this.collaborators.get(userId);
      collaborator.presence = presence;
      this.collaborators.set(userId, collaborator);
    }
    this._emit('presence_update', { presence, userId });
  }

  /**
   * Handle collaborator joined
   * @param {Object} collaborator - Collaborator data
   */
  _handleCollaboratorJoined(collaborator) {
    this.collaborators.set(collaborator.id, collaborator);
    this._emit('collaborator_joined', collaborator);
  }

  /**
   * Handle collaborator left
   * @param {string} userId - User ID
   */
  _handleCollaboratorLeft(userId) {
    const collaborator = this.collaborators.get(userId);
    this.collaborators.delete(userId);
    this._emit('collaborator_left', { userId, collaborator });
  }

  /**
   * Handle document joined confirmation
   * @param {Object} data - Join data
   */
  _handleDocumentJoined(data) {
    this._log('Successfully joined document:', data.documentId);
    this._emit('document_joined', data);
  }

  /**
   * Process operation queue
   */
  async _processOperationQueue() {
    if (this.isProcessingOperations || this.operationQueue.length === 0) {
      return;
    }

    this.isProcessingOperations = true;

    try {
      while (this.operationQueue.length > 0) {
        const operation = this.operationQueue.shift();
        // Process operation (transform, apply, etc.)
        await this._processOperation(operation);
      }
    } catch (error) {
      this._error('Error processing operations:', error);
    } finally {
      this.isProcessingOperations = false;
    }
  }

  /**
   * Process a single operation
   * @param {Object} operation - Operation to process
   */
  async _processOperation(operation) {
    // This would contain the operational transformation logic
    // For now, just emit the operation
    this._emit('operation_processed', operation);
  }

  /**
   * Generate unique operation ID
   * @returns {string} Operation ID
   */
  _generateOperationId() {
    return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Disconnect from collaboration
   */
  disconnect() {
    if (this.wsClient) {
      this.wsClient.disconnect();
      this.wsClient = null;
    }
    
    this.isConnected = false;
    this.currentDocument = null;
    this.collaborators.clear();
    this.operationQueue = [];
    this.eventListeners.clear();
  }

  /**
   * Log debug message
   * @param {...any} args - Arguments to log
   */
  _log(...args) {
    if (this.debug) {
      console.log('[CollaborationService]', ...args);
    }
  }

  /**
   * Log error message
   * @param {...any} args - Arguments to log
   */
  _error(...args) {
    console.error('[CollaborationService]', ...args);
  }
}

// Export singleton instance
export default new CollaborationService();
