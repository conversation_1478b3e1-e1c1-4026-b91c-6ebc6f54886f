"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[403],{2403:(e,t,n)=>{n.r(t),n.d(t,{default:()=>q});var r,a,l,o,i,c,u,d,s,m,p=n(436),y=n(4467),g=n(5544),f=n(7528),A=n(6540),v=n(1468),h=n(9748),E=n(3598),b=n(7852),x=n(3903),S=n(9237),w=n(1143),C=n(359),D=n(2454),k=n(7046),I=n(261),O=n(234),j=n(1616),L=n(3587),z=n(6020);function Z(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function R(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Z(Object(n),!0).forEach((function(t){(0,y.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Z(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var P=L.I4.div(r||(r=(0,f.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),z.Ay.spacing[4]),M=L.I4.div(a||(a=(0,f.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: ",";\n"])),z.Ay.spacing[4]),W=L.I4.div(l||(l=(0,f.A)(["\n  display: grid;\n  grid-template-columns: repeat(12, 1fr);\n  grid-template-rows: repeat(12, 40px);\n  gap: ",";\n  background-color: ",";\n  border: 1px dashed ",";\n  border-radius: ",";\n  padding: ",";\n  min-height: 500px;\n"])),z.Ay.spacing[2],z.Ay.colors.neutral[100],z.Ay.colors.neutral[300],z.Ay.borderRadius.md,z.Ay.spacing[4]),T=L.I4.div(o||(o=(0,f.A)(["\n  grid-column: span ",";\n  grid-row: span ",";\n  background-color: ",";\n  border: 1px solid ",";\n  border-radius: ",";\n  padding: ",";\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  cursor: ",";\n  opacity: ",";\n  box-shadow: ",";\n"])),(function(e){return e.width||3}),(function(e){return e.height||2}),(function(e){return e.isPlaceholder?z.Ay.colors.primary.light:"white"}),(function(e){return e.isSelected?z.Ay.colors.primary.main:z.Ay.colors.neutral[300]}),z.Ay.borderRadius.md,z.Ay.spacing[2],(function(e){return e.isDragging?"grabbing":"grab"}),(function(e){return e.isDragging?.5:1}),(function(e){return e.isSelected?"0 0 0 2px ".concat(z.Ay.colors.primary.main):"none"})),$=L.I4.div(i||(i=(0,f.A)(["\n  display: flex;\n  flex-wrap: wrap;\n  gap: ",";\n  margin-bottom: ",";\n"])),z.Ay.spacing[2],z.Ay.spacing[4]),N=L.I4.div(c||(c=(0,f.A)(["\n  padding: ",";\n  background-color: white;\n  border: 1px solid ",";\n  border-radius: ",";\n  cursor: grab;\n  display: flex;\n  align-items: center;\n  gap: ",";\n\n  &:hover {\n    background-color: ",";\n  }\n"])),z.Ay.spacing[2],z.Ay.colors.neutral[300],z.Ay.borderRadius.md,z.Ay.spacing[2],z.Ay.colors.neutral[100]),H=L.I4.div(u||(u=(0,f.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),z.Ay.spacing[3]),Y=L.I4.div(d||(d=(0,f.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),z.Ay.spacing[2]),F=L.I4.div(s||(s=(0,f.A)(["\n  display: flex;\n  gap: ",";\n  margin-bottom: ",";\n"])),z.Ay.spacing[2],z.Ay.spacing[4]),X=L.I4.div(m||(m=(0,f.A)(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ",";\n  background-color: ",";\n  border-radius: ",";\n  text-align: center;\n"])),z.Ay.spacing[8],z.Ay.colors.neutral[100],z.Ay.borderRadius.md),B=function(e){var t=e.component,n=(e.index,e.onSelect),r=e.isSelected,a=e.onRemove,l=e.onDragStart,o=e.onDragEnd,i=(0,A.useState)(!1),c=(0,g.A)(i,2),u=c[0],d=c[1],s=(0,A.useState)({x:t.x||0,y:t.y||0}),m=(0,g.A)(s,2),p=m[0],y=m[1];return A.createElement(T,{isDragging:u,isSelected:r,width:t.width,height:t.height,style:{gridColumn:"".concat(p.x+1," / span ").concat(t.width),gridRow:"".concat(p.y+1," / span ").concat(t.height),cursor:u?"grabbing":"grab",position:"relative",zIndex:u?10:1},onClick:function(e){e.stopPropagation(),n(t)},onMouseDown:function(e){d(!0),l&&l(t);var n=e.clientX,r=e.clientY,a=p.x,i=p.y,c=function(e){var t=e.clientX-n,l=e.clientY-r,o=Math.max(0,a+Math.round(t/50)),c=Math.max(0,i+Math.round(l/40));y({x:o,y:c})},u=function(){d(!1),o&&o(t,p),document.removeEventListener("mousemove",c),document.removeEventListener("mouseup",u)};document.addEventListener("mousemove",c),document.addEventListener("mouseup",u)}},A.createElement("div",null,A.createElement("div",{style:{fontWeight:z.Ay.typography.fontWeight.semibold}},t.name),A.createElement("div",{style:{fontSize:z.Ay.typography.fontSize.sm,color:z.Ay.colors.neutral[500]}},t.type)),A.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},A.createElement("div",{style:{fontSize:z.Ay.typography.fontSize.sm,color:z.Ay.colors.neutral[500]}},t.width,"x",t.height),A.createElement("div",{style:{display:"flex",gap:"4px"}},A.createElement(h.A,{style:{cursor:"grab"}}),A.createElement(E.A,{style:{cursor:"pointer",color:z.Ay.colors.danger.main},onClick:function(e){e.stopPropagation(),a&&a(t.id)}}))))},J=function(e){var t=e.onDrop,n=e.children,r=(0,A.useState)(!1),a=(0,g.A)(r,2),l=a[0],o=a[1],i=A.useRef(null);return A.createElement("div",{ref:i,style:{position:"relative",height:"100%"},onDragOver:function(e){e.preventDefault(),o(!0)},onDragLeave:function(){o(!1)},onDrop:function(e){e.preventDefault(),o(!1);var n=i.current.getBoundingClientRect(),r=e.clientX-n.left,a=e.clientY-n.top,l=Math.floor(r/50),c=Math.floor(a/40);if(t)try{var u=JSON.parse(e.dataTransfer.getData("application/json"));t(u,{x:l,y:c})}catch(e){console.error("Error parsing drag data:",e)}},onClick:function(){}},n,l&&A.createElement("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(37, 99, 235, 0.1)",border:"2px dashed ".concat(z.Ay.colors.primary.main),borderRadius:z.Ay.borderRadius.md,zIndex:10}}))};const q=function(){var e=(0,v.wA)(),t=(0,v.d4)((function(e){return e.components||[]})),n=(0,v.d4)((function(e){return e.layouts||[]})),r=(0,A.useState)(""),a=(0,g.A)(r,2),l=a[0],o=a[1],i=(0,A.useState)("grid"),c=(0,g.A)(i,2),u=c[0],d=c[1],s=(0,A.useState)([]),m=(0,g.A)(s,2),y=m[0],f=m[1],Z=(0,A.useState)(null),T=(0,g.A)(Z,2),q=T[0],G=T[1],U=(0,A.useState)(null),K=(0,g.A)(U,2),Q=K[0],V=K[1],_=(0,A.useState)(!1),ee=(0,g.A)(_,2),te=ee[0],ne=ee[1],re=(0,A.useState)({}),ae=(0,g.A)(re,2),le=ae[0],oe=ae[1],ie=(0,A.useState)(3),ce=(0,g.A)(ie,2),ue=ce[0],de=ce[1],se=(0,A.useState)(2),me=(0,g.A)(se,2),pe=me[0],ye=me[1],ge=function(){var e={};return l.trim()||(e.name="Layout name is required"),oe(e),0===Object.keys(e).length},fe=function(e){G(e),o(e.name),d(e.type),f(e.items||[]),ne(!0),oe({})},Ae=function(e){f(y.filter((function(t){return t.id!==e}))),Q&&Q.id===e&&V(null)};return A.createElement(P,null,A.createElement(L.Zp,null,A.createElement(L.Zp.Header,null,A.createElement(L.Zp.Title,null,te?"Edit Layout":"Create Layout"),te&&A.createElement(L.$n,{variant:"text",size:"small",onClick:function(){o(""),d("grid"),f([]),G(null),ne(!1),oe({})},startIcon:A.createElement(b.A,null)},"Cancel")),A.createElement(L.Zp.Content,null,A.createElement(H,null,A.createElement(Y,null,A.createElement(L.pd,{label:"Layout Name",value:l,onChange:function(e){return o(e.target.value)},placeholder:"Enter layout name",fullWidth:!0,error:!!le.name,helperText:le.name})),A.createElement(Y,null,A.createElement(L.l6,{label:"Layout Type",value:u,onChange:function(e){return d(e.target.value)},options:[{value:"grid",label:"Grid Layout"},{value:"flex",label:"Flex Layout"},{value:"custom",label:"Custom Layout"}],fullWidth:!0})))),A.createElement(L.Zp.Footer,null,te?A.createElement(L.$n,{variant:"primary",onClick:function(){if(q&&ge()){var t=R(R({},q),{},{name:l.trim(),type:u,items:y,updatedAt:(new Date).toISOString()});e((0,j.mR)(t)),o(""),d("grid"),f([]),G(null),ne(!1),oe({})}},startIcon:A.createElement(x.A,null)},"Update Layout"):A.createElement(L.$n,{variant:"primary",onClick:function(){if(ge()){var t={id:Date.now().toString(),name:l.trim(),type:u,items:y,createdAt:(new Date).toISOString()};e((0,j.S7)(t)),o(""),d("grid"),f([]),oe({})}},startIcon:A.createElement(S.A,null)},"Add Layout"))),A.createElement(L.Zp,null,A.createElement(L.Zp.Header,null,A.createElement(L.Zp.Title,null,"Layout Designer")),A.createElement(L.Zp.Content,null,A.createElement($,null,A.createElement("div",{style:{marginRight:z.Ay.spacing[4],fontWeight:z.Ay.typography.fontWeight.medium}},"Component Palette:"),t.map((function(e){return A.createElement(N,{key:e.id,draggable:!0,onDragStart:function(t){t.dataTransfer.setData("application/json",JSON.stringify({id:e.id,name:e.name,type:e.type})),t.dataTransfer.effectAllowed="copy"}},A.createElement(h.A,null),A.createElement("span",null,e.name))})),0===t.length&&A.createElement("div",{style:{color:z.Ay.colors.neutral[500]}},"No components available. Create components first.")),A.createElement(F,null,A.createElement("div",{style:{display:"flex",alignItems:"center",gap:z.Ay.spacing[2]}},A.createElement(w.A,null),A.createElement(L.pd,{label:"Width",type:"number",min:1,max:12,value:ue,onChange:function(e){return de(parseInt(e.target.value,10))},style:{width:"80px"}})),A.createElement("div",{style:{display:"flex",alignItems:"center",gap:z.Ay.spacing[2]}},A.createElement(C.A,null),A.createElement(L.pd,{label:"Height",type:"number",min:1,max:12,value:pe,onChange:function(e){return ye(parseInt(e.target.value,10))},style:{width:"80px"}})),Q&&A.createElement(L.$n,{variant:"primary",size:"small",onClick:function(){if(Q){var e=y.map((function(e){return e.id===Q.id?R(R({},e),{},{width:ue,height:pe}):e}));f(e),V(null)}}},"Apply")),A.createElement(J,{onDrop:function(e,n){var r=t.find((function(t){return t.id===e.id}));if(r){var a=Math.floor(10*Math.random())+1,l=Math.floor(10*Math.random())+1,o={id:Date.now().toString(),componentId:r.id,name:r.name,type:r.type,x:a,y:l,width:ue,height:pe};f([].concat((0,p.A)(y),[o]))}}},A.createElement(W,null,y.map((function(e,t){return A.createElement(B,{key:e.id,component:e,index:t,onSelect:function(){return function(e){V(e),de(e.width),ye(e.height)}(e)},isSelected:Q&&Q.id===e.id,onRemove:Ae,onDragEnd:function(e,t){var n=y.map((function(n){return n.id===e.id?R(R({},n),{},{x:t.x,y:t.y}):n}));f(n)}})})))))),A.createElement(L.Zp,null,A.createElement(L.Zp.Header,null,A.createElement(L.Zp.Title,null,"Saved Layouts")),A.createElement(L.Zp.Content,null,0===n.length?A.createElement(X,null,A.createElement("div",{style:{fontSize:"48px",color:z.Ay.colors.neutral[400],marginBottom:z.Ay.spacing[4]}},A.createElement(D.A,null)),A.createElement("h3",null,"No Layouts Yet"),A.createElement("p",null,"Create your first layout to get started")):A.createElement(M,null,n.map((function(t){var n;return A.createElement(L.Zp,{key:t.id,elevation:"sm"},A.createElement(L.Zp.Header,null,A.createElement("div",null,A.createElement("div",{style:{fontWeight:z.Ay.typography.fontWeight.semibold}},t.name),A.createElement("div",{style:{fontSize:z.Ay.typography.fontSize.sm,color:z.Ay.colors.neutral[500]}},t.type)),A.createElement("div",{style:{display:"flex",gap:z.Ay.spacing[1]}},A.createElement(L.$n,{variant:"text",size:"small",onClick:function(){return function(t){var n=R(R({},t),{},{id:Date.now().toString(),name:"".concat(t.name," (Copy)"),createdAt:(new Date).toISOString()});e((0,j.S7)(n))}(t)}},A.createElement(k.A,null)),A.createElement(L.$n,{variant:"text",size:"small",onClick:function(){return fe(t)}},A.createElement(I.A,null)),A.createElement(L.$n,{variant:"text",size:"small",onClick:function(){return n=t.id,e((0,j.N1)(n)),void(q&&q.id===n&&(o(""),d("grid"),f([]),G(null),ne(!1)));var n}},A.createElement(E.A,null)))),A.createElement(L.Zp.Content,{onClick:function(){return fe(t)}},A.createElement("div",{style:{height:"150px",backgroundColor:z.Ay.colors.neutral[100],borderRadius:z.Ay.borderRadius.md,display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer"}},A.createElement(O.A,{style:{fontSize:"24px",color:z.Ay.colors.neutral[400]}}))),A.createElement(L.Zp.Footer,null,A.createElement("div",{style:{fontSize:z.Ay.typography.fontSize.sm,color:z.Ay.colors.neutral[500]}},(null===(n=t.items)||void 0===n?void 0:n.length)||0," components")))}))))))}}}]);