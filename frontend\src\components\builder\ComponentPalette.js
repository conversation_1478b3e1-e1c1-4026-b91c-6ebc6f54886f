import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Di<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Card, message } from 'antd';
import {
  FormOutlined,
  TableOutlined,
  BarChartOutlined,
  PictureOutlined,
  FontSizeOutlined,
  AppstoreOutlined,
  OrderedListOutlined,
  CheckSquareOutlined,
  CalendarOutlined,
  SlidersFilled,
  TagsOutlined,
  FileTextOutlined,
  CreditCardOutlined,
  BarsOutlined,
  LayoutOutlined,
  BookOutlined,
  StarOutlined
} from '@ant-design/icons';
import axios from 'axios';

const { Title, Text } = Typography;

const ComponentPalette = ({ onAddComponent }) => {
  const [componentTemplates, setComponentTemplates] = useState([]);
  const [templatesLoading, setTemplatesLoading] = useState(false);

  // Fetch component templates
  useEffect(() => {
    fetchComponentTemplates();
  }, []);

  const fetchComponentTemplates = async () => {
    setTemplatesLoading(true);
    try {
      const response = await axios.get('/api/component-templates/?is_public=true');
      setComponentTemplates(response.data.results || response.data);
    } catch (error) {
      console.error('Error fetching component templates:', error);
    } finally {
      setTemplatesLoading(false);
    }
  };

  const handleUseTemplate = async (template) => {
    try {
      // Parse the template's default props
      let defaultProps = {};
      if (template.default_props) {
        if (typeof template.default_props === 'string') {
          defaultProps = JSON.parse(template.default_props);
        } else {
          defaultProps = template.default_props;
        }
      }

      // Add component with template props
      onAddComponent(template.component_type, defaultProps);
      message.success(`Added ${template.name} template`);
    } catch (error) {
      console.error('Error using template:', error);
      message.error('Failed to use template');
    }
  };

  const componentGroups = [
    {
      title: 'Layout',
      components: [
        { type: 'header', icon: <FontSizeOutlined />, label: 'Header' },
        { type: 'section', icon: <LayoutOutlined />, label: 'Section' },
        { type: 'card', icon: <CreditCardOutlined />, label: 'Card' },
        { type: 'tabs', icon: <BarsOutlined />, label: 'Tabs' },
        { type: 'divider', icon: <BarsOutlined />, label: 'Divider' },
      ]
    },
    {
      title: 'Basic Components',
      components: [
        { type: 'text', icon: <FileTextOutlined />, label: 'Text' },
        { type: 'button', icon: <AppstoreOutlined />, label: 'Button' },
        { type: 'image', icon: <PictureOutlined />, label: 'Image' },
        { type: 'list', icon: <OrderedListOutlined />, label: 'List' },
        { type: 'tag', icon: <TagsOutlined />, label: 'Tag' },
      ]
    },
    {
      title: 'Form Components',
      components: [
        { type: 'form', icon: <FormOutlined />, label: 'Form' },
        { type: 'input', icon: <FormOutlined />, label: 'Input' },
        { type: 'select', icon: <FormOutlined />, label: 'Select' },
        { type: 'checkbox', icon: <CheckSquareOutlined />, label: 'Checkbox' },
        { type: 'datepicker', icon: <CalendarOutlined />, label: 'Date Picker' },
        { type: 'slider', icon: <SlidersFilled />, label: 'Slider' },
      ]
    },
    {
      title: 'Data Components',
      components: [
        { type: 'table', icon: <TableOutlined />, label: 'Table' },
        { type: 'chart', icon: <BarChartOutlined />, label: 'Chart' },
        { type: 'statistic', icon: <BarChartOutlined />, label: 'Statistic' },
      ]
    }
  ];

  return (
    <div>
      <Title level={5}>Components</Title>
      <Text type="secondary">Drag or click to add components to your app</Text>

      {/* Component Templates Section */}
      {componentTemplates.length > 0 && (
        <div style={{ marginTop: '16px' }}>
          <Divider orientation="left" plain>
            <BookOutlined /> Templates
          </Divider>
          <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
            {componentTemplates.slice(0, 6).map((template) => (
              <Card
                key={template.id}
                size="small"
                style={{ marginBottom: '8px', cursor: 'pointer' }}
                hoverable
                onClick={() => handleUseTemplate(template)}
              >
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div style={{ marginRight: '8px' }}>
                    <AppstoreOutlined />
                  </div>
                  <div style={{ flex: 1 }}>
                    <Text strong style={{ fontSize: '12px' }}>
                      {template.name}
                    </Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '11px' }}>
                      {template.component_type}
                    </Text>
                  </div>
                  {template.is_public && (
                    <StarOutlined style={{ color: '#faad14' }} />
                  )}
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {componentGroups.map((group) => (
        <div key={`component-group-${group.title}`} style={{ marginTop: '16px' }}>
          <Divider orientation="left" plain>{group.title}</Divider>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
            {group.components.map((component) => (
              <Tooltip key={`component-${group.title}-${component.type}`} title={component.label}>
                <Button
                  icon={component.icon}
                  onClick={() => onAddComponent(component.type)}
                  style={{
                    width: '70px',
                    height: '70px',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    padding: '4px'
                  }}
                >
                  <div style={{ marginTop: '4px', fontSize: '12px' }}>
                    {component.label}
                  </div>
                </Button>
              </Tooltip>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default ComponentPalette;
